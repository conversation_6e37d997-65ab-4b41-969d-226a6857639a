spring.application.name=MaTransactionBackEnd
server.address=0.0.0.0
server.port=8397
spring.webflux.base-path=/ma-service/api/v1/transactions


# spring.profiles.active=dev  

#Log config
LOG_PATH=/var/log/ma-transaction-backend
logging.level.org.apache.kafka=OFF
logging.level.kafka=OFF

#Export file config
export.file.base-dir=/opt/ma-service/export/

#opensearch-config
opensearch.host=************
opensearch.port=9200
opensearch.protocol=http
opensearch.index=/tb_0606/_search
opensearch.index.searchDocId=/tb_0606/_doc/

#permistion-config
permission.base-url:http://localhost:8482/permission/api/v1
permission.path.merchants-by-user=/merchants-by-user

# R2DBC configuration for PostgreSQL
spring.r2dbc.url=r2dbc:postgresql://db1514.onepay.vn:1514/ma?currentSchema=ma
spring.r2dbc.username=postgres
spring.r2dbc.password=4n
spring.r2dbc.pool.enabled=true
spring.r2dbc.pool.initial-size=5
spring.r2dbc.pool.max-size=20

# Disable standard JPA configuration, since you're using R2DBC
spring.jpa.hibernate.ddl-auto=none
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect

# Cron job sync transaction-report daily configuration
transaction-report.job.enabled=true
transaction-report.job.cron=0 00 16 * * *

# Cron job sync promotion-report daily configuration
promotion-report.job.enabled=true
promotion-report.job.cron=0 05 16 * * *

# Cron job sync upos-report daily configuration
upos-report.job.enabled=true
upos-report.job.cron=0 10 16 * * *

#kafka
spring.kafka.bootstrap-servers=localhost:9092
kafka.download-task.enabled=true

#noti url
ma-enventbus.url=http://localhost/ma-eventbus

# DB2 - extra
db2.r2dbc.url=r2dbc:postgresql://db1514.onepay.vn:5432/postgres?currentSchema=merchant_monitor_mm
db2.r2dbc.username=postgres
db2.r2dbc.password=4n

# configuration for Oracle
#onereport_1118
spring.datasource.oracle.onereport.url=*****************************************
spring.datasource.oracle.onereport.username=onereport
spring.datasource.oracle.onereport.password=onereport
spring.datasource.oracle.onereport.hikari.minimum-idle=5
spring.datasource.oracle.onereport.hikari.maximum-pool-size=20
spring.datasource.oracle.onereport.hikari.idle-timeout=30000
spring.datasource.oracle.onereport.hikari.connection-timeout=30000
spring.datasource.oracle.onereport.hikari.pool-name=OracleOneReportHikariPool

spring.datasource.oracle.driver-class-name=oracle.jdbc.OracleDriver
spring.datasource.oracle.type=com.zaxxer.hikari.HikariDataSource
#merchantportal_1114
spring.datasource.oracle.merchantportal.url=*****************************************
spring.datasource.oracle.merchantportal.username=merchantportal
spring.datasource.oracle.merchantportal.password=merchantportal
spring.datasource.oracle.merchantportal.hikari.minimum-idle=5
spring.datasource.oracle.merchantportal.hikari.maximum-pool-size=20
spring.datasource.oracle.merchantportal.hikari.idle-timeout=30000
spring.datasource.oracle.merchantportal.hikari.connection-timeout=30000
spring.datasource.oracle.merchantportal.hikari.pool-name=OracleMerchantPortalHikariPool

# day cutoff
bnpl.refund.kredivo_deadline=14
bnpl.refund.homecredit_deadline=22
bnpl.refund.fundiin_deadline=22

international.authorize.deadline=30
international.refund_purchase.deadline=365
international.capture.deadline=30
international.capture.start.deadline=7
international.capture.end.deadline=30
qr.refund.deadline=365

enable_sync_transaction_opensearch=false
#url international detail
international.url.base=http://localhost:8238/ma-international/api/v1
#url domestic detail
domestic.url.base=http://localhost:8237/ma-domestic/api/v1
#url qr detail
qr.url.base=http://localhost:8241/ma-qr/api/v1
#url vietqr detail
vietqr.url.base=http://localhost:8246/ma-vietqr/api/v1
#url bnpl detail
bnpl.url.base=http://localhost:8235/ma-bnpl/api/v1
#url direct debit detail
directdebit.url.base=http://localhost:8247/ma-direct-debit/api/v1
#url upos detail
upos.url.base=http://localhost:8236/ma-upos/api/v1
#url ma-service
ma-service.url.base=http://localhost:8180
#url opensearch service
opensearch.url.base=http://localhost:8089/opensearch-service/api/v1