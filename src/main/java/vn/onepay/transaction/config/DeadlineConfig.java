package vn.onepay.transaction.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class DeadlineConfig {
    
    @Value("${bnpl.refund.kredivo_deadline}")
    private int bnplRefundKredivoDeadline;
    
    @Value("${bnpl.refund.homecredit_deadline}")
    private int bnplRefundHomecreditDeadline;
    
    @Value("${bnpl.refund.fundiin_deadline}")
    private int bnplRefundFundiinDeadline;
    
    @Value("${international.authorize.deadline}")
    private int internationalAuthorizeDeadline;
    
    @Value("${international.refund_purchase.deadline}")
    private int internationalRefundPurchaseDeadline;
    
    @Value("${international.capture.deadline}")
    private int internationalCaptureDeadline;
    
    @Value("${international.capture.start.deadline}")
    private int internationalCaptureStartDeadline;
    
    @Value("${international.capture.end.deadline}")
    private int internationalCaptureEndDeadline;
    
    @Value("${qr.refund.deadline}")
    private int qrRefundDeadline;

    // Getters
    public int getBnplRefundKredivoDeadline() {
        return bnplRefundKredivoDeadline;
    }

    public int getBnplRefundHomecreditDeadline() {
        return bnplRefundHomecreditDeadline;
    }

    public int getBnplRefundFundiinDeadline() {
        return bnplRefundFundiinDeadline;
    }

    public int getInternationalAuthorizeDeadline() {
        return internationalAuthorizeDeadline;
    }

    public int getInternationalRefundPurchaseDeadline() {
        return internationalRefundPurchaseDeadline;
    }

    public int getInternationalCaptureDeadline() {
        return internationalCaptureDeadline;
    }

    public int getInternationalCaptureStartDeadline() {
        return internationalCaptureStartDeadline;
    }

    public int getInternationalCaptureEndDeadline() {
        return internationalCaptureEndDeadline;
    }

    public int getQrRefundDeadline() {
        return qrRefundDeadline;
    }
}
