package vn.onepay.transaction.util;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import vn.onepay.transaction.constant.IConstants;
import vn.onepay.transaction.dto.Acquirer;
import vn.onepay.transaction.dto.AcquirerDTO;
import vn.onepay.transaction.dto.TransactionDetailResponse;
import vn.onepay.transaction.dto.TransactionHistoryDTO;
import vn.onepay.transaction.dto.ActionMessage;
import vn.onepay.transaction.dto.MSPMerchant;

public class CheckPermissionUtil {
    private static final Logger logger = LoggerFactory.getLogger(CheckPermissionUtil.class);
    private static final String STATUS_SUCCESSFULL = "Successful";
    private static final String STATUS_PENDING = "Pending";
    private static final String MESSAGE_CODE_REFUND_DEADLINE = "REFUND_DEADLINE";
    private static final String MESSAGE_CODE_CAPTURE_DEADLINE = "CAPTURE_DEADLINE";
    private static final String MESSAGE_CODE_CAPTURE_DEADLINE_WARNING = "CAPTURE_DEADLINE_WARNING";
    private static final String MESSAGE_CODE_VOID_CAPTURE_DEADLINE = "VOID_CAPTURE_DEADLINE";
    private static final String MESSAGE_CODE_VOID_REFUND_PURCHASE_DEADLINE = "VOID_REFUND_PURCHASE_DEADLINE";
    private static final String MESSAGE_CODE_REFUND_CAPTURE_INSTALLMENT_STATUS_CREATE = "REFUND_CAPTURE_INSTALLMENT_STATUS_CREATE";
    private static final String MESSAGE_CODE_REFUND_CAPTURE_INSTALLMENT_STATUS_APPROVED = "REFUND_CAPTURE_INSTALLMENT_STATUS_APPROVED";
    private static final String MESSAGE_CODE_REFUND_CAPTURE_INSTALLMENT_STATUS_SENT = "REFUND_CAPTURE_INSTALLMENT_STATUS_SENT";
    private static final String MESSAGE_CODE_VOID_REFUND_CAPTURE_DEADLINE = "VOID_REFUND_CAPTURE_DEADLINE";
    private static final String MESSAGE_CODE_VOID_AUTHORIZE_DEADLINE = "VOID_AUTHORIZE_DEADLINE";

    private static final String MESSAGE_CODE_VOID_PURCHASE_DEADLINE = "VOID_PURCHASE_DEADLINE";
    private static final String MESSAGE_CODE_VOID_PURCHASE_INSTALLMENT_STATUS_CREATE = "MESSAGE_CODE_VOID_PURCHASE_INSTALLMENT_STATUS_CREATE";
    private static final String MESSAGE_CODE_REFUND_PURCHASE_INSTALLMENT_STATUS_CREATE = "REFUND_PURCHASE_INSTALLMENT_STATUS_CREATE";
    private static final String MESSAGE_CODE_REFUND_PURCHASE_INSTALLMENT_STATUS_APPROVED = "REFUND_PURCHASE_INSTALLMENT_STATUS_APPROVED";
    private static final String MESSAGE_CODE_REFUND_PURCHASE_INSTALLMENT_STATUS_SENT = "REFUND_PURCHASE_INSTALLMENT_STATUS_SENT";

    private static final String MERCHANT_ID_OP_SAMSUNG = "OP_SAMSUNG";
    private static final String PROVIDER_KREDIVO = "Kredivo";

    private static final String GATEWAY_MPGS = "MPGS";

    

    private static final DateTimeFormatter inputFormatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME;
    private static final DateTimeFormatter displayFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
    private static final DateTimeFormatter displayTimeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss dd/MM/yyyy");

    @Value ("${bnpl.refund.kredivo_deadline}")
    private int bnplRefundKredivoDeadline;
    @Value ("${bnpl.refund.homecredit_deadline}")
    private int bnplRefundHomecreditDeadline;
    @Value ("${bnpl.refund.fundiin_deadline}")
    private int bnplRefundFundiinDeadline;
    @Value ("${international.authorize.deadline}")
    private int internationalAuthorizeDeadline;
    @Value ("${international.refund_purchase.deadline}")
    private int internationalRefundPurchaseDeadline;
    @Value ("${international.capture.deadline}")
    private int internationalCaptureDeadline;
    @Value ("${international.capture.start.deadline}")
    private int internationalCaptureStartDeadline;
    @Value ("${international.capture.end.deadline}")
    private int internationalCaptureEndDeadline;
    @Value ("${qr.refund.deadline}")
    private int qrRefundDeadline;

    public static boolean checkPermission(List<String> userPermissions, String requiredPermission) {
        return userPermissions.contains(requiredPermission);
    }

    public static Map<String, ActionMessage> userPermissionDefault(){
        Map<String, ActionMessage> actions = new HashMap<>();
        
        actions.put(IConstants.ACTION_REFUND, new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", ""));
        actions.put(IConstants.ACTION_VOID, new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", ""));
        actions.put(IConstants.ACTION_CAPTURE, new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", ""));
        actions.put(IConstants.ACTION_APPROVE_REJECT, new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", ""));

        return actions;
    }

    public static ActionMessage checkPerActionCapture(String transactionId, BigDecimal amountCapture, List<TransactionHistoryDTO> lstTransactionHistory,TransactionDetailResponse detail, List<String> userPermissions) {
        Boolean capturePermission = checkPermission(userPermissions, IConstants.TRANSACTION_APP_TRANS_MGMT_CAPTURE);
        if (!capturePermission) {
            logger.info("checkActionCapture capturePermission invalid, transactionId={}", transactionId);
            return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
        }

        String transactionType = detail.getTransactionInformation().getTransactionType();
        switch (transactionType) {
        case IConstants.TRANSACTION_TYPE_AUTHORIZE:
            return checkActionCapture(transactionId, amountCapture, lstTransactionHistory, detail);
        default:
            logger.info("checkActionCapture transactionType invalid, transactionId={}, transactionType={}", transactionId, transactionType);
            return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
        }
    }

    public static ActionMessage checkPerActionRefund(String transactionId, BigDecimal amountRefund, List<TransactionHistoryDTO> lstTransactionHistory,TransactionDetailResponse detail, MSPMerchant merchant, List<String> userPermissions) {
        Boolean refundPermission = checkPermission(userPermissions, IConstants.TRANSACTION_APP_TRANS_MGMT_CREATE_REFUND);
        if (!refundPermission) {
            logger.info("checkActionRefund refundPermission invalid, transactionId={}", transactionId);
            return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
        }

        String transactionType = detail.getTransactionInformation().getTransactionType();
        switch (transactionType) {
        case IConstants.TRANSACTION_TYPE_PURCHASE, IConstants.TRANSACTION_TYPE_PURCHASE_BNPL:
            return checkActionRefundPurchase(transactionId, amountRefund, lstTransactionHistory, detail, merchant);
        case IConstants.TRANSACTION_TYPE_CAPTURE:
            return checkActionRefundCapture(transactionId, amountRefund, lstTransactionHistory, detail);
        default:
            logger.info("checkActionRefund paymentMethod invalid, transactionId={}, transactionType={}", transactionId, transactionType);
            return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
        }
    }

    public static ActionMessage checkPerActionVoid(String transactionId, AcquirerDTO acquirerDTO, List<TransactionHistoryDTO> lstTransactionHistory,TransactionDetailResponse detail, List<String> userPermissions) {
        Boolean voidPermission = checkPermission(userPermissions, IConstants.TRANSACTION_APP_TRANS_MGMT_VOID_TRANS);
        if (!voidPermission) {
            logger.info("checkActionVoid voidPermission invalid, transactionId={}", transactionId);
            return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
        }

        String transactionType = detail.getTransactionInformation().getTransactionType();
        switch (transactionType) {
        case IConstants.TRANSACTION_TYPE_PURCHASE:
            return checkActionVoidPurchase(transactionId, acquirerDTO, lstTransactionHistory, detail);
        case IConstants.TRANSACTION_TYPE_AUTHORIZE:
            return checkActionVoidAuthorize(transactionId, lstTransactionHistory, detail);
        case IConstants.TRANSACTION_TYPE_CAPTURE:
            return checkActionVoidCapture(transactionId, acquirerDTO, lstTransactionHistory, detail);
        case IConstants.TRANSACTION_TYPE_REFUND_PURCHASE:
            return checkActionVoidRefundPurchase(transactionId, acquirerDTO, lstTransactionHistory, detail);
        case IConstants.TRANSACTION_TYPE_REFUND_CAPTURE:
            return checkActionVoidRefundCapture(transactionId, acquirerDTO, lstTransactionHistory, detail);
        default:
            logger.info("checkActionVoid paymentMethod invalid, transactionId={}, transactionType={}", transactionId, transactionType);
            return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
        }
    }

    public static ActionMessage checkPerActionApproveReject(String transactionId,TransactionDetailResponse detail, List<String> userPermissions) {
        Boolean approveRejectPermission = checkPermission(userPermissions, IConstants.TRANSACTION_APP_TRANS_MGMT_APPROVE_REFUND);
        if (!approveRejectPermission) {
            logger.info("checkActionApproveReject approveRejectPermission invalid, transactionId={}", transactionId);
            return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
        }

        return checkActionApproveAndRejectRefund(transactionId, detail);
    }

    public static ActionMessage checkActionCapture(String transactionId, BigDecimal amountCapture, List<TransactionHistoryDTO> lstTransactionHistory,TransactionDetailResponse detail) {
        String paymentMethod = detail.getTransactionInformation().getPaymentMethod();
        switch (paymentMethod) {
        case IConstants.PAYMENT_METHOD_QT, IConstants.PAYMENT_METHOD_SS_PAY, IConstants.PAYMENT_METHOD_GG_PAY, IConstants.PAYMENT_METHOD_AP_PAY, IConstants.PAYMENT_METHOD_INSTALLMENT:
            return checkCaptureQT(transactionId, amountCapture, lstTransactionHistory, detail);
        default:
            logger.info("checkActionCapture paymentMethod invalid, transactionId={}, paymentMethod={}", transactionId, paymentMethod);
            return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
        }
    }

    private static ActionMessage checkCaptureQT(String transactionId, BigDecimal amountCapture, List<TransactionHistoryDTO> lstTransactionHistory,TransactionDetailResponse detail) {
        String transactionStatus = detail.getTransactionInformation().getTransactionStatus();
        if (transactionStatus == null || !transactionStatus.equalsIgnoreCase(STATUS_SUCCESSFULL)) {
            logger.info("checkCaptureQT transactionStatus invalid, transactionId={}, transactionStatus={}", transactionId, transactionStatus);
            return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
        }

        BigDecimal totalAmount = detail.getTransactionInformation().getTransactionAmount();
        BigDecimal totalCapture = BigDecimal.ZERO;
        BigDecimal totalVoidCapture = BigDecimal.ZERO;
        for (TransactionHistoryDTO transactionHistoryDTO : lstTransactionHistory) {
            if (transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_VOID_AUTHORIZE) && 
                transactionHistoryDTO.getStatus().equalsIgnoreCase(STATUS_SUCCESSFULL)) {
                logger.info("checkCaptureQT has void authorize, transactionId={}", transactionId);
                return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
            }

            if(transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_CAPTURE) && 
                transactionHistoryDTO.getStatus().equalsIgnoreCase(STATUS_SUCCESSFULL)) {
                totalCapture = totalCapture.add(transactionHistoryDTO.getAmount());
            }
            
            if (transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_VOID_CAPTURE) && 
                transactionHistoryDTO.getStatus().equalsIgnoreCase(STATUS_SUCCESSFULL)) {
                totalVoidCapture = totalVoidCapture.add(transactionHistoryDTO.getAmount());
            }
        }

        totalCapture = totalCapture.subtract(totalVoidCapture);
        totalCapture = totalCapture.add(amountCapture);

        if ((amountCapture == BigDecimal.ZERO && totalAmount.compareTo(totalCapture) <= 0) || (amountCapture.compareTo(BigDecimal.ZERO) > 0 && totalAmount.compareTo(totalCapture) < 0)) {
            logger.info("checkCaptureQT totalCapture > totalAmount, transactionId={}, totalCapture={}, totalAmount={}", transactionId, totalCapture, totalAmount);
            return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
        }

        // cardType is PayPal => return false
        if (detail.getPaymentMethod().getCardType().equalsIgnoreCase(IConstants.CARD_TYPE_PAYPAL)) {
            logger.info("checkCaptureQT cardType invalid, transactionId={}, cardType={}", transactionId, detail.getPaymentMethod().getCardType());
            return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
        }

        // check cutoff 30 days
        String checkDeadlineCapture = checkTimeDeadline(detail.getTransactionInformation().getTransactionCreatedTime(), internationalCaptureDeadline);
        if (!checkDeadlineCapture.isEmpty()) {
            logger.info("checkCaptureQT checkDeadlineCapture, transactionId={}, checkDeadlineCapture={}", transactionId, checkDeadlineCapture);
            return new ActionMessage(IConstants.ACTION_STATE_DISABLED, MESSAGE_CODE_CAPTURE_DEADLINE, checkDeadlineCapture);
        }

        String messageCode = "";
        // check in range 7 - 30 days
        Boolean isInRange = isInRange(detail.getTransactionInformation().getTransactionCreatedTime(), internationalCaptureStartDeadline, internationalCaptureEndDeadline);
        if(isInRange) {
            messageCode = MESSAGE_CODE_CAPTURE_DEADLINE_WARNING;
        }

        ActionMessage msg = new ActionMessage(IConstants.ACTION_STATE_VISIBLE, messageCode, "");
        msg.setRemainingAmount(totalAmount.subtract(totalCapture));

        return msg;
    }

    public static ActionMessage checkActionVoidCapture(String transactionId, AcquirerDTO acquirerDTO, List<TransactionHistoryDTO> lstTransactionHistory,TransactionDetailResponse detail) {
        String paymentMethod = detail.getTransactionInformation().getPaymentMethod();
        switch (paymentMethod) {
        case IConstants.PAYMENT_METHOD_QT, IConstants.PAYMENT_METHOD_SS_PAY, IConstants.PAYMENT_METHOD_GG_PAY, IConstants.PAYMENT_METHOD_AP_PAY, IConstants.PAYMENT_METHOD_INSTALLMENT:
            return checkVoidCaptureQT(transactionId, acquirerDTO, lstTransactionHistory, detail);
        default:
            logger.info("checkActionVoidCapture paymentMethod invalid, transactionId={}, paymentMethod={}", transactionId, paymentMethod);
            return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
        }
    }

    private static ActionMessage checkVoidCaptureQT(String transactionId, AcquirerDTO acquirerDTO, List<TransactionHistoryDTO> lstTransactionHistory,TransactionDetailResponse detail) {
        String transactionStatus = detail.getTransactionInformation().getTransactionStatus();
        if (transactionStatus == null || !transactionStatus.equalsIgnoreCase(STATUS_SUCCESSFULL)) {
            logger.info("checkVoidCaptureQT transactionStatus invalid, transactionId={}, transactionStatus={}", transactionId, transactionStatus);
            return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
        }

        String gateway = acquirerDTO.getList().stream()
        .filter(acquirer -> acquirer.getId() == Integer.parseInt(detail.getPaymentMethod().getAcquirer()))
        .map(Acquirer::getGateway)
        .findFirst()
        .orElse("");

        String lastTransactionId = "";
        for (TransactionHistoryDTO transactionHistoryDTO : lstTransactionHistory) {
            if (transactionId.equals(transactionHistoryDTO.getDadId()) && 
                transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_VOID_CAPTURE) && 
                transactionHistoryDTO.getStatus().equalsIgnoreCase(STATUS_SUCCESSFULL)) {
                logger.info("checkVoidCaptureQT has void capture, transactionId={}", transactionId);
                return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
            }

            if (gateway.equalsIgnoreCase(GATEWAY_MPGS) &&
                transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_CAPTURE) && 
                transactionHistoryDTO.getStatus().equalsIgnoreCase(STATUS_SUCCESSFULL)) {
                // check last capture
                if (lastTransactionId.isEmpty()) {
                    lastTransactionId = transactionHistoryDTO.getTransactionId();
                }

                if (transactionHistoryDTO.getTransactionId() != lastTransactionId) {
                    return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
                }
            }

            if(transactionId.equals(transactionHistoryDTO.getDadId()) && 
                transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_REQUEST_REFUND) && 
                (transactionHistoryDTO.getStatus().equalsIgnoreCase(IConstants.REFUND_STATUS_ONEPAY_APPROVED) ||
                transactionHistoryDTO.getStatus().equalsIgnoreCase(IConstants.REFUND_STATUS_WAITING_FOR_ONEPAY) ||
                transactionHistoryDTO.getStatus().equalsIgnoreCase(IConstants.REFUND_STATUS_WAITING_FOR_MERCHANT))) {
                logger.info("checkVoidCaptureQT has refund, transactionId={}", transactionId);
                return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
            }

            if(transactionId.equals(transactionHistoryDTO.getDadId()) && 
                transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_REFUND_CAPTURE) && 
                transactionHistoryDTO.getStatus().equalsIgnoreCase(STATUS_SUCCESSFULL)) {
                logger.info("checkVoidCaptureQT has refund, transactionId={}", transactionId);
                return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
            }

            // transaction 3B
            if(transactionId.equals(transactionHistoryDTO.getDadId()) && 
                transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_REFUND) && 
                transactionHistoryDTO.getStatus().equalsIgnoreCase(STATUS_SUCCESSFULL)) {
                logger.info("checkVoidCaptureQT has refund, transactionId={}", transactionId);
                return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
            }
        }

        String acquirerId = detail.getPaymentMethod().getAcquirer();
        int cutoffHour = acquirerDTO.getList().stream()
        .filter(acquirer -> acquirer.getId() == Integer.parseInt(acquirerId))
        .map(Acquirer::getCutoff)
        .findFirst()
        .orElse(0);
        String dateCutoff = checkCutoffDeadline(detail.getTransactionInformation().getTransactionCreatedTime(), cutoffHour);
        if (dateCutoff.length() > 0) {
            logger.info("checkVoidCaptureQT checkCutoffDeadline, transactionId={}, checkCutoffDeadline={}", transactionId, dateCutoff);
            return new ActionMessage(IConstants.ACTION_STATE_DISABLED, MESSAGE_CODE_VOID_CAPTURE_DEADLINE, dateCutoff);
        }

        return new ActionMessage(IConstants.ACTION_STATE_VISIBLE, "", "");
    }

    public static ActionMessage checkActionVoidRefundCapture(String transactionId, AcquirerDTO acquirerDTO, List<TransactionHistoryDTO> lstTransactionHistory,TransactionDetailResponse detail) {
        String paymentMethod = detail.getTransactionInformation().getPaymentMethod();
        switch (paymentMethod) {
        case IConstants.PAYMENT_METHOD_QT, IConstants.PAYMENT_METHOD_SS_PAY, IConstants.PAYMENT_METHOD_GG_PAY, IConstants.PAYMENT_METHOD_AP_PAY, IConstants.PAYMENT_METHOD_INSTALLMENT:
            return checkVoidRefundCaptureQT(transactionId, acquirerDTO, lstTransactionHistory, detail);
        default:
            logger.info("checkActionVoidRefundCapture paymentMethod invalid, transactionId={}, paymentMethod={}", transactionId, paymentMethod);
            return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
        }
    }

    private static ActionMessage checkVoidRefundCaptureQT(String transactionId, AcquirerDTO acquirerDTO, List<TransactionHistoryDTO> lstTransactionHistory,TransactionDetailResponse detail) {
        String transactionStatus = detail.getTransactionInformation().getTransactionStatus();
        if (transactionStatus == null || !transactionStatus.equalsIgnoreCase(STATUS_SUCCESSFULL)) {
            logger.info("checkVoidRefundCaptureQT transactionStatus invalid, transactionId={}, transactionStatus={}", transactionId, transactionStatus);
            return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
        }

        String gateway = acquirerDTO.getList().stream()
        .filter(acquirer -> acquirer.getId() == Integer.parseInt(detail.getPaymentMethod().getAcquirer()))
        .map(Acquirer::getGateway)
        .findFirst()
        .orElse("");

        // get capture_id
        String parentId = lstTransactionHistory.stream()
        .filter(transactionHistoryDTO -> transactionHistoryDTO.getTransactionId().equalsIgnoreCase(transactionId))
        .map(TransactionHistoryDTO::getDadId)
        .findFirst()
        .orElse("");

        String lastTransactionId = "";
        for (TransactionHistoryDTO transactionHistoryDTO : lstTransactionHistory) {
            if (parentId.equals(transactionHistoryDTO.getDadId()) && 
                transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_VOID_REFUND_CAPTURE) && 
                transactionHistoryDTO.getStatus().equalsIgnoreCase(STATUS_SUCCESSFULL)) {
                logger.info("checkVoidRefundCaptureQT has void refund capture, transactionId={}", transactionId);
                return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
            }

            if (gateway.equalsIgnoreCase(GATEWAY_MPGS) && parentId.equals(transactionHistoryDTO.getDadId()) && 
                transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_REFUND_CAPTURE) && 
                transactionHistoryDTO.getStatus().equalsIgnoreCase(STATUS_SUCCESSFULL)) {
                // check last refund capture
                if (lastTransactionId.isEmpty()) {
                    lastTransactionId = transactionHistoryDTO.getTransactionId();
                }
                 
                if (transactionHistoryDTO.getTransactionId() != lastTransactionId) {
                    return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
                }
            }
        }

        String acquirerId = detail.getPaymentMethod().getAcquirer();
        int cutoffHour = acquirerDTO.getList().stream()
        .filter(acquirer -> acquirer.getId() == Integer.parseInt(acquirerId))
        .map(Acquirer::getCutoff)
        .findFirst()
        .orElse(0);

        String dateCutoff = checkCutoffDeadline(detail.getTransactionInformation().getTransactionCreatedTime(), cutoffHour);
        if (dateCutoff.length() > 0) {
            logger.info("checkVoidRefundCaptureQT checkCutoffDeadline, transactionId={}, checkCutoffDeadline={}", transactionId, dateCutoff);
            return new ActionMessage(IConstants.ACTION_STATE_DISABLED, MESSAGE_CODE_VOID_REFUND_CAPTURE_DEADLINE, dateCutoff);
        }

        return new ActionMessage(IConstants.ACTION_STATE_VISIBLE, "", "");
    }

    public static ActionMessage checkActionVoidRefundPurchase(String transactionId, AcquirerDTO acquirerDTO, List<TransactionHistoryDTO> lstTransactionHistory,TransactionDetailResponse detail) {
        String paymentMethod = detail.getTransactionInformation().getPaymentMethod();
        switch (paymentMethod) {
        case IConstants.PAYMENT_METHOD_QT, IConstants.PAYMENT_METHOD_SS_PAY, IConstants.PAYMENT_METHOD_GG_PAY, IConstants.PAYMENT_METHOD_AP_PAY, IConstants.PAYMENT_METHOD_INSTALLMENT:
            return checkVoidRefundPurchaseQT(transactionId, acquirerDTO, lstTransactionHistory, detail);
        default:
            logger.info("checkActionVoidRefundPurchase paymentMethod invalid, transactionId={}, paymentMethod={}", transactionId, paymentMethod);
            return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
        }
    }

    private static ActionMessage checkVoidRefundPurchaseQT(String transactionId, AcquirerDTO acquirerDTO, List<TransactionHistoryDTO> lstTransactionHistory,TransactionDetailResponse detail) {
        String transactionStatus = detail.getTransactionInformation().getTransactionStatus();
        if (transactionStatus == null || !transactionStatus.equalsIgnoreCase(STATUS_SUCCESSFULL)) {
            logger.info("checkVoidRefundPurchaseQT transactionStatus invalid, transactionId={}, transactionStatus={}", transactionId, transactionStatus);
            return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
        }

        for (TransactionHistoryDTO transactionHistoryDTO : lstTransactionHistory) {
            if (transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_VOID_REFUND_PURCHASE) && 
                transactionHistoryDTO.getStatus().equalsIgnoreCase(STATUS_SUCCESSFULL)) {
                logger.info("checkVoidRefundPurchaseQT has void refund capture, transactionId={}", transactionId);
                return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
            }
        }

        String acquirerId = detail.getPaymentMethod().getAcquirer();
        int cutoffHour = acquirerDTO.getList().stream()
        .filter(acquirer -> acquirer.getId() == Integer.parseInt(acquirerId))
        .map(Acquirer::getCutoff)
        .findFirst()
        .orElse(0);

        String dateCutoff = checkCutoffDeadline(detail.getTransactionInformation().getTransactionCreatedTime(), cutoffHour);
        if (dateCutoff.length() > 0) {
            logger.info("checkVoidRefundPurchaseQT checkCutoffDeadline, transactionId={}, checkCutoffDeadline={}", transactionId, dateCutoff);
            return new ActionMessage(IConstants.ACTION_STATE_DISABLED, MESSAGE_CODE_VOID_REFUND_PURCHASE_DEADLINE, dateCutoff);
        }

        return new ActionMessage(IConstants.ACTION_STATE_VISIBLE, "", "");
    }

    public static ActionMessage checkActionVoidPurchase(String transactionId, AcquirerDTO acquirerDTO, List<TransactionHistoryDTO> lstTransactionHistory,TransactionDetailResponse detail) {
        String paymentMethod = detail.getTransactionInformation().getPaymentMethod();
        switch (paymentMethod) {
        case IConstants.PAYMENT_METHOD_QT, IConstants.PAYMENT_METHOD_SS_PAY, IConstants.PAYMENT_METHOD_GG_PAY, IConstants.PAYMENT_METHOD_AP_PAY, IConstants.PAYMENT_METHOD_INSTALLMENT:
            return checkVoidPurchaseQT(transactionId, acquirerDTO, lstTransactionHistory, detail);
        default:
            logger.info("checkActionVoidPurchase paymentMethod invalid, transactionId={}, paymentMethod={}", transactionId, paymentMethod);
            return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
        }
    }

    private static ActionMessage checkVoidPurchaseQT(String transactionId, AcquirerDTO acquirerDTO, List<TransactionHistoryDTO> lstTransactionHistory,TransactionDetailResponse detail) {
        String transactionStatus = detail.getTransactionInformation().getTransactionStatus();
        if (transactionStatus == null || !transactionStatus.equalsIgnoreCase(STATUS_SUCCESSFULL)) {
            logger.info("checkVoidPurchaseQT transactionStatus invalid, transactionId={}, transactionStatus={}", transactionId, transactionStatus);
            return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
        }

        for (TransactionHistoryDTO transactionHistoryDTO : lstTransactionHistory) {
            if (transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_VOID_PURCHASE) && 
                transactionHistoryDTO.getStatus().equalsIgnoreCase(STATUS_SUCCESSFULL)) {
                logger.info("checkVoidPurchaseQT has void purchase, transactionId={}", transactionId);
                return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
            }

            if(transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_REFUND) && 
                transactionHistoryDTO.getStatus().equalsIgnoreCase(STATUS_SUCCESSFULL)) {
                logger.info("checkVoidPurchaseQT has refund, transactionId={}", transactionId);
                return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
            }

            if(transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_REQUEST_REFUND) && 
                (transactionHistoryDTO.getStatus().equalsIgnoreCase(IConstants.REFUND_STATUS_ONEPAY_APPROVED) ||
                transactionHistoryDTO.getStatus().equalsIgnoreCase(IConstants.REFUND_STATUS_WAITING_FOR_ONEPAY) ||
                transactionHistoryDTO.getStatus().equalsIgnoreCase(IConstants.REFUND_STATUS_WAITING_FOR_MERCHANT))) {
                logger.info("checkVoidPurchaseQT has refund, transactionId={}", transactionId);
                return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
            }
        }

        // cardType is PayPal => return false
        if (detail.getPaymentMethod().getCardType().equalsIgnoreCase(IConstants.CARD_TYPE_PAYPAL)) {
           return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
        }

        String installmentStatus = detail.getTransactionInformation().getInstallmentStatus();
        if (installmentStatus.equalsIgnoreCase(IConstants.INSTALLMENT_STATUS_APPROVED) ||
            installmentStatus.equalsIgnoreCase(IConstants.INSTALLMENT_STATUS_SENT)) {
            return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
        }

        String acquirerId = detail.getPaymentMethod().getAcquirer();
        int cutoffHour = acquirerDTO.getList().stream()
        .filter(acquirer -> acquirer.getId() == Integer.parseInt(acquirerId))
        .map(Acquirer::getCutoff)
        .findFirst()
        .orElse(0);

        String dateCutoff = checkCutoffDeadline(detail.getTransactionInformation().getTransactionCreatedTime(), cutoffHour);
        if (dateCutoff.length() > 0) {
            logger.info("checkVoidPurchaseQT checkCutoffDeadline, transactionId={}, checkCutoffDeadline={}", transactionId, dateCutoff);
            return new ActionMessage(IConstants.ACTION_STATE_DISABLED, MESSAGE_CODE_VOID_PURCHASE_DEADLINE, dateCutoff);
        }

        String messageCode = "";
        if(installmentStatus.equalsIgnoreCase(IConstants.INSTALLMENT_STATUS_CREATED)) {
            messageCode = MESSAGE_CODE_VOID_PURCHASE_INSTALLMENT_STATUS_CREATE;
        }

        return new ActionMessage(IConstants.ACTION_STATE_VISIBLE, messageCode, "");
    }

    public static ActionMessage checkActionVoidAuthorize(String transactionId, List<TransactionHistoryDTO> lstTransactionHistory,TransactionDetailResponse detail) {
        String paymentMethod = detail.getTransactionInformation().getPaymentMethod();
        switch (paymentMethod) {
        case IConstants.PAYMENT_METHOD_QT, IConstants.PAYMENT_METHOD_SS_PAY, IConstants.PAYMENT_METHOD_GG_PAY, IConstants.PAYMENT_METHOD_AP_PAY, IConstants.PAYMENT_METHOD_INSTALLMENT:
            return checkVoidAuthorizeQT(transactionId, lstTransactionHistory, detail);
        default:
            logger.info("checkActionVoidAuthorize paymentMethod invalid, transactionId={}, paymentMethod={}", transactionId, paymentMethod);
            return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
        }
    }
    
    private static ActionMessage checkVoidAuthorizeQT(String transactionId, List<TransactionHistoryDTO> lstTransactionHistory,TransactionDetailResponse detail) {
        String transactionStatus = detail.getTransactionInformation().getTransactionStatus();
        if (transactionStatus == null || !transactionStatus.equalsIgnoreCase(STATUS_SUCCESSFULL)) {
            logger.info("checkVoidAuthorizeQT transactionStatus invalid, transactionId={}, transactionStatus={}", transactionId, transactionStatus);
            return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
        }

        for (TransactionHistoryDTO transactionHistoryDTO : lstTransactionHistory) {
            if (transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_VOID_AUTHORIZE) && 
                transactionHistoryDTO.getStatus().equalsIgnoreCase(STATUS_SUCCESSFULL)) {
                logger.info("checkVoidAuthorizeQT has void authorize, transactionId={}", transactionId);
                return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
            }

            if(transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_CAPTURE) && 
                transactionHistoryDTO.getStatus().equalsIgnoreCase(STATUS_SUCCESSFULL)) {
                logger.info("checkVoidAuthorizeQT has capture, transactionId={}", transactionId);
                return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
            }
        }

        // check cutoff 30 days
        String checkDeadlineVoidAuthorize = checkDeadline(detail.getTransactionInformation().getTransactionCreatedTime(), internationalAuthorizeDeadline);
        if (!checkDeadlineVoidAuthorize.isEmpty()) {
            logger.info("checkVoidAuthorizeQT checkDeadlineVoidAuthorize, transactionId={}, checkDeadlineVoidAuthorize={}", transactionId, checkDeadlineVoidAuthorize);
            return new ActionMessage(IConstants.ACTION_STATE_DISABLED, MESSAGE_CODE_VOID_AUTHORIZE_DEADLINE, checkDeadlineVoidAuthorize);
        }

        String installmentStatus = detail.getTransactionInformation().getInstallmentStatus();
        String messageCode = "";
        if(installmentStatus.equalsIgnoreCase(IConstants.INSTALLMENT_STATUS_CREATED)) {
            messageCode = MESSAGE_CODE_VOID_PURCHASE_INSTALLMENT_STATUS_CREATE;
        }

        return new ActionMessage(IConstants.ACTION_STATE_VISIBLE, messageCode, "");
    }

    public static ActionMessage checkActionRefundCapture(String transactionId, BigDecimal amountRefund, List<TransactionHistoryDTO> lstTransactionHistory,TransactionDetailResponse detail) {
        String paymentMethod = detail.getTransactionInformation().getPaymentMethod();
        switch (paymentMethod) {
        case IConstants.PAYMENT_METHOD_QT, IConstants.PAYMENT_METHOD_SS_PAY, IConstants.PAYMENT_METHOD_GG_PAY, IConstants.PAYMENT_METHOD_AP_PAY, IConstants.PAYMENT_METHOD_INSTALLMENT:
            return checkRefundCaptureQT(transactionId, amountRefund, lstTransactionHistory, detail);
        default:
            logger.info("checkActionRefundCapture paymentMethod invalid, transactionId={}, paymentMethod={}", transactionId, paymentMethod);
            return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
        }
    }

    private static ActionMessage checkRefundCaptureQT(String transactionId, BigDecimal amountRefund, List<TransactionHistoryDTO> lstTransactionHistory,TransactionDetailResponse detail) {
        String transactionStatus = detail.getTransactionInformation().getTransactionStatus();
        if (transactionStatus == null || !transactionStatus.equalsIgnoreCase(STATUS_SUCCESSFULL)) {
            logger.info("checkRefundCaptureQT transactionStatus invalid, transactionId={}, transactionStatus={}", transactionId, transactionStatus);
            return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
        }

        // cardType is PayPal => return false
        if (detail.getPaymentMethod().getCardType().equalsIgnoreCase(IConstants.CARD_TYPE_PAYPAL)) {
            logger.info("checkRefundCaptureQT cardType invalid, transactionId={}, cardType={}", transactionId, detail.getPaymentMethod().getCardType());
            return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
        }

        BigDecimal totalRefund = BigDecimal.ZERO;
        BigDecimal totalAmount = detail.getTransactionInformation().getTransactionAmount();
        for (TransactionHistoryDTO transactionHistoryDTO : lstTransactionHistory) {
            if (transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_VOID_CAPTURE) && 
                transactionHistoryDTO.getStatus().equalsIgnoreCase(STATUS_SUCCESSFULL) &&
                transactionHistoryDTO.getDadId().equals(transactionId)) {
                logger.info("checkRefundCaptureQT has void capture, transactionId={}", transactionId);
                return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
            }

            if (transactionHistoryDTO.getDadId().equals(transactionId) && 
                (transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_REFUND) ||
                (transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_REQUEST_REFUND) && 
                    (transactionHistoryDTO.getStatus().equalsIgnoreCase(IConstants.REFUND_STATUS_WAITING_FOR_ONEPAY) ||
                    transactionHistoryDTO.getStatus().equalsIgnoreCase(IConstants.REFUND_STATUS_WAITING_FOR_MERCHANT) ||
                    transactionHistoryDTO.getStatus().equalsIgnoreCase(IConstants.REFUND_STATUS_ONEPAY_APPROVED))))) {
                totalRefund = totalRefund.add(transactionHistoryDTO.getAmount());
            }

            if (transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_VOID_REFUND_CAPTURE) && 
                transactionHistoryDTO.getStatus().equalsIgnoreCase(STATUS_SUCCESSFULL) &&
                transactionHistoryDTO.getDadId().equals(transactionId)) {
                totalRefund = totalRefund.subtract(transactionHistoryDTO.getAmount());
            }
        }

        totalRefund = totalRefund.add(amountRefund);
        if ((amountRefund == BigDecimal.ZERO && totalAmount.compareTo(totalRefund) <= 0) || (amountRefund.compareTo(BigDecimal.ZERO) > 0 && totalAmount.compareTo(totalRefund) < 0)) {
            logger.info("checkRefundCaptureQT totalRefund > totalAmount, transactionId={}, totalRefund={}, totalAmount={}", transactionId, totalRefund, totalAmount);
            return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
        }

        String installmentStatus = detail.getTransactionInformation().getInstallmentStatus();
        String messageCode = "";
        switch (installmentStatus) {
            case IConstants.INSTALLMENT_STATUS_CREATED:
                messageCode = MESSAGE_CODE_REFUND_CAPTURE_INSTALLMENT_STATUS_CREATE;
                break;
            case IConstants.INSTALLMENT_STATUS_APPROVED:
                messageCode = MESSAGE_CODE_REFUND_CAPTURE_INSTALLMENT_STATUS_APPROVED;
                break;
            case IConstants.INSTALLMENT_STATUS_SENT:
                messageCode = MESSAGE_CODE_REFUND_CAPTURE_INSTALLMENT_STATUS_SENT;
                break;
            default:
                break;
        }

        ActionMessage msg = new ActionMessage(IConstants.ACTION_STATE_VISIBLE, messageCode, "");
        msg.setRemainingAmount(totalAmount.subtract(totalRefund));
        msg.setRefundPartial(true);

        return msg;
    }

    private static ActionMessage checkActionApproveAndRejectRefund(String transactionId, TransactionDetailResponse detail) {
        String transactionStatus = detail.getTransactionInformation().getTransactionStatus();
        if (transactionStatus == null || !transactionStatus.equalsIgnoreCase(IConstants.REFUND_STATUS_WAITING_FOR_MERCHANT)) {
            logger.info("checkApproveAndRejectRefund transactionStatus invalid, transactionId={}, transactionStatus={}", transactionId, transactionStatus);
            return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
        }

        if (!detail.getTransactionInformation().getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_REQUEST_REFUND)) {
            logger.info("checkApproveAndRejectRefund transactionType invalid, transactionId={}, transactionType={}", transactionId, detail.getTransactionInformation().getTransactionType());
            return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
        }

        return new ActionMessage(IConstants.ACTION_STATE_VISIBLE, "", "");
    }

    public static ActionMessage checkActionRefundPurchase(String transactionId, BigDecimal amountRefund, List<TransactionHistoryDTO> lstTransactionHistory,TransactionDetailResponse detail, MSPMerchant merchant) {
        String paymentMethod = detail.getTransactionInformation().getPaymentMethod();
        switch (paymentMethod) {
        case IConstants.PAYMENT_METHOD_QT, IConstants.PAYMENT_METHOD_SS_PAY, IConstants.PAYMENT_METHOD_GG_PAY, IConstants.PAYMENT_METHOD_AP_PAY, IConstants.PAYMENT_METHOD_INSTALLMENT:
            return checkRefundPurchaseQT(transactionId, amountRefund, lstTransactionHistory, detail);
        case IConstants.PAYMENT_METHOD_ND:
            return checkRefundPurchaseND(transactionId, amountRefund, lstTransactionHistory, detail);
        case IConstants.PAYMENT_METHOD_QR:
            return checkRefundPurchaseQR(transactionId, amountRefund, lstTransactionHistory, detail);
        case IConstants.PAYMENT_METHOD_BNPL:
            return checkRefundPurchaseBNPL(transactionId, amountRefund, lstTransactionHistory, detail);
        case IConstants.PAYMENT_METHOD_VIETQR:
            return checkRefundPurchaseVietQR(transactionId, amountRefund, lstTransactionHistory, detail, merchant);
        default:
            logger.info("checkActionRefundPurchase paymentMethod invalid, transactionId={}, paymentMethod={}", transactionId, paymentMethod);
            return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
        }
    }

    private static ActionMessage checkRefundPurchaseVietQR(String transactionId, BigDecimal amountRefund, List<TransactionHistoryDTO> lstTransactionHistory,TransactionDetailResponse detail, MSPMerchant merchant) {
        String transactionStatus = detail.getTransactionInformation().getTransactionStatus();
        if (transactionStatus == null || !transactionStatus.equalsIgnoreCase(STATUS_SUCCESSFULL)) {
            logger.info("checkRefundPurchaseVietQR transactionStatus invalid, transactionId={}, transactionStatus={}", transactionId, transactionStatus);
            return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
        }

        BigDecimal totalRefund = BigDecimal.ZERO;
        BigDecimal totalAmount = detail.getTransactionInformation().getTransactionAmount();
        for (TransactionHistoryDTO transactionHistoryDTO : lstTransactionHistory) {
            if ((transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_REFUND) && 
                (transactionHistoryDTO.getStatus().equalsIgnoreCase(STATUS_SUCCESSFULL) ||
                transactionHistoryDTO.getStatus().equalsIgnoreCase(STATUS_PENDING))) ||
                (transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_REQUEST_REFUND) && 
                    (transactionHistoryDTO.getStatus().equalsIgnoreCase(IConstants.REFUND_STATUS_WAITING_FOR_ONEPAY) ||
                    transactionHistoryDTO.getStatus().equalsIgnoreCase(IConstants.REFUND_STATUS_WAITING_FOR_MERCHANT) ||
                    transactionHistoryDTO.getStatus().equalsIgnoreCase(IConstants.REFUND_STATUS_ONEPAY_APPROVED)))) {
                totalRefund = totalRefund.add(transactionHistoryDTO.getAmount());
            }
        }

        totalRefund = totalRefund.add(amountRefund);
        if ((amountRefund == BigDecimal.ZERO && totalAmount.compareTo(totalRefund) <= 0) || (amountRefund.compareTo(BigDecimal.ZERO) > 0 && totalAmount.compareTo(totalRefund) < 0)) {
            logger.info("checkRefundPurchaseVietQR totalRefund > totalAmount, transactionId={}, totalRefund={}, totalAmount={}", transactionId, totalRefund, totalAmount);
            return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
        }

        ActionMessage msg = new ActionMessage(IConstants.ACTION_STATE_VISIBLE, "", "");
        if (merchant != null && merchant.getSData() != null) {
            msg.setRefundPartial(allowRefundPartialForVietqr(merchant.getSData()));
        } else {
            msg.setRefundPartial(false);
        }

        // if refund partial is not allowed and amount refund is not equal total amount => hide
        if (amountRefund.compareTo(BigDecimal.ZERO) > 0 && amountRefund.compareTo(totalAmount) != 0 && !msg.getRefundPartial()) {
            msg.setActionState(IConstants.ACTION_STATE_HIDDEN);
        }

        msg.setRemainingAmount(totalAmount.subtract(totalRefund));
        return msg;
    }   

    private static ActionMessage checkRefundPurchaseBNPL(String transactionId, BigDecimal amountRefund, List<TransactionHistoryDTO> lstTransactionHistory,TransactionDetailResponse detail) {
        String transactionStatus = detail.getTransactionInformation().getTransactionStatus();
        if (transactionStatus == null || !transactionStatus.equalsIgnoreCase(STATUS_SUCCESSFULL)) {
            logger.info("checkRefundPurchaseBNPL transactionStatus invalid, transactionId={}, transactionStatus={}", transactionId, transactionStatus);
            return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
        }

        BigDecimal totalRefund = BigDecimal.ZERO;
        BigDecimal totalAmount = detail.getTransactionInformation().getTransactionAmount();
        for (TransactionHistoryDTO transactionHistoryDTO : lstTransactionHistory) {
            if ((transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_REFUND) && 
                (transactionHistoryDTO.getStatus().equalsIgnoreCase(STATUS_SUCCESSFULL) ||
                transactionHistoryDTO.getStatus().equalsIgnoreCase(STATUS_PENDING))) ||
                (transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_REQUEST_REFUND) && 
                    (transactionHistoryDTO.getStatus().equalsIgnoreCase(IConstants.REFUND_STATUS_WAITING_FOR_ONEPAY) ||
                    transactionHistoryDTO.getStatus().equalsIgnoreCase(IConstants.REFUND_STATUS_WAITING_FOR_MERCHANT) ||
                    transactionHistoryDTO.getStatus().equalsIgnoreCase(IConstants.REFUND_STATUS_ONEPAY_APPROVED)))) {
                totalRefund = totalRefund.add(transactionHistoryDTO.getAmount());
            }
        }

        totalRefund = totalRefund.add(amountRefund);
        if ((amountRefund == BigDecimal.ZERO && totalAmount.compareTo(totalRefund) <= 0) || (amountRefund.compareTo(BigDecimal.ZERO) > 0 && totalAmount.compareTo(totalRefund) < 0)) {
            logger.info("checkRefundPurchaseBNPL totalRefund > totalAmount, transactionId={}, totalRefund={}, totalAmount={}", transactionId, totalRefund, totalAmount);
            return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
        }

        String provider = detail.getPaymentMethod().getProvider();
        if (detail.getMerchantInformation().getMerchantId().equalsIgnoreCase(MERCHANT_ID_OP_SAMSUNG) &&
            provider.equalsIgnoreCase(IConstants.PROVIDER_KREDIVO)) {
            // check kredivo deadline 14 days
            String checkRefundDeadline = checkDeadline(detail.getTransactionInformation().getTransactionCreatedTime(), bnplRefundKredivoDeadline);
            if (!checkRefundDeadline.isEmpty()) {
                logger.info("checkRefundPurchaseBNPL checkRefundDeadline, transactionId={}, checkRefundDeadline={}", transactionId, checkRefundDeadline);
                return new ActionMessage(IConstants.ACTION_STATE_DISABLED, MESSAGE_CODE_REFUND_DEADLINE, checkRefundDeadline);
            }
        }

        if (provider.equalsIgnoreCase(IConstants.PROVIDER_FUNDIIN) || provider.equalsIgnoreCase(IConstants.PROVIDER_HOMECREDIT)) {
            // check homecredit and fundiin deadline 22 days
            String checkRefundDeadline = checkDeadline(detail.getTransactionInformation().getTransactionCreatedTime(), bnplRefundHomecreditDeadline);
            if (!checkRefundDeadline.isEmpty()) {
                logger.info("checkRefundPurchaseBNPL checkRefundDeadline, transactionId={}, checkRefundDeadline={}", transactionId, checkRefundDeadline);
                return new ActionMessage(IConstants.ACTION_STATE_DISABLED, MESSAGE_CODE_REFUND_DEADLINE, checkRefundDeadline);
            }
        }

        ActionMessage msg = new ActionMessage(IConstants.ACTION_STATE_VISIBLE, "", "");
        
        msg.setRefundPartial(false);
        if (provider.equalsIgnoreCase(IConstants.PROVIDER_HOMECREDIT)) {
            msg.setRefundPartial(true);
        }

        msg.setRemainingAmount(totalAmount.subtract(totalRefund));
        return msg;
    }

    private static ActionMessage checkRefundPurchaseQR(String transactionId, BigDecimal amountRefund, List<TransactionHistoryDTO> lstTransactionHistory,TransactionDetailResponse detail) {
        String transactionStatus = detail.getTransactionInformation().getTransactionStatus();
        if (transactionStatus == null || !transactionStatus.equalsIgnoreCase(STATUS_SUCCESSFULL)) {
            logger.info("checkRefundPurchaseQR transactionStatus invalid, transactionId={}, transactionStatus={}", transactionId, transactionStatus);
            return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
        }

        BigDecimal totalRefund = BigDecimal.ZERO;
        BigDecimal totalAmount = detail.getTransactionInformation().getTransactionAmount();
        for (TransactionHistoryDTO transactionHistoryDTO : lstTransactionHistory) {
            if ((transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_REFUND) && 
                (transactionHistoryDTO.getStatus().equalsIgnoreCase(STATUS_SUCCESSFULL) ||
                transactionHistoryDTO.getStatus().equalsIgnoreCase(STATUS_PENDING))) ||
                (transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_REQUEST_REFUND) && 
                    (transactionHistoryDTO.getStatus().equalsIgnoreCase(IConstants.REFUND_STATUS_WAITING_FOR_ONEPAY) ||
                    transactionHistoryDTO.getStatus().equalsIgnoreCase(IConstants.REFUND_STATUS_WAITING_FOR_MERCHANT) ||
                    transactionHistoryDTO.getStatus().equalsIgnoreCase(IConstants.REFUND_STATUS_ONEPAY_APPROVED)))) {
                totalRefund = totalRefund.add(transactionHistoryDTO.getAmount());
            }
        }

        totalRefund = totalRefund.add(amountRefund);
        if ((amountRefund == BigDecimal.ZERO && totalAmount.compareTo(totalRefund) <= 0) || (amountRefund.compareTo(BigDecimal.ZERO) > 0 && totalAmount.compareTo(totalRefund) < 0)) {
            logger.info("checkRefundPurchaseQR totalRefund > totalAmount, transactionId={}, totalRefund={}, totalAmount={}", transactionId, totalRefund, totalAmount);
            return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
        }

        String checkRefundDeadline = "";
        switch (detail.getPaymentMethod().getAppName()) {
            // case IConstants.APP_NAME_SMARTPAY:
            //     checkRefundDeadline = checkDeadline(detail.getTransactionInformation().getTransactionCreatedTime(), 80);
            //     break;
            case IConstants.APP_NAME_MOMO, IConstants.APP_NAME_ZALOPAY:
                // check deadline 365 days
                checkRefundDeadline = checkDeadline(detail.getTransactionInformation().getTransactionCreatedTime(), qrRefundDeadline);
                break;
            default:
                break;
        }

        if (!checkRefundDeadline.isEmpty()) {
            logger.info("checkRefundPurchaseQR checkRefundDeadline, transactionId={}, checkRefundDeadline={}", transactionId, checkRefundDeadline);
            return new ActionMessage(IConstants.ACTION_STATE_DISABLED, MESSAGE_CODE_REFUND_DEADLINE, checkRefundDeadline);
        }

        ActionMessage msg = new ActionMessage(IConstants.ACTION_STATE_VISIBLE, "", "");
        msg.setRefundPartial(true);
        msg.setRemainingAmount(totalAmount.subtract(totalRefund));
        return msg;
    }
    
    private static ActionMessage checkRefundPurchaseND(String transactionId, BigDecimal amountRefund, List<TransactionHistoryDTO> lstTransactionHistory,TransactionDetailResponse detail) {
        String transactionStatus = detail.getTransactionInformation().getTransactionStatus();
        if (transactionStatus == null || !transactionStatus.equalsIgnoreCase(STATUS_SUCCESSFULL)) {
            logger.info("checkRefundPurchaseND transactionStatus invalid, transactionId={}, transactionStatus={}", transactionId, transactionStatus);
            return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
        }

        BigDecimal totalRefund = BigDecimal.ZERO;
        BigDecimal totalAmount = detail.getTransactionInformation().getTransactionAmount();
        for (TransactionHistoryDTO transactionHistoryDTO : lstTransactionHistory) {
            if (transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_REQUEST_REFUND) && 
                    (transactionHistoryDTO.getStatus().equalsIgnoreCase(IConstants.REFUND_STATUS_WAITING_FOR_ONEPAY) ||
                    transactionHistoryDTO.getStatus().equalsIgnoreCase(IConstants.REFUND_STATUS_WAITING_FOR_MERCHANT) ||
                    transactionHistoryDTO.getStatus().equalsIgnoreCase(IConstants.REFUND_STATUS_ONEPAY_APPROVED))) {
                totalRefund = totalRefund.add(transactionHistoryDTO.getAmount());
            }
        }

        totalRefund = totalRefund.add(amountRefund);
        if ((amountRefund == BigDecimal.ZERO && totalAmount.compareTo(totalRefund) <= 0) || (amountRefund.compareTo(BigDecimal.ZERO) > 0 && totalAmount.compareTo(totalRefund) < 0)) {
            logger.info("checkRefundPurchaseND totalRefund > totalAmount, transactionId={}, totalRefund={}, totalAmount={}", transactionId, totalRefund, totalAmount);
            return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
        }

        ActionMessage msg = new ActionMessage(IConstants.ACTION_STATE_VISIBLE, "", "");
        msg.setRefundPartial(true);
        msg.setRemainingAmount(totalAmount.subtract(totalRefund));
        return msg;
    }

    private static ActionMessage checkRefundPurchaseQT(String transactionId, BigDecimal amountRefund, List<TransactionHistoryDTO> lstTransactionHistory,TransactionDetailResponse detail) {
        String transactionStatus = detail.getTransactionInformation().getTransactionStatus();
        if (transactionStatus == null || !transactionStatus.equalsIgnoreCase(STATUS_SUCCESSFULL)) {
            logger.info("checkRefundPurchaseQT transactionStatus invalid, transactionId={}, transactionStatus={}", transactionId, transactionStatus);
            return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
        }

        BigDecimal totalRefund = BigDecimal.ZERO;
        BigDecimal totalAmount = detail.getTransactionInformation().getTransactionAmount();
        for (TransactionHistoryDTO transactionHistoryDTO : lstTransactionHistory) {
            if (transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_VOID_PURCHASE) && 
                transactionHistoryDTO.getStatus().equalsIgnoreCase(STATUS_SUCCESSFULL)) {
                logger.info("checkRefundPurchaseQT has void purchase, transactionId={}", transactionId);
                return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
            }

            if ((transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_REFUND) && 
                transactionHistoryDTO.getStatus().equalsIgnoreCase(STATUS_SUCCESSFULL)) ||
                (transactionHistoryDTO.getTransactionType().equalsIgnoreCase(IConstants.TRANSACTION_TYPE_REQUEST_REFUND) && 
                    (transactionHistoryDTO.getStatus().equalsIgnoreCase(IConstants.REFUND_STATUS_WAITING_FOR_ONEPAY) ||
                    transactionHistoryDTO.getStatus().equalsIgnoreCase(IConstants.REFUND_STATUS_WAITING_FOR_MERCHANT) ||
                    transactionHistoryDTO.getStatus().equalsIgnoreCase(IConstants.REFUND_STATUS_ONEPAY_APPROVED)))) {
                totalRefund = totalRefund.add(transactionHistoryDTO.getAmount());
            }
        }

        totalRefund = totalRefund.add(amountRefund);
        if ((amountRefund == BigDecimal.ZERO && totalAmount.compareTo(totalRefund) <= 0) || (amountRefund.compareTo(BigDecimal.ZERO) > 0 && totalAmount.compareTo(totalRefund) < 0)) {
            logger.info("checkRefundPurchaseQT totalRefund > totalAmount, transactionId={}, totalRefund={}, totalAmount={}", transactionId, totalRefund, totalAmount);
            return new ActionMessage(IConstants.ACTION_STATE_HIDDEN, "", "");
        }

        if (detail.getPaymentMethod().getCardType().equalsIgnoreCase(IConstants.CARD_TYPE_JCB)) {
            // check deadline 365 days
            String checkRefundDeadline = checkDeadline(detail.getTransactionInformation().getTransactionCreatedTime(), internationalRefundPurchaseDeadline);
            if (!checkRefundDeadline.isEmpty()) {
                logger.info("checkRefundPurchaseQT checkRefundDeadline, transactionId={}, checkRefundDeadline={}", transactionId, checkRefundDeadline);
                return new ActionMessage(IConstants.ACTION_STATE_DISABLED, MESSAGE_CODE_REFUND_DEADLINE, checkRefundDeadline);
            }
        }

        String installmentStatus = detail.getTransactionInformation().getInstallmentStatus();
        String messageCode = "";
        switch (installmentStatus) {
            case IConstants.INSTALLMENT_STATUS_APPROVED:
                messageCode = MESSAGE_CODE_REFUND_PURCHASE_INSTALLMENT_STATUS_APPROVED;
                break;
            case IConstants.INSTALLMENT_STATUS_SENT:
                messageCode = MESSAGE_CODE_REFUND_PURCHASE_INSTALLMENT_STATUS_SENT;
                break;
            case IConstants.INSTALLMENT_STATUS_CREATED:
                messageCode = MESSAGE_CODE_REFUND_PURCHASE_INSTALLMENT_STATUS_CREATE;
                break;
            default:
                break;
        }
        ActionMessage msg = new ActionMessage(IConstants.ACTION_STATE_VISIBLE, messageCode, "");
        msg.setRefundPartial(true);
        msg.setRemainingAmount(totalAmount.subtract(totalRefund));
        return msg;
    }

    private static Boolean allowRefundPartialForVietqr(String jsonData) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode jsonNode = objectMapper.readTree(jsonData);

            JsonNode vietqrNode = jsonNode
                .path("refund_config")
                .path("merchant_refund_partial")
                .path("vietqr");

            return vietqrNode.isBoolean() && vietqrNode.asBoolean();
        } catch (Exception e) {
            logger.warn("Error parsing JSON for vietqr: {}", jsonData, e);
            return false;
        }
    }

    /**
     * Check if transaction is within the allowed refund window (before 23:59:59 of transactionDate + daysToAdd)
     *
     * @param transactionDateStr Date string (e.g. "2025-07-01T01:00:00")
     * @param daysToAdd Number of days allowed for refund (e.g. 365 for 1 year)
     * @return formatted deadline (dd/MM/yyyy) if still valid, empty string if expired or error
     */
    public static String checkDeadline(String transactionDateStr, int daysToAdd) {
        try {
            // Parse transaction date and reset time to 00:00:00
            LocalDateTime transactionDate = LocalDateTime.parse(transactionDateStr, inputFormatter)
                                                        .withHour(0).withMinute(0).withSecond(0).withNano(0);

            // Add allowed days and set deadline to 23:59:59
            LocalDateTime deadline = transactionDate.plusDays(daysToAdd)
                                                    .withHour(23).withMinute(59).withSecond(59);

            LocalDateTime now = LocalDateTime.now();

            // Compare now with deadline
            if (now.isAfter(deadline)) {
                return deadline.toLocalDate().format(displayTimeFormatter);  // return dd/MM/yyyy
            } else {
                return "";
            }

        } catch (Exception e) {
            logger.error("Error parsing date: {}", transactionDateStr, e);
            return "";
        }
    }

    /**
     * Check if transaction is within the allowed refund window (before transactionDate + daysToAdd)
     *
     * @param transactionDateStr Date string (e.g. "2025-07-01T01:00:00")
     * @param daysToAdd Number of days allowed for refund (e.g. 365 for 1 year)
     * @return formatted deadline (dd/MM/yyyy) if still valid, empty string if expired or error
     */
    public static String checkTimeDeadline(String transactionDateStr, int daysToAdd) {
        try {
            // Parse transaction date
            LocalDateTime transactionDate = LocalDateTime.parse(transactionDateStr, inputFormatter);

            // Add allowed days
            LocalDateTime deadline = transactionDate.plusDays(daysToAdd);

            LocalDateTime now = LocalDateTime.now();

            // Compare now with deadline
            if (now.isAfter(deadline)) {
                return deadline.toLocalDate().format(displayFormatter);  // return hh:mm:ss dd/MM/yyyy
            } else {
                return "";
            }

        } catch (Exception e) {
            logger.error("Error parsing date: {}", transactionDateStr, e);
            return "";
        }
    }

    /**
     * Check if current time is after cutoff time based on transaction time.
     * - If Purchase is before cutoff hour of day T => cutoff is same day T at cutoffHour
     * - If Purchase is after cutoff hour => cutoff is T+1 at cutoffHour
     *
     * @param dateString  Transaction datetime string (e.g. "2025-07-11T18:13:06")
     * @param cutoffHour  Cutoff hour (e.g. 17 means 17:00:00)
     * @return formatted deadline time (hh:mm:ss dd/MM/yyyy) if now is after cutoff, else empty string
    */
    public static String checkCutoffDeadline(String dateString, int cutoffHour) {
        try {
            DateTimeFormatter inputFormatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME;

            LocalDateTime transactionTime = LocalDateTime.parse(dateString, inputFormatter);

            // Set cutoff time for same day at cutoffHour
            LocalDateTime cutoffTime = transactionTime.with(LocalTime.of(cutoffHour, 0));

            // If transaction was after cutoff, then cutoff is next day at cutoffHour
            if (transactionTime.isAfter(cutoffTime)) {
                cutoffTime = cutoffTime.plusDays(1);
            }

            LocalDateTime now = LocalDateTime.now();
            logger.info("checkCutoffDeadline transactionTime={}, cutoffTime={}, now={}, hour={}", transactionTime, cutoffTime, now, cutoffHour);

            // If current time is after cutoff -> return formatted cutoff time
            if (now.isAfter(cutoffTime)) {
                return cutoffTime.format(displayTimeFormatter);
            }

            // Still valid
            return "";
        } catch (Exception e) {
            System.err.println("Error parsing date: " + dateString);
            e.printStackTrace();
            return "";
        }
    }

    /**
     * Check if the current time is within a specific range (in days) from the authorize time.
     *
     * Conditions:
     * - Current time - authorize time < endDays
     * - Current time - authorize time >= startDays
     *
     * @param authorizeDateTimeStr  the authorize time string in ISO format (e.g. "2025-07-01T14:15:00")
     * @param startDays             minimum number of days (inclusive)
     * @param endDays               maximum number of days (exclusive)
     * @return true if within the range, false otherwise
     */
    public static boolean isInRange(String authorizeDateTimeStr, int startDays, int endDays) {
        try {
            // Parse the authorize time
            LocalDateTime authorizeTime = LocalDateTime.parse(authorizeDateTimeStr, inputFormatter);

            // Get current local system time
            LocalDateTime now = LocalDateTime.now();

            // Calculate number of full days between authorize time and now
            long daysBetween = ChronoUnit.DAYS.between(authorizeTime, now);

            // Check if within the [startDays, endDays) range
            return daysBetween >= startDays && daysBetween < endDays;
        } catch (Exception e) {
            System.err.println("Error parsing date: " + authorizeDateTimeStr);
            e.printStackTrace();
            return false;
        }
    }
}
