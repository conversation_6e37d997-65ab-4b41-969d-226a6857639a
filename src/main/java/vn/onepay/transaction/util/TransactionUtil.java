package vn.onepay.transaction.util;

import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.databind.ObjectMapper;

import vn.onepay.transaction.constant.IConstants;

public class TransactionUtil {
    private static final Logger logger = LoggerFactory.getLogger(TransactionUtil.class);

    public static String prepareRefundRequestBNPL(String jsonData, String provider, String newNote, String newCanceledBy) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> jsonMap = mapper.readValue(jsonData, Map.class);

            switch (provider) {
                case IConstants.PROVIDER_KREDIVO:
                    // Replace values
                    jsonMap.put("note", newNote);
                    jsonMap.put("canceledby", newCanceledBy);
                    
                    // Convert back to string
                    return mapper.writeValueAsString(jsonMap);
                default:
                    logger.info("prepareRefundRequestBNPL Provider {} not supported", provider);
                    return jsonData;
            }
        } catch (Exception e) {
            logger.error("prepareRefundRequestBNPL Error preparing refund request: {}", e.getMessage(), e);
            return jsonData;
        }
    }
}
