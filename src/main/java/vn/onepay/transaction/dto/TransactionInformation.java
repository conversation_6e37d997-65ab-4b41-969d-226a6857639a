package vn.onepay.transaction.dto;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TransactionInformation {
    private String transactionId;
    private String parentTransactionId;
    private String transactionType;
    private String paymentMethod;
    private String transactionCreatedTime;
    private String transactionCompletedTime;
    private BigDecimal transactionAmount;
    private String transactionCurrency;
    private String transactionStatus;
    private String responseCode;
    private String merchantTransRef;
    private String parentMerchantTransRef;
    private String paymentChannel;
    private String installmentStatus;
    private String operator;
}
