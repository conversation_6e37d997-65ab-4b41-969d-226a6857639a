package vn.onepay.transaction.dto;

import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PromotionInformation {
    private String promotionCode;
    private String promotionName;
    private BigDecimal discountAmount;
    private BigDecimal originAmount;
    private BigDecimal paymentAmount;
    private String discountCurrency;
}
