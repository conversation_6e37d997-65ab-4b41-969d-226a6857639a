package vn.onepay.transaction.dto;

import java.math.BigDecimal;

public class PromotionResponse {
    private String promotionCode;
    private String promotionName;
    private String promotionCurrency;
    private BigDecimal promotionAmount;
    private BigDecimal promotionOriginalAmount;
    private BigDecimal promotionAmountDiscount;
    private String promotionGate;

    public PromotionResponse() {
    }

    public PromotionResponse(String promotionCode, String promotionName, String promotionCurrency, BigDecimal promotionAmount, BigDecimal promotionOriginalAmount, BigDecimal promotionAmountDiscount, String promotionGate) {
        this.promotionCode = promotionCode;
        this.promotionName = promotionName;
        this.promotionCurrency = promotionCurrency;
        this.promotionAmount = promotionAmount;
        this.promotionOriginalAmount = promotionOriginalAmount;
        this.promotionAmountDiscount = promotionAmountDiscount;
        this.promotionGate = promotionGate;
    }

    public String getPromotionCode() {
        return promotionCode;
    }

    public void setPromotionCode(String promotionCode) {
        this.promotionCode = promotionCode;
    }

    public String getPromotionName() {
        return promotionName;
    }

    public void setPromotionName(String promotionName) {
        this.promotionName = promotionName;
    }

    public String getPromotionCurrency() {
        return promotionCurrency;
    }

    public void setPromotionCurrency(String promotionCurrency) {
        this.promotionCurrency = promotionCurrency;
    }

    public BigDecimal getPromotionAmount() {
        return promotionAmount;
    }

    public void setPromotionAmount(BigDecimal promotionAmount) {
        this.promotionAmount = promotionAmount;
    }

    public BigDecimal getPromotionOriginalAmount() {
        return promotionOriginalAmount;
    }

    public void setPromotionOriginalAmount(BigDecimal promotionOriginalAmount) {
        this.promotionOriginalAmount = promotionOriginalAmount;
    }

    public BigDecimal getPromotionAmountDiscount() {
        return promotionAmountDiscount;
    }   

    public void setPromotionAmountDiscount(BigDecimal promotionAmountDiscount) {
        this.promotionAmountDiscount = promotionAmountDiscount;
    }

    public String getPromotionGate() {
        return promotionGate;
    }

    public void setPromotionGate(String promotionGate) {
        this.promotionGate = promotionGate;
    }
}
