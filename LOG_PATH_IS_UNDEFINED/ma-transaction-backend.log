2025-08-18 05:06:34.193 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 1179869 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-08-18 05:06:34.197 [main] INFO  v.o.t.TransactionAppApplication - The following 1 profile is active: "dev"
2025-08-18 05:06:35.036 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-08-18 05:06:35.261 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 217 ms. Found 2 R2DBC repository interfaces.
2025-08-18 05:06:37.038 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Starting...
2025-08-18 05:06:37.437 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleMerchantPortalHikariPool - Added connection oracle.jdbc.driver.T4CConnection@1d6f77d7
2025-08-18 05:06:37.440 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Start completed.
2025-08-18 05:06:37.519 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Starting...
2025-08-18 05:06:37.654 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleOneReportHikariPool - Added connection oracle.jdbc.driver.T4CConnection@a03529c
2025-08-18 05:06:37.654 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Start completed.
2025-08-18 05:06:37.805 [main] INFO  v.o.t.job.PromotionReportJob - Scheduling PromotionReportJob with cron: 0 05 16 * * *
2025-08-18 05:06:37.814 [main] INFO  v.o.t.job.PromotionReportJob - End PromotionReportJob!
2025-08-18 05:06:37.818 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-08-18 05:06:37.819 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-08-18 05:06:37.825 [main] INFO  v.o.transaction.job.UposReportJob - Scheduling UposReportJob with cron: 0 10 16 * * *
2025-08-18 05:06:37.827 [main] INFO  v.o.transaction.job.UposReportJob - End UposReportJob!
2025-08-18 05:06:38.878 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 8394 (http)
2025-08-18 05:06:39.084 [main] INFO  v.o.t.TransactionAppApplication - Started TransactionAppApplication in 5.605 seconds (process running for 7.512)
2025-08-18 05:09:10.943 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://localhost:8394/ma-service/api/v1/transactions/transaction/multiple/approve-reject
2025-08-18 05:09:10.944 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-08-18 05:09:10.944 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-08-18 05:09:10.944 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-08-18 05:09:10.944 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-08-18 05:09:10.944 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-08-18 05:09:10.944 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev18-ma.onepay.vn
2025-08-18 05:09:10.944 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-08-18 05:09:10.944 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev18-ma.onepay.vn/mav2-main/
2025-08-18 05:09:10.945 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-08-18 05:09:10.945 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-08-18 05:09:10.945 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-08-18 05:09:10.945 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-08-18 05:09:10.945 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-08-18 05:09:10.945 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-08-18 05:09:10.945 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-08-18 05:09:10.945 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 417604
2025-08-18 05:09:10.945 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.*********.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; JSESSIONID=dummy; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************.3Eg84dqXHjJ3ixV4g4NZFYS0-5c28TzIa5mju5_L1Lg
2025-08-18 05:09:10.945 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-18 05:09:10.945 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 6a798988-989a-4461-acd5-d462e13175f7
2025-08-18 05:09:10.945 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8394
2025-08-18 05:09:10.945 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-08-18 05:09:10.946 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 522
2025-08-18 05:09:10.949 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-08-18 05:09:35.975 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api approveOrRejectMultipleTransaction MA-----------
2025-08-18 05:09:35.991 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api approveOrRejectMultipleTransaction MA------------
2025-08-18 05:09:36.000 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=868849 ,paymentMethod=QT ,transactionType=Request Refund ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-18 05:09:36.000 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-08-18 05:09:36.061 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/868849 ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-18 05:09:36.145 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"transaction_id":"868849","original_transaction_id":"*********","transaction_type":"Request Refund","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"17","acquirer_name":"","acquirer_short_name":""},"transaction_reference":"TESTPCI_1755170440838","amount":{"total":1000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"512345****0008","card_type":"Mastercard","name_on_card":"123","card_date":{"month":"12","year":"25"}},"merchant_id":"TESTPCI","authentication":{},"transaction_time":"2025-08-14T18:20:40Z","transaction_status":"","transaction_ref_number":"*********","ip_address":"************","ip_proxy":"","bin_country":"","csc_result_code":"","verification_security_level":"","response_code":"","can_void":false,"risk_assesment":"Not Assessed","bank_id":"","wait_for_approval_amount":"0.00","advance_status":"Merchant Rejected","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"OTHER","acqResponseCode":"","status3ds":"","riskOverAllResult":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"123","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00","installment_statement_date":""}
2025-08-18 05:09:36.145 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-08-18 05:09:36.151 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-08-18 05:09:36.159 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-08-18 05:09:36.179 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-08-14T18:20:40, orderAmount=1000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=868849, parentTransactionId=*********, transactionType=Request Refund, paymentMethod=QT, transactionCreatedTime=2025-08-14T18:20:40, transactionCompletedTime=2025-08-14T18:20:40, transactionAmount=1000.0, transactionCurrency=VND, transactionStatus=Merchant Rejected, responseCode=, merchantTransRef=TESTPCI_1755170440838, paymentChannel=null, installmentStatus=), paymentMethod=PaymentMethod(cardNumber=512345****0008, cardBrand=null, cardType=Mastercard, issuer=, acquirer=17, cardExpiry=1225, nameOnCard=123, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, settlementAmount=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=InstallmentDetail(priceDifference=0, period=, amountMonthly=0.00), promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=************, ipProxy=, ipCountry=************, binCountry=, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null, amountAdvance=null), actions=null, histories=null)
2025-08-18 05:09:36.179 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-08-18 05:09:36.180 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/refund/868849 ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-18 05:09:36.194 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"row_num":"0","merchant_id":"TESTPCI","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"17","acquirer_name":"Vietinbank - MPGS","acquirer_short_name":"Vietinbank - MPGS"},"original_transaction_id":"*********","transaction_id":"868849","transaction_time":"2025-08-14T18:20:40Z","transaction_type":"Request Refund","transaction_status":"404","authentication":"","amount":{"currency":"VND","total":1000.0,"purchase_total":1000.0},"transaction_purchase_time":"2025-08-14T14:03:49Z","card":{"card_number":"512345****0008","card_type":"Mastercard","card_date":{"month":"12","year":"25"}},"csc_result_code":"","refund_type":"1"}
2025-08-18 05:09:36.195 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-08-18 05:09:36.195 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonRequestRefundIntenational...
2025-08-18 05:09:36.196 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonRequestRefundIntenational...
2025-08-18 05:09:36.196 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE REFUND INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-08-14T18:20:40, orderAmount=1000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=868849, parentTransactionId=*********, transactionType=Request Refund, paymentMethod=QT, transactionCreatedTime=2025-08-14T18:20:40, transactionCompletedTime=2025-08-14T18:20:40, transactionAmount=1000.0, transactionCurrency=VND, transactionStatus=404, responseCode=null, merchantTransRef=null, paymentChannel=null, installmentStatus=null), paymentMethod=PaymentMethod(cardNumber=512345****0008, cardBrand=null, cardType=Mastercard, issuer=Vietinbank - MPGS, acquirer=17, cardExpiry=null, nameOnCard=null, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, settlementAmount=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=null, promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=null), riskManagement=RiskManagement(ipAddress=************, ipProxy=, ipCountry=************, binCountry=, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null, amountAdvance=null), actions=null, histories=null)
2025-08-18 05:09:36.196 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-08-18 05:09:36.196 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getAdvanceFeeInfo ===================
2025-08-18 05:09:36.380 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-08-18 05:09:36.381 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-08-18 05:09:36.392 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=868850 ,paymentMethod=QT ,transactionType=Request Refund ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-18 05:09:36.393 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-08-18 05:09:36.394 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/868850 ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-18 05:09:36.408 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"transaction_id":"868850","original_transaction_id":"*********","transaction_type":"Request Refund","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"4","acquirer_name":"","acquirer_short_name":""},"transaction_reference":"TESTPCI_1755226501571","amount":{"total":1000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"531358****3430","card_type":"Mastercard","name_on_card":"123","card_date":{"month":"11","year":"26"}},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y"},"transaction_time":"2025-08-15T09:55:02Z","transaction_status":"","transaction_ref_number":"op3d20_vcb-82781","ip_address":"***************","ip_proxy":"","bin_country":"","csc_result_code":"","verification_security_level":"05","response_code":"","can_void":false,"risk_assesment":"Not Assessed","bank_id":"","wait_for_approval_amount":"0.00","advance_status":"Merchant Rejected","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"CREDIT","acqResponseCode":"","status3ds":"Y","riskOverAllResult":"ACCEPT","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"123","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00","installment_statement_date":""}
2025-08-18 05:09:36.408 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-08-18 05:09:36.409 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-08-18 05:09:36.409 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-08-18 05:09:36.410 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-08-15T09:55:02, orderAmount=1000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=868850, parentTransactionId=*********, transactionType=Request Refund, paymentMethod=QT, transactionCreatedTime=2025-08-15T09:55:02, transactionCompletedTime=2025-08-15T09:55:02, transactionAmount=1000.0, transactionCurrency=VND, transactionStatus=Merchant Rejected, responseCode=, merchantTransRef=TESTPCI_1755226501571, paymentChannel=null, installmentStatus=), paymentMethod=PaymentMethod(cardNumber=531358****3430, cardBrand=null, cardType=Mastercard, issuer=, acquirer=4, cardExpiry=1126, nameOnCard=123, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, settlementAmount=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=InstallmentDetail(priceDifference=0, period=, amountMonthly=0.00), promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=***************, ipProxy=, ipCountry=***************, binCountry=, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null, amountAdvance=null), actions=null, histories=null)
2025-08-18 05:09:36.410 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-08-18 05:09:36.411 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/refund/868850 ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-18 05:09:36.421 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-08-18 05:09:36.421 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - list merchant from permission: [OP_TESTVND, OP_VND01, OP_MPGSVND, OP_MPGSUSD, OP_MPAYVND3D, TESTBIDVV3, ONEPAYHM, D_TEST, OP_CYBSVND, TEST3DSMPGS, OP_MPAYVN1, TESTND, OP_TESTK7011, TESTHD3BEN, TESTSTATICQR, OP_TESTK4121, OP_SACOMTH, TESTAPLVNC, TESTORIFLCBS, TESTVCB_5411, OP_TESTK7399, TESTGGPAY, OP_TESTK5331, TESTKOVENA, TESTSTGINVOICE, TESTMERCHANT16KY, TESTOPVPB, TESTVTBCYBER, HUONG1, TESTOPBIDV, TESTTRAGOP, TESTPCI, TESTONEPAY, OP_TESTUSD, OP_SACOMTRVV, ONEPAY, INVOICETG, TESTDMX, TESTTNS, TESTINVOICE, TESTVCB3D2, TESTBIDV, TESTHC, TEST3DS2_LT, MIGS3DS, TESTAXA1, TESTVF, TESTUPOSTG, TESTAOSVNBANK, OP_TESTKBANK, TESTAPPLE, TESTVCBHM1, TESTTVLOKA, TESTVCB_7399, TESTVAGROUP, TESTVCB_6300, OP_TESTM8211, OP_TESTK7372, OP_TESTK7991, TESTPCI4, TESTOPVCB2, TESTSACOMCYBER, TESTUSD, OP_TESTVP5732, OP_TESTVP6300, ONEPAYMT, TESTATM, OPTEST, TESTTNSUSD, MPAYVND, TESTBIDV1, OP_VPBMPGS3, TESTAWPOSTG, TESTTRAGOP1, TESTSAMSUNG, TESTOP_VTB2, TESTUPOSKBANK, TESTAPLBANK, TESTVCB_8211, TESTMERCHANTID20KYTU, MONPAYOUT, TESTVIETQR, TESTINVOICETG, OP_TESTVP8211, TESTPAYOUT2, TEST, OP_SACOMCBSU, OP_VPBMPGS1, TESTONEPAY20, TESTVJU, OP_TESTCONFIG, OP_TESTSS, OP_TESTBHX, OPTESTHB, TESTAWPOS, TESTACVNC, OP_TESTK4900, TESTONEPAYTHB, ONEPAY_REFUND, TESTAPLTG, OP_TESTK5814, TESTVCB_4722, OP_TESTK5722, TESTPAYOUT1, TESTOPVCB, TESTIDTEST4, TESTONEPAYDVC, OP_SACOMCBSV, OP_SACOMTRVU, ****************, OPMPAY, FALSE, TESTDUONG, OP_VPBMPGS2, TESTSACOMCBU, TESTLZD, TESTSAPO, OP_TESTAUTH, TESTMEGA1, OP_TESTMID, TESTVTASABRE, OP_TESTK4722, TESTOP_VTB3, OP_SACOMVIN, OP_TESTK8220, TESTCNY, TESTVCB_4900, OP_TESTK5698, TESTPCI3, ANHTVTEST, TESTTOKEN, TESTONEPAY2, MONPAYCOLLECT, OP_TESTVP5411, TESTSHOPIFYTG, TESTOP_D, TESTONEPAYDVTT, TESTTRAGOP2, TESTBIDVU3, TESTBIDVV2, TESTTOKENUSD, TESTMEGA, TEST3DS2_MK, TEST3DS2_TN, TESTVFVND, TESTVTB3D2, TESTENETVIET, TESTSHOPIFY, TESTAPPLEND, TESTVCBHM2, TESTTRAGOP3, TESTOKENOP, TESTKONEVA, TESTOP_VTB1, AUTOAPLND, TESTSSPAY, TESTSAMSUNGPL, TESTVCB_5732, TESTAES, OP_TESTK7832, TESTPCI2, TESTJBAUSD, TESTVCBCYBER, TESTMERCHANT15K, OPPREPAID, TESTONEPAYUSD, OP_MPAYVND, TESTSACOMCBV, OP_VCBVND, TESTSAPO2, TESTBIDVCBSU, TESTVCB3D2O, TESTMAFC, OP_TESTOP20, TESTAXA, TESTTGDD3D2, TESTPR, TESTHC1, ONEPAYHB, TESTSSBNPL, OP_TESTK8299, TESTHD2BEN, TESTPCIEM, TESTMCD, OP_TESTK5732, TESTPROMMG, OP_TESTTT, TESTAUTOMSP, OP_TESTUSD2, OP_TESTAPPAY, TESTTHB, TESTSOS, TESTAPPLETG, OP_TESTK5977, EKKO1, TESTIDTEST6, TESTONEPAYDVDT, TESTBIDVCSU2, ****************, OP_VCBUSD, TESTBIDVCBSV, OP_TEST3DS, OP_TEST3DS2V, OP_CYBSUSD, TESTTHSC, TESTBNPL, TESTAOSVNC, TESTPROM, OP_SACOMECOM, TESTQRTINH, TESTTOKENDV, TESTUPOS, OP_TESTK5969, OP_TESTK5816, TESTONEPAY3, TESTPCI1, TESTKBANKCYBER, TESTVPBCYBER, TEST_OP2B, OP_TESTVP4900, TESTIDTEST3, TESTPAYPAL]
2025-08-18 05:09:36.421 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"row_num":"0","merchant_id":"TESTPCI","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"4","acquirer_name":"Vietcombank - CyberSource","acquirer_short_name":"Vietcombank - CyberSource"},"original_transaction_id":"*********","transaction_id":"868850","transaction_time":"2025-08-15T09:55:02Z","transaction_type":"Request Refund","transaction_status":"404","authentication":"","amount":{"currency":"VND","total":1000.0,"purchase_total":1000.0},"transaction_purchase_time":"2025-08-15T09:51:47Z","card":{"card_number":"531358****3430","card_type":"Mastercard","card_date":{"month":"11","year":"26"}},"csc_result_code":"","refund_type":"1"}
2025-08-18 05:09:36.421 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-08-18 05:09:36.422 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonRequestRefundIntenational...
2025-08-18 05:09:36.423 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-08-18 05:09:36.424 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonRequestRefundIntenational...
2025-08-18 05:09:36.425 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE REFUND INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-08-15T09:55:02, orderAmount=1000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=868850, parentTransactionId=*********, transactionType=Request Refund, paymentMethod=QT, transactionCreatedTime=2025-08-15T09:55:02, transactionCompletedTime=2025-08-15T09:55:02, transactionAmount=1000.0, transactionCurrency=VND, transactionStatus=404, responseCode=null, merchantTransRef=null, paymentChannel=null, installmentStatus=null), paymentMethod=PaymentMethod(cardNumber=531358****3430, cardBrand=null, cardType=Mastercard, issuer=Vietcombank - CyberSource, acquirer=4, cardExpiry=null, nameOnCard=null, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, settlementAmount=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=null, promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=null), riskManagement=RiskManagement(ipAddress=***************, ipProxy=, ipCountry=***************, binCountry=, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null, amountAdvance=null), actions=null, histories=null)
2025-08-18 05:09:36.425 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-08-18 05:09:36.425 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-08-18 05:09:36.425 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getAdvanceFeeInfo ===================
2025-08-18 05:09:36.428 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-08-18 05:09:36.428 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-08-18 05:09:36.435 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api approveOrRejectTransaction MA-----------
2025-08-18 05:09:36.436 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/refund/868849 ,transactionId868849 ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811 ,realIp=127.0.0.1 ,jsonData={"id":"868849","op":"replace","path":"/reject","value":{"merchant_id":"868849","transaction_reference":"TESTPCI_1755170440838","currency":"VND"},"skipCallSynchronize":false,"service":"QT"}
2025-08-18 05:09:36.450 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-08-18 05:09:36.451 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - list merchant from permission: [OP_TESTVND, OP_VND01, OP_MPGSVND, OP_MPGSUSD, OP_MPAYVND3D, TESTBIDVV3, ONEPAYHM, D_TEST, OP_CYBSVND, TEST3DSMPGS, OP_MPAYVN1, TESTND, OP_TESTK7011, TESTHD3BEN, TESTSTATICQR, OP_TESTK4121, OP_SACOMTH, TESTAPLVNC, TESTORIFLCBS, TESTVCB_5411, OP_TESTK7399, TESTGGPAY, OP_TESTK5331, TESTKOVENA, TESTSTGINVOICE, TESTMERCHANT16KY, TESTOPVPB, TESTVTBCYBER, HUONG1, TESTOPBIDV, TESTTRAGOP, TESTPCI, TESTONEPAY, OP_TESTUSD, OP_SACOMTRVV, ONEPAY, INVOICETG, TESTDMX, TESTTNS, TESTINVOICE, TESTVCB3D2, TESTBIDV, TESTHC, TEST3DS2_LT, MIGS3DS, TESTAXA1, TESTVF, TESTUPOSTG, TESTAOSVNBANK, OP_TESTKBANK, TESTAPPLE, TESTVCBHM1, TESTTVLOKA, TESTVCB_7399, TESTVAGROUP, TESTVCB_6300, OP_TESTM8211, OP_TESTK7372, OP_TESTK7991, TESTPCI4, TESTOPVCB2, TESTSACOMCYBER, TESTUSD, OP_TESTVP5732, OP_TESTVP6300, ONEPAYMT, TESTATM, OPTEST, TESTTNSUSD, MPAYVND, TESTBIDV1, OP_VPBMPGS3, TESTAWPOSTG, TESTTRAGOP1, TESTSAMSUNG, TESTOP_VTB2, TESTUPOSKBANK, TESTAPLBANK, TESTVCB_8211, TESTMERCHANTID20KYTU, MONPAYOUT, TESTVIETQR, TESTINVOICETG, OP_TESTVP8211, TESTPAYOUT2, TEST, OP_SACOMCBSU, OP_VPBMPGS1, TESTONEPAY20, TESTVJU, OP_TESTCONFIG, OP_TESTSS, OP_TESTBHX, OPTESTHB, TESTAWPOS, TESTACVNC, OP_TESTK4900, TESTONEPAYTHB, ONEPAY_REFUND, TESTAPLTG, OP_TESTK5814, TESTVCB_4722, OP_TESTK5722, TESTPAYOUT1, TESTOPVCB, TESTIDTEST4, TESTONEPAYDVC, OP_SACOMCBSV, OP_SACOMTRVU, ****************, OPMPAY, FALSE, TESTDUONG, OP_VPBMPGS2, TESTSACOMCBU, TESTLZD, TESTSAPO, OP_TESTAUTH, TESTMEGA1, OP_TESTMID, TESTVTASABRE, OP_TESTK4722, TESTOP_VTB3, OP_SACOMVIN, OP_TESTK8220, TESTCNY, TESTVCB_4900, OP_TESTK5698, TESTPCI3, ANHTVTEST, TESTTOKEN, TESTONEPAY2, MONPAYCOLLECT, OP_TESTVP5411, TESTSHOPIFYTG, TESTOP_D, TESTONEPAYDVTT, TESTTRAGOP2, TESTBIDVU3, TESTBIDVV2, TESTTOKENUSD, TESTMEGA, TEST3DS2_MK, TEST3DS2_TN, TESTVFVND, TESTVTB3D2, TESTENETVIET, TESTSHOPIFY, TESTAPPLEND, TESTVCBHM2, TESTTRAGOP3, TESTOKENOP, TESTKONEVA, TESTOP_VTB1, AUTOAPLND, TESTSSPAY, TESTSAMSUNGPL, TESTVCB_5732, TESTAES, OP_TESTK7832, TESTPCI2, TESTJBAUSD, TESTVCBCYBER, TESTMERCHANT15K, OPPREPAID, TESTONEPAYUSD, OP_MPAYVND, TESTSACOMCBV, OP_VCBVND, TESTSAPO2, TESTBIDVCBSU, TESTVCB3D2O, TESTMAFC, OP_TESTOP20, TESTAXA, TESTTGDD3D2, TESTPR, TESTHC1, ONEPAYHB, TESTSSBNPL, OP_TESTK8299, TESTHD2BEN, TESTPCIEM, TESTMCD, OP_TESTK5732, TESTPROMMG, OP_TESTTT, TESTAUTOMSP, OP_TESTUSD2, OP_TESTAPPAY, TESTTHB, TESTSOS, TESTAPPLETG, OP_TESTK5977, EKKO1, TESTIDTEST6, TESTONEPAYDVDT, TESTBIDVCSU2, ****************, OP_VCBUSD, TESTBIDVCBSV, OP_TEST3DS, OP_TEST3DS2V, OP_CYBSUSD, TESTTHSC, TESTBNPL, TESTAOSVNC, TESTPROM, OP_SACOMECOM, TESTQRTINH, TESTTOKENDV, TESTUPOS, OP_TESTK5969, OP_TESTK5816, TESTONEPAY3, TESTPCI1, TESTKBANKCYBER, TESTVPBCYBER, TEST_OP2B, OP_TESTVP4900, TESTIDTEST3, TESTPAYPAL]
2025-08-18 05:09:36.451 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-08-18 05:09:36.451 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-08-18 05:09:36.452 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api approveOrRejectTransaction MA-----------
2025-08-18 05:09:36.453 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/refund/868850 ,transactionId868850 ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811 ,realIp=127.0.0.1 ,jsonData={"id":"868850","op":"replace","path":"/reject","value":{"merchant_id":"868850","transaction_reference":"TESTPCI_1755226501571","currency":"VND"},"skipCallSynchronize":false,"service":"QT"}
2025-08-18 05:09:49.360 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api approveOrRejectTransaction MA------------
2025-08-18 05:09:49.402 [ForkJoinPool.commonPool-worker-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE REFUND API: response code=400 ,response body={"code":400,"message":"Invalid request"}
2025-08-18 05:33:13.814 [ForkJoinPool.commonPool-worker-1] ERROR v.o.t.s.i.TransactionOldServiceImpl - Approve or reject failed: 868850
2025-08-18 05:33:19.864 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api approveOrRejectTransaction MA------------
2025-08-18 05:33:19.885 [ForkJoinPool.commonPool-worker-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE REFUND API: response code=400 ,response body={"code":400,"message":"Invalid request"}
2025-08-18 05:33:24.502 [ForkJoinPool.commonPool-worker-1] ERROR v.o.t.s.i.TransactionOldServiceImpl - Approve or reject failed: 868849
2025-08-18 05:33:24.542 [ForkJoinPool.commonPool-worker-1] INFO  v.o.t.exception.RequestLoggingFilter - Response: PATCH http://localhost:8394/ma-service/api/v1/transactions/transaction/multiple/approve-reject - Status: 200 OK
2025-08-18 05:33:37.792 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://localhost:8394/ma-service/api/v1/transactions/transaction/multiple/approve-reject
2025-08-18 05:33:37.792 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-08-18 05:33:37.792 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-08-18 05:33:37.792 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-08-18 05:33:37.792 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-08-18 05:33:37.792 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-08-18 05:33:37.792 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev18-ma.onepay.vn
2025-08-18 05:33:37.792 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-08-18 05:33:37.792 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev18-ma.onepay.vn/mav2-main/
2025-08-18 05:33:37.792 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-08-18 05:33:37.792 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-08-18 05:33:37.793 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-08-18 05:33:37.793 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-08-18 05:33:37.793 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-08-18 05:33:37.793 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-08-18 05:33:37.793 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-08-18 05:33:37.793 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 417604
2025-08-18 05:33:37.793 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.*********.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; JSESSIONID=dummy; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************.3Eg84dqXHjJ3ixV4g4NZFYS0-5c28TzIa5mju5_L1Lg
2025-08-18 05:33:37.793 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-18 05:33:37.793 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 94e07841-b5cc-42c3-8533-8af6f5dc0a36
2025-08-18 05:33:37.793 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8394
2025-08-18 05:33:37.793 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-08-18 05:33:37.793 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 522
2025-08-18 05:33:37.793 [reactor-http-epoll-5] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-08-18 05:33:40.003 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api approveOrRejectMultipleTransaction MA-----------
2025-08-18 05:33:40.005 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api approveOrRejectMultipleTransaction MA------------
2025-08-18 05:33:40.006 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=868849 ,paymentMethod=QT ,transactionType=Request Refund ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-18 05:33:40.006 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-08-18 05:33:40.006 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/868849 ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-18 05:33:40.130 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"transaction_id":"868849","original_transaction_id":"*********","transaction_type":"Request Refund","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"17","acquirer_name":"","acquirer_short_name":""},"transaction_reference":"TESTPCI_1755170440838","amount":{"total":1000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"512345****0008","card_type":"Mastercard","name_on_card":"123","card_date":{"month":"12","year":"25"}},"merchant_id":"TESTPCI","authentication":{},"transaction_time":"2025-08-14T18:20:40Z","transaction_status":"","transaction_ref_number":"*********","ip_address":"************","ip_proxy":"","bin_country":"","csc_result_code":"","verification_security_level":"","response_code":"","can_void":false,"risk_assesment":"Not Assessed","bank_id":"","wait_for_approval_amount":"0.00","advance_status":"Merchant Rejected","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"OTHER","acqResponseCode":"","status3ds":"","riskOverAllResult":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"123","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00","installment_statement_date":""}
2025-08-18 05:33:40.130 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-08-18 05:33:40.131 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-08-18 05:33:40.132 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-08-18 05:33:40.132 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-08-14T18:20:40, orderAmount=1000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=868849, parentTransactionId=*********, transactionType=Request Refund, paymentMethod=QT, transactionCreatedTime=2025-08-14T18:20:40, transactionCompletedTime=2025-08-14T18:20:40, transactionAmount=1000.0, transactionCurrency=VND, transactionStatus=Merchant Rejected, responseCode=, merchantTransRef=TESTPCI_1755170440838, paymentChannel=null, installmentStatus=), paymentMethod=PaymentMethod(cardNumber=512345****0008, cardBrand=null, cardType=Mastercard, issuer=, acquirer=17, cardExpiry=1225, nameOnCard=123, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, settlementAmount=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=InstallmentDetail(priceDifference=0, period=, amountMonthly=0.00), promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=************, ipProxy=, ipCountry=************, binCountry=, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null, amountAdvance=null), actions=null, histories=null)
2025-08-18 05:33:40.132 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-08-18 05:33:40.133 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/refund/868849 ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-18 05:33:40.149 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"row_num":"0","merchant_id":"TESTPCI","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"17","acquirer_name":"Vietinbank - MPGS","acquirer_short_name":"Vietinbank - MPGS"},"original_transaction_id":"*********","transaction_id":"868849","transaction_time":"2025-08-14T18:20:40Z","transaction_type":"Request Refund","transaction_status":"404","authentication":"","amount":{"currency":"VND","total":1000.0,"purchase_total":1000.0},"transaction_purchase_time":"2025-08-14T14:03:49Z","card":{"card_number":"512345****0008","card_type":"Mastercard","card_date":{"month":"12","year":"25"}},"csc_result_code":"","refund_type":"1"}
2025-08-18 05:33:40.149 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-08-18 05:33:40.151 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonRequestRefundIntenational...
2025-08-18 05:33:40.152 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonRequestRefundIntenational...
2025-08-18 05:33:40.152 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE REFUND INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-08-14T18:20:40, orderAmount=1000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=868849, parentTransactionId=*********, transactionType=Request Refund, paymentMethod=QT, transactionCreatedTime=2025-08-14T18:20:40, transactionCompletedTime=2025-08-14T18:20:40, transactionAmount=1000.0, transactionCurrency=VND, transactionStatus=404, responseCode=null, merchantTransRef=null, paymentChannel=null, installmentStatus=null), paymentMethod=PaymentMethod(cardNumber=512345****0008, cardBrand=null, cardType=Mastercard, issuer=Vietinbank - MPGS, acquirer=17, cardExpiry=null, nameOnCard=null, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, settlementAmount=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=null, promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=null), riskManagement=RiskManagement(ipAddress=************, ipProxy=, ipCountry=************, binCountry=, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null, amountAdvance=null), actions=null, histories=null)
2025-08-18 05:33:40.152 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-08-18 05:33:40.152 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getAdvanceFeeInfo ===================
2025-08-18 05:33:40.160 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-08-18 05:33:40.160 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-08-18 05:33:40.161 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=868850 ,paymentMethod=QT ,transactionType=Request Refund ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-18 05:33:40.161 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-08-18 05:33:40.162 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/868850 ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-18 05:33:40.177 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"transaction_id":"868850","original_transaction_id":"*********","transaction_type":"Request Refund","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"4","acquirer_name":"","acquirer_short_name":""},"transaction_reference":"TESTPCI_1755226501571","amount":{"total":1000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"531358****3430","card_type":"Mastercard","name_on_card":"123","card_date":{"month":"11","year":"26"}},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y"},"transaction_time":"2025-08-15T09:55:02Z","transaction_status":"","transaction_ref_number":"op3d20_vcb-82781","ip_address":"***************","ip_proxy":"","bin_country":"","csc_result_code":"","verification_security_level":"05","response_code":"","can_void":false,"risk_assesment":"Not Assessed","bank_id":"","wait_for_approval_amount":"0.00","advance_status":"Merchant Rejected","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"CREDIT","acqResponseCode":"","status3ds":"Y","riskOverAllResult":"ACCEPT","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"123","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00","installment_statement_date":""}
2025-08-18 05:33:40.177 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-08-18 05:33:40.178 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-08-18 05:33:40.179 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-08-18 05:33:40.179 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-08-15T09:55:02, orderAmount=1000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=868850, parentTransactionId=*********, transactionType=Request Refund, paymentMethod=QT, transactionCreatedTime=2025-08-15T09:55:02, transactionCompletedTime=2025-08-15T09:55:02, transactionAmount=1000.0, transactionCurrency=VND, transactionStatus=Merchant Rejected, responseCode=, merchantTransRef=TESTPCI_1755226501571, paymentChannel=null, installmentStatus=), paymentMethod=PaymentMethod(cardNumber=531358****3430, cardBrand=null, cardType=Mastercard, issuer=, acquirer=4, cardExpiry=1126, nameOnCard=123, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, settlementAmount=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=InstallmentDetail(priceDifference=0, period=, amountMonthly=0.00), promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=***************, ipProxy=, ipCountry=***************, binCountry=, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null, amountAdvance=null), actions=null, histories=null)
2025-08-18 05:33:40.179 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-08-18 05:33:40.180 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/refund/868850 ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-18 05:33:40.182 [reactor-http-epoll-5] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-08-18 05:33:40.183 [reactor-http-epoll-5] INFO  v.o.t.service.MerchantService - list merchant from permission: [OP_TESTVND, OP_VND01, OP_MPGSVND, OP_MPGSUSD, OP_MPAYVND3D, TESTBIDVV3, ONEPAYHM, D_TEST, OP_CYBSVND, TEST3DSMPGS, OP_MPAYVN1, TESTND, OP_TESTK7011, TESTHD3BEN, TESTSTATICQR, OP_TESTK4121, OP_SACOMTH, TESTAPLVNC, TESTORIFLCBS, TESTVCB_5411, OP_TESTK7399, TESTGGPAY, OP_TESTK5331, TESTKOVENA, TESTSTGINVOICE, TESTMERCHANT16KY, TESTOPVPB, TESTVTBCYBER, HUONG1, TESTOPBIDV, TESTTRAGOP, TESTPCI, TESTONEPAY, OP_TESTUSD, OP_SACOMTRVV, ONEPAY, INVOICETG, TESTDMX, TESTTNS, TESTINVOICE, TESTVCB3D2, TESTBIDV, TESTHC, TEST3DS2_LT, MIGS3DS, TESTAXA1, TESTVF, TESTUPOSTG, TESTAOSVNBANK, OP_TESTKBANK, TESTAPPLE, TESTVCBHM1, TESTTVLOKA, TESTVCB_7399, TESTVAGROUP, TESTVCB_6300, OP_TESTM8211, OP_TESTK7372, OP_TESTK7991, TESTPCI4, TESTOPVCB2, TESTSACOMCYBER, TESTUSD, OP_TESTVP5732, OP_TESTVP6300, ONEPAYMT, TESTATM, OPTEST, TESTTNSUSD, MPAYVND, TESTBIDV1, OP_VPBMPGS3, TESTAWPOSTG, TESTTRAGOP1, TESTSAMSUNG, TESTOP_VTB2, TESTUPOSKBANK, TESTAPLBANK, TESTVCB_8211, TESTMERCHANTID20KYTU, MONPAYOUT, TESTVIETQR, TESTINVOICETG, OP_TESTVP8211, TESTPAYOUT2, TEST, OP_SACOMCBSU, OP_VPBMPGS1, TESTONEPAY20, TESTVJU, OP_TESTCONFIG, OP_TESTSS, OP_TESTBHX, OPTESTHB, TESTAWPOS, TESTACVNC, OP_TESTK4900, TESTONEPAYTHB, ONEPAY_REFUND, TESTAPLTG, OP_TESTK5814, TESTVCB_4722, OP_TESTK5722, TESTPAYOUT1, TESTOPVCB, TESTIDTEST4, TESTONEPAYDVC, OP_SACOMCBSV, OP_SACOMTRVU, ****************, OPMPAY, FALSE, TESTDUONG, OP_VPBMPGS2, TESTSACOMCBU, TESTLZD, TESTSAPO, OP_TESTAUTH, TESTMEGA1, OP_TESTMID, TESTVTASABRE, OP_TESTK4722, TESTOP_VTB3, OP_SACOMVIN, OP_TESTK8220, TESTCNY, TESTVCB_4900, OP_TESTK5698, TESTPCI3, ANHTVTEST, TESTTOKEN, TESTONEPAY2, MONPAYCOLLECT, OP_TESTVP5411, TESTSHOPIFYTG, TESTOP_D, TESTONEPAYDVTT, TESTTRAGOP2, TESTBIDVU3, TESTBIDVV2, TESTTOKENUSD, TESTMEGA, TEST3DS2_MK, TEST3DS2_TN, TESTVFVND, TESTVTB3D2, TESTENETVIET, TESTSHOPIFY, TESTAPPLEND, TESTVCBHM2, TESTTRAGOP3, TESTOKENOP, TESTKONEVA, TESTOP_VTB1, AUTOAPLND, TESTSSPAY, TESTSAMSUNGPL, TESTVCB_5732, TESTAES, OP_TESTK7832, TESTPCI2, TESTJBAUSD, TESTVCBCYBER, TESTMERCHANT15K, OPPREPAID, TESTONEPAYUSD, OP_MPAYVND, TESTSACOMCBV, OP_VCBVND, TESTSAPO2, TESTBIDVCBSU, TESTVCB3D2O, TESTMAFC, OP_TESTOP20, TESTAXA, TESTTGDD3D2, TESTPR, TESTHC1, ONEPAYHB, TESTSSBNPL, OP_TESTK8299, TESTHD2BEN, TESTPCIEM, TESTMCD, OP_TESTK5732, TESTPROMMG, OP_TESTTT, TESTAUTOMSP, OP_TESTUSD2, OP_TESTAPPAY, TESTTHB, TESTSOS, TESTAPPLETG, OP_TESTK5977, EKKO1, TESTIDTEST6, TESTONEPAYDVDT, TESTBIDVCSU2, ****************, OP_VCBUSD, TESTBIDVCBSV, OP_TEST3DS, OP_TEST3DS2V, OP_CYBSUSD, TESTTHSC, TESTBNPL, TESTAOSVNC, TESTPROM, OP_SACOMECOM, TESTQRTINH, TESTTOKENDV, TESTUPOS, OP_TESTK5969, OP_TESTK5816, TESTONEPAY3, TESTPCI1, TESTKBANKCYBER, TESTVPBCYBER, TEST_OP2B, OP_TESTVP4900, TESTIDTEST3, TESTPAYPAL]
2025-08-18 05:33:40.183 [reactor-http-epoll-5] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-08-18 05:33:40.183 [reactor-http-epoll-5] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-08-18 05:33:40.184 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api approveOrRejectTransaction MA-----------
2025-08-18 05:33:40.184 [reactor-http-epoll-5] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/refund/868849 ,transactionId868849 ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811 ,realIp=127.0.0.1 ,jsonData={"id":"868849","op":"replace","path":"/reject","value":{"merchant_id":"868849","transaction_reference":"TESTPCI_1755170440838","currency":"VND"},"skipCallSynchronize":false,"service":"QT"}
2025-08-18 05:33:40.195 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"row_num":"0","merchant_id":"TESTPCI","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"4","acquirer_name":"Vietcombank - CyberSource","acquirer_short_name":"Vietcombank - CyberSource"},"original_transaction_id":"*********","transaction_id":"868850","transaction_time":"2025-08-15T09:55:02Z","transaction_type":"Request Refund","transaction_status":"404","authentication":"","amount":{"currency":"VND","total":1000.0,"purchase_total":1000.0},"transaction_purchase_time":"2025-08-15T09:51:47Z","card":{"card_number":"531358****3430","card_type":"Mastercard","card_date":{"month":"11","year":"26"}},"csc_result_code":"","refund_type":"1"}
2025-08-18 05:33:40.195 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-08-18 05:33:40.195 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonRequestRefundIntenational...
2025-08-18 05:33:40.196 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonRequestRefundIntenational...
2025-08-18 05:33:40.196 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE REFUND INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-08-15T09:55:02, orderAmount=1000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=868850, parentTransactionId=*********, transactionType=Request Refund, paymentMethod=QT, transactionCreatedTime=2025-08-15T09:55:02, transactionCompletedTime=2025-08-15T09:55:02, transactionAmount=1000.0, transactionCurrency=VND, transactionStatus=404, responseCode=null, merchantTransRef=null, paymentChannel=null, installmentStatus=null), paymentMethod=PaymentMethod(cardNumber=531358****3430, cardBrand=null, cardType=Mastercard, issuer=Vietcombank - CyberSource, acquirer=4, cardExpiry=null, nameOnCard=null, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, settlementAmount=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=null, promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=null), riskManagement=RiskManagement(ipAddress=***************, ipProxy=, ipCountry=***************, binCountry=, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null, amountAdvance=null), actions=null, histories=null)
2025-08-18 05:33:40.196 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-08-18 05:33:40.196 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getAdvanceFeeInfo ===================
2025-08-18 05:33:40.199 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-08-18 05:33:40.199 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-08-18 05:33:40.230 [reactor-http-epoll-6] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-08-18 05:33:40.231 [reactor-http-epoll-6] INFO  v.o.t.service.MerchantService - list merchant from permission: [OP_TESTVND, OP_VND01, OP_MPGSVND, OP_MPGSUSD, OP_MPAYVND3D, TESTBIDVV3, ONEPAYHM, D_TEST, OP_CYBSVND, TEST3DSMPGS, OP_MPAYVN1, TESTND, OP_TESTK7011, TESTHD3BEN, TESTSTATICQR, OP_TESTK4121, OP_SACOMTH, TESTAPLVNC, TESTORIFLCBS, TESTVCB_5411, OP_TESTK7399, TESTGGPAY, OP_TESTK5331, TESTKOVENA, TESTSTGINVOICE, TESTMERCHANT16KY, TESTOPVPB, TESTVTBCYBER, HUONG1, TESTOPBIDV, TESTTRAGOP, TESTPCI, TESTONEPAY, OP_TESTUSD, OP_SACOMTRVV, ONEPAY, INVOICETG, TESTDMX, TESTTNS, TESTINVOICE, TESTVCB3D2, TESTBIDV, TESTHC, TEST3DS2_LT, MIGS3DS, TESTAXA1, TESTVF, TESTUPOSTG, TESTAOSVNBANK, OP_TESTKBANK, TESTAPPLE, TESTVCBHM1, TESTTVLOKA, TESTVCB_7399, TESTVAGROUP, TESTVCB_6300, OP_TESTM8211, OP_TESTK7372, OP_TESTK7991, TESTPCI4, TESTOPVCB2, TESTSACOMCYBER, TESTUSD, OP_TESTVP5732, OP_TESTVP6300, ONEPAYMT, TESTATM, OPTEST, TESTTNSUSD, MPAYVND, TESTBIDV1, OP_VPBMPGS3, TESTAWPOSTG, TESTTRAGOP1, TESTSAMSUNG, TESTOP_VTB2, TESTUPOSKBANK, TESTAPLBANK, TESTVCB_8211, TESTMERCHANTID20KYTU, MONPAYOUT, TESTVIETQR, TESTINVOICETG, OP_TESTVP8211, TESTPAYOUT2, TEST, OP_SACOMCBSU, OP_VPBMPGS1, TESTONEPAY20, TESTVJU, OP_TESTCONFIG, OP_TESTSS, OP_TESTBHX, OPTESTHB, TESTAWPOS, TESTACVNC, OP_TESTK4900, TESTONEPAYTHB, ONEPAY_REFUND, TESTAPLTG, OP_TESTK5814, TESTVCB_4722, OP_TESTK5722, TESTPAYOUT1, TESTOPVCB, TESTIDTEST4, TESTONEPAYDVC, OP_SACOMCBSV, OP_SACOMTRVU, ****************, OPMPAY, FALSE, TESTDUONG, OP_VPBMPGS2, TESTSACOMCBU, TESTLZD, TESTSAPO, OP_TESTAUTH, TESTMEGA1, OP_TESTMID, TESTVTASABRE, OP_TESTK4722, TESTOP_VTB3, OP_SACOMVIN, OP_TESTK8220, TESTCNY, TESTVCB_4900, OP_TESTK5698, TESTPCI3, ANHTVTEST, TESTTOKEN, TESTONEPAY2, MONPAYCOLLECT, OP_TESTVP5411, TESTSHOPIFYTG, TESTOP_D, TESTONEPAYDVTT, TESTTRAGOP2, TESTBIDVU3, TESTBIDVV2, TESTTOKENUSD, TESTMEGA, TEST3DS2_MK, TEST3DS2_TN, TESTVFVND, TESTVTB3D2, TESTENETVIET, TESTSHOPIFY, TESTAPPLEND, TESTVCBHM2, TESTTRAGOP3, TESTOKENOP, TESTKONEVA, TESTOP_VTB1, AUTOAPLND, TESTSSPAY, TESTSAMSUNGPL, TESTVCB_5732, TESTAES, OP_TESTK7832, TESTPCI2, TESTJBAUSD, TESTVCBCYBER, TESTMERCHANT15K, OPPREPAID, TESTONEPAYUSD, OP_MPAYVND, TESTSACOMCBV, OP_VCBVND, TESTSAPO2, TESTBIDVCBSU, TESTVCB3D2O, TESTMAFC, OP_TESTOP20, TESTAXA, TESTTGDD3D2, TESTPR, TESTHC1, ONEPAYHB, TESTSSBNPL, OP_TESTK8299, TESTHD2BEN, TESTPCIEM, TESTMCD, OP_TESTK5732, TESTPROMMG, OP_TESTTT, TESTAUTOMSP, OP_TESTUSD2, OP_TESTAPPAY, TESTTHB, TESTSOS, TESTAPPLETG, OP_TESTK5977, EKKO1, TESTIDTEST6, TESTONEPAYDVDT, TESTBIDVCSU2, ****************, OP_VCBUSD, TESTBIDVCBSV, OP_TEST3DS, OP_TEST3DS2V, OP_CYBSUSD, TESTTHSC, TESTBNPL, TESTAOSVNC, TESTPROM, OP_SACOMECOM, TESTQRTINH, TESTTOKENDV, TESTUPOS, OP_TESTK5969, OP_TESTK5816, TESTONEPAY3, TESTPCI1, TESTKBANKCYBER, TESTVPBCYBER, TEST_OP2B, OP_TESTVP4900, TESTIDTEST3, TESTPAYPAL]
2025-08-18 05:33:40.231 [reactor-http-epoll-6] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-08-18 05:33:40.231 [reactor-http-epoll-6] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-08-18 05:33:40.231 [reactor-http-epoll-6] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api approveOrRejectTransaction MA-----------
2025-08-18 05:33:40.232 [reactor-http-epoll-6] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/refund/868850 ,transactionId868850 ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811 ,realIp=127.0.0.1 ,jsonData={"id":"868850","op":"replace","path":"/reject","value":{"merchant_id":"868850","transaction_reference":"TESTPCI_1755226501571","currency":"VND"},"skipCallSynchronize":false,"service":"QT"}
2025-08-18 05:33:41.697 [reactor-http-epoll-6] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api approveOrRejectTransaction MA------------
2025-08-18 05:33:41.717 [ForkJoinPool.commonPool-worker-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE REFUND API: response code=400 ,response body={"code":400,"message":"Invalid request"}
2025-08-18 05:34:19.175 [ForkJoinPool.commonPool-worker-1] ERROR v.o.t.s.i.TransactionOldServiceImpl - Approve or reject failed: 868850
2025-08-18 05:34:50.902 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 1190616 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-08-18 05:34:50.904 [main] INFO  v.o.t.TransactionAppApplication - The following 1 profile is active: "dev"
2025-08-18 05:34:51.661 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-08-18 05:34:51.829 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 161 ms. Found 2 R2DBC repository interfaces.
2025-08-18 05:34:53.412 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Starting...
2025-08-18 05:34:53.680 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleMerchantPortalHikariPool - Added connection oracle.jdbc.driver.T4CConnection@7e5e6573
2025-08-18 05:34:53.682 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Start completed.
2025-08-18 05:34:53.753 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Starting...
2025-08-18 05:34:53.866 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleOneReportHikariPool - Added connection oracle.jdbc.driver.T4CConnection@6ec3d8e4
2025-08-18 05:34:53.866 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Start completed.
2025-08-18 05:34:54.014 [main] INFO  v.o.t.job.PromotionReportJob - Scheduling PromotionReportJob with cron: 0 05 16 * * *
2025-08-18 05:34:54.025 [main] INFO  v.o.t.job.PromotionReportJob - End PromotionReportJob!
2025-08-18 05:34:54.028 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-08-18 05:34:54.029 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-08-18 05:34:54.032 [main] INFO  v.o.transaction.job.UposReportJob - Scheduling UposReportJob with cron: 0 10 16 * * *
2025-08-18 05:34:54.033 [main] INFO  v.o.transaction.job.UposReportJob - End UposReportJob!
2025-08-18 05:34:54.882 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 8394 (http)
2025-08-18 05:34:55.080 [main] INFO  v.o.t.TransactionAppApplication - Started TransactionAppApplication in 4.776 seconds (process running for 6.3)
2025-08-18 05:35:02.676 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://localhost:8394/ma-service/api/v1/transactions/transaction/multiple/approve-reject
2025-08-18 05:35:02.677 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-08-18 05:35:02.677 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-08-18 05:35:02.677 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-08-18 05:35:02.677 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-08-18 05:35:02.677 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-08-18 05:35:02.677 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev18-ma.onepay.vn
2025-08-18 05:35:02.677 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-08-18 05:35:02.677 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev18-ma.onepay.vn/mav2-main/
2025-08-18 05:35:02.677 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-08-18 05:35:02.677 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-08-18 05:35:02.677 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-08-18 05:35:02.677 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-08-18 05:35:02.677 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-08-18 05:35:02.678 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-08-18 05:35:02.678 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-08-18 05:35:02.678 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 417604
2025-08-18 05:35:02.678 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.*********.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; JSESSIONID=dummy; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************.3Eg84dqXHjJ3ixV4g4NZFYS0-5c28TzIa5mju5_L1Lg
2025-08-18 05:35:02.678 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-18 05:35:02.678 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = e48c2a8b-c466-42ad-8881-6d909cb8e339
2025-08-18 05:35:02.678 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8394
2025-08-18 05:35:02.678 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-08-18 05:35:02.678 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 522
2025-08-18 05:35:02.682 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-08-18 05:35:05.896 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api approveOrRejectMultipleTransaction MA-----------
2025-08-18 05:35:05.911 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api approveOrRejectMultipleTransaction MA------------
2025-08-18 05:35:05.919 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=868849 ,paymentMethod=QT ,transactionType=Request Refund ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-18 05:35:05.919 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-08-18 05:35:05.976 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/868849 ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-18 05:35:06.053 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"transaction_id":"868849","original_transaction_id":"*********","transaction_type":"Request Refund","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"17","acquirer_name":"","acquirer_short_name":""},"transaction_reference":"TESTPCI_1755170440838","amount":{"total":1000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"512345****0008","card_type":"Mastercard","name_on_card":"123","card_date":{"month":"12","year":"25"}},"merchant_id":"TESTPCI","authentication":{},"transaction_time":"2025-08-14T18:20:40Z","transaction_status":"","transaction_ref_number":"*********","ip_address":"************","ip_proxy":"","bin_country":"","csc_result_code":"","verification_security_level":"","response_code":"","can_void":false,"risk_assesment":"Not Assessed","bank_id":"","wait_for_approval_amount":"0.00","advance_status":"Merchant Rejected","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"OTHER","acqResponseCode":"","status3ds":"","riskOverAllResult":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"123","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00","installment_statement_date":""}
2025-08-18 05:35:06.054 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-08-18 05:35:06.059 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-08-18 05:35:06.067 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-08-18 05:35:06.085 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-08-14T18:20:40, orderAmount=1000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=868849, parentTransactionId=*********, transactionType=Request Refund, paymentMethod=QT, transactionCreatedTime=2025-08-14T18:20:40, transactionCompletedTime=2025-08-14T18:20:40, transactionAmount=1000.0, transactionCurrency=VND, transactionStatus=Merchant Rejected, responseCode=, merchantTransRef=TESTPCI_1755170440838, paymentChannel=null, installmentStatus=), paymentMethod=PaymentMethod(cardNumber=512345****0008, cardBrand=null, cardType=Mastercard, issuer=, acquirer=17, cardExpiry=1225, nameOnCard=123, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, settlementAmount=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=InstallmentDetail(priceDifference=0, period=, amountMonthly=0.00), promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=************, ipProxy=, ipCountry=************, binCountry=, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null, amountAdvance=null), actions=null, histories=null)
2025-08-18 05:35:06.085 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-08-18 05:35:06.085 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/refund/868849 ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-18 05:35:06.108 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"row_num":"0","merchant_id":"TESTPCI","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"17","acquirer_name":"Vietinbank - MPGS","acquirer_short_name":"Vietinbank - MPGS"},"original_transaction_id":"*********","transaction_id":"868849","transaction_time":"2025-08-14T18:20:40Z","transaction_type":"Request Refund","transaction_status":"404","authentication":"","amount":{"currency":"VND","total":1000.0,"purchase_total":1000.0},"transaction_purchase_time":"2025-08-14T14:03:49Z","card":{"card_number":"512345****0008","card_type":"Mastercard","card_date":{"month":"12","year":"25"}},"csc_result_code":"","refund_type":"1"}
2025-08-18 05:35:06.108 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-08-18 05:35:06.109 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonRequestRefundIntenational...
2025-08-18 05:35:06.109 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonRequestRefundIntenational...
2025-08-18 05:35:06.109 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE REFUND INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-08-14T18:20:40, orderAmount=1000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=868849, parentTransactionId=*********, transactionType=Request Refund, paymentMethod=QT, transactionCreatedTime=2025-08-14T18:20:40, transactionCompletedTime=2025-08-14T18:20:40, transactionAmount=1000.0, transactionCurrency=VND, transactionStatus=404, responseCode=null, merchantTransRef=null, paymentChannel=null, installmentStatus=null), paymentMethod=PaymentMethod(cardNumber=512345****0008, cardBrand=null, cardType=Mastercard, issuer=Vietinbank - MPGS, acquirer=17, cardExpiry=null, nameOnCard=null, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, settlementAmount=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=null, promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=null), riskManagement=RiskManagement(ipAddress=************, ipProxy=, ipCountry=************, binCountry=, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null, amountAdvance=null), actions=null, histories=null)
2025-08-18 05:35:06.109 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-08-18 05:35:06.110 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getAdvanceFeeInfo ===================
2025-08-18 05:35:06.290 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-08-18 05:35:06.291 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-08-18 05:35:06.306 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=868850 ,paymentMethod=QT ,transactionType=Request Refund ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-18 05:35:06.306 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-08-18 05:35:06.307 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/868850 ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-18 05:35:06.327 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"transaction_id":"868850","original_transaction_id":"*********","transaction_type":"Request Refund","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"4","acquirer_name":"","acquirer_short_name":""},"transaction_reference":"TESTPCI_1755226501571","amount":{"total":1000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"531358****3430","card_type":"Mastercard","name_on_card":"123","card_date":{"month":"11","year":"26"}},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y"},"transaction_time":"2025-08-15T09:55:02Z","transaction_status":"","transaction_ref_number":"op3d20_vcb-82781","ip_address":"***************","ip_proxy":"","bin_country":"","csc_result_code":"","verification_security_level":"05","response_code":"","can_void":false,"risk_assesment":"Not Assessed","bank_id":"","wait_for_approval_amount":"0.00","advance_status":"Merchant Rejected","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"CREDIT","acqResponseCode":"","status3ds":"Y","riskOverAllResult":"ACCEPT","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"123","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00","installment_statement_date":""}
2025-08-18 05:35:06.327 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-08-18 05:35:06.328 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-08-18 05:35:06.329 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-08-18 05:35:06.329 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-08-15T09:55:02, orderAmount=1000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=868850, parentTransactionId=*********, transactionType=Request Refund, paymentMethod=QT, transactionCreatedTime=2025-08-15T09:55:02, transactionCompletedTime=2025-08-15T09:55:02, transactionAmount=1000.0, transactionCurrency=VND, transactionStatus=Merchant Rejected, responseCode=, merchantTransRef=TESTPCI_1755226501571, paymentChannel=null, installmentStatus=), paymentMethod=PaymentMethod(cardNumber=531358****3430, cardBrand=null, cardType=Mastercard, issuer=, acquirer=4, cardExpiry=1126, nameOnCard=123, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, settlementAmount=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=InstallmentDetail(priceDifference=0, period=, amountMonthly=0.00), promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=***************, ipProxy=, ipCountry=***************, binCountry=, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null, amountAdvance=null), actions=null, histories=null)
2025-08-18 05:35:06.329 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-08-18 05:35:06.330 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/refund/868850 ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-18 05:35:06.346 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"row_num":"0","merchant_id":"TESTPCI","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"4","acquirer_name":"Vietcombank - CyberSource","acquirer_short_name":"Vietcombank - CyberSource"},"original_transaction_id":"*********","transaction_id":"868850","transaction_time":"2025-08-15T09:55:02Z","transaction_type":"Request Refund","transaction_status":"404","authentication":"","amount":{"currency":"VND","total":1000.0,"purchase_total":1000.0},"transaction_purchase_time":"2025-08-15T09:51:47Z","card":{"card_number":"531358****3430","card_type":"Mastercard","card_date":{"month":"11","year":"26"}},"csc_result_code":"","refund_type":"1"}
2025-08-18 05:35:06.346 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-08-18 05:35:06.346 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonRequestRefundIntenational...
2025-08-18 05:35:06.347 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonRequestRefundIntenational...
2025-08-18 05:35:06.347 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE REFUND INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-08-15T09:55:02, orderAmount=1000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=868850, parentTransactionId=*********, transactionType=Request Refund, paymentMethod=QT, transactionCreatedTime=2025-08-15T09:55:02, transactionCompletedTime=2025-08-15T09:55:02, transactionAmount=1000.0, transactionCurrency=VND, transactionStatus=404, responseCode=null, merchantTransRef=null, paymentChannel=null, installmentStatus=null), paymentMethod=PaymentMethod(cardNumber=531358****3430, cardBrand=null, cardType=Mastercard, issuer=Vietcombank - CyberSource, acquirer=4, cardExpiry=null, nameOnCard=null, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, settlementAmount=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=null, promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=null), riskManagement=RiskManagement(ipAddress=***************, ipProxy=, ipCountry=***************, binCountry=, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null, amountAdvance=null), actions=null, histories=null)
2025-08-18 05:35:06.347 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-08-18 05:35:06.347 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getAdvanceFeeInfo ===================
2025-08-18 05:35:06.350 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-08-18 05:35:06.350 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-08-18 05:35:06.370 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-08-18 05:35:06.370 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - list merchant from permission: [OP_TESTVND, OP_VND01, OP_MPGSVND, OP_MPGSUSD, OP_MPAYVND3D, TESTBIDVV3, ONEPAYHM, D_TEST, OP_CYBSVND, TEST3DSMPGS, OP_MPAYVN1, TESTND, OP_TESTK7011, TESTHD3BEN, TESTSTATICQR, OP_TESTK4121, OP_SACOMTH, TESTAPLVNC, TESTORIFLCBS, TESTVCB_5411, OP_TESTK7399, TESTGGPAY, OP_TESTK5331, TESTKOVENA, TESTSTGINVOICE, TESTMERCHANT16KY, TESTOPVPB, TESTVTBCYBER, HUONG1, TESTOPBIDV, TESTTRAGOP, TESTPCI, TESTONEPAY, OP_TESTUSD, OP_SACOMTRVV, ONEPAY, INVOICETG, TESTDMX, TESTTNS, TESTINVOICE, TESTVCB3D2, TESTBIDV, TESTHC, TEST3DS2_LT, MIGS3DS, TESTAXA1, TESTVF, TESTUPOSTG, TESTAOSVNBANK, OP_TESTKBANK, TESTAPPLE, TESTVCBHM1, TESTTVLOKA, TESTVCB_7399, TESTVAGROUP, TESTVCB_6300, OP_TESTM8211, OP_TESTK7372, OP_TESTK7991, TESTPCI4, TESTOPVCB2, TESTSACOMCYBER, TESTUSD, OP_TESTVP5732, OP_TESTVP6300, ONEPAYMT, TESTATM, OPTEST, TESTTNSUSD, MPAYVND, TESTBIDV1, OP_VPBMPGS3, TESTAWPOSTG, TESTTRAGOP1, TESTSAMSUNG, TESTOP_VTB2, TESTUPOSKBANK, TESTAPLBANK, TESTVCB_8211, TESTMERCHANTID20KYTU, MONPAYOUT, TESTVIETQR, TESTINVOICETG, OP_TESTVP8211, TESTPAYOUT2, TEST, OP_SACOMCBSU, OP_VPBMPGS1, TESTONEPAY20, TESTVJU, OP_TESTCONFIG, OP_TESTSS, OP_TESTBHX, OPTESTHB, TESTAWPOS, TESTACVNC, OP_TESTK4900, TESTONEPAYTHB, ONEPAY_REFUND, TESTAPLTG, OP_TESTK5814, TESTVCB_4722, OP_TESTK5722, TESTPAYOUT1, TESTOPVCB, TESTIDTEST4, TESTONEPAYDVC, OP_SACOMCBSV, OP_SACOMTRVU, ****************, OPMPAY, FALSE, TESTDUONG, OP_VPBMPGS2, TESTSACOMCBU, TESTLZD, TESTSAPO, OP_TESTAUTH, TESTMEGA1, OP_TESTMID, TESTVTASABRE, OP_TESTK4722, TESTOP_VTB3, OP_SACOMVIN, OP_TESTK8220, TESTCNY, TESTVCB_4900, OP_TESTK5698, TESTPCI3, ANHTVTEST, TESTTOKEN, TESTONEPAY2, MONPAYCOLLECT, OP_TESTVP5411, TESTSHOPIFYTG, TESTOP_D, TESTONEPAYDVTT, TESTTRAGOP2, TESTBIDVU3, TESTBIDVV2, TESTTOKENUSD, TESTMEGA, TEST3DS2_MK, TEST3DS2_TN, TESTVFVND, TESTVTB3D2, TESTENETVIET, TESTSHOPIFY, TESTAPPLEND, TESTVCBHM2, TESTTRAGOP3, TESTOKENOP, TESTKONEVA, TESTOP_VTB1, AUTOAPLND, TESTSSPAY, TESTSAMSUNGPL, TESTVCB_5732, TESTAES, OP_TESTK7832, TESTPCI2, TESTJBAUSD, TESTVCBCYBER, TESTMERCHANT15K, OPPREPAID, TESTONEPAYUSD, OP_MPAYVND, TESTSACOMCBV, OP_VCBVND, TESTSAPO2, TESTBIDVCBSU, TESTVCB3D2O, TESTMAFC, OP_TESTOP20, TESTAXA, TESTTGDD3D2, TESTPR, TESTHC1, ONEPAYHB, TESTSSBNPL, OP_TESTK8299, TESTHD2BEN, TESTPCIEM, TESTMCD, OP_TESTK5732, TESTPROMMG, OP_TESTTT, TESTAUTOMSP, OP_TESTUSD2, OP_TESTAPPAY, TESTTHB, TESTSOS, TESTAPPLETG, OP_TESTK5977, EKKO1, TESTIDTEST6, TESTONEPAYDVDT, TESTBIDVCSU2, ****************, OP_VCBUSD, TESTBIDVCBSV, OP_TEST3DS, OP_TEST3DS2V, OP_CYBSUSD, TESTTHSC, TESTBNPL, TESTAOSVNC, TESTPROM, OP_SACOMECOM, TESTQRTINH, TESTTOKENDV, TESTUPOS, OP_TESTK5969, OP_TESTK5816, TESTONEPAY3, TESTPCI1, TESTKBANKCYBER, TESTVPBCYBER, TEST_OP2B, OP_TESTVP4900, TESTIDTEST3, TESTPAYPAL]
2025-08-18 05:35:06.373 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-08-18 05:35:06.375 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-08-18 05:35:06.386 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api approveOrRejectTransaction MA-----------
2025-08-18 05:35:06.387 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/refund/868849 ,transactionId868849 ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811 ,realIp=127.0.0.1 ,jsonData={"id":"868849","op":"replace","path":"/reject","value":{"merchant_id":"868849","transaction_reference":"TESTPCI_1755170440838","currency":"VND"},"skipCallSynchronize":false,"service":"QT"}
2025-08-18 05:35:06.391 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api approveOrRejectTransaction MA------------
2025-08-18 05:35:06.412 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-08-18 05:35:06.412 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - list merchant from permission: [OP_TESTVND, OP_VND01, OP_MPGSVND, OP_MPGSUSD, OP_MPAYVND3D, TESTBIDVV3, ONEPAYHM, D_TEST, OP_CYBSVND, TEST3DSMPGS, OP_MPAYVN1, TESTND, OP_TESTK7011, TESTHD3BEN, TESTSTATICQR, OP_TESTK4121, OP_SACOMTH, TESTAPLVNC, TESTORIFLCBS, TESTVCB_5411, OP_TESTK7399, TESTGGPAY, OP_TESTK5331, TESTKOVENA, TESTSTGINVOICE, TESTMERCHANT16KY, TESTOPVPB, TESTVTBCYBER, HUONG1, TESTOPBIDV, TESTTRAGOP, TESTPCI, TESTONEPAY, OP_TESTUSD, OP_SACOMTRVV, ONEPAY, INVOICETG, TESTDMX, TESTTNS, TESTINVOICE, TESTVCB3D2, TESTBIDV, TESTHC, TEST3DS2_LT, MIGS3DS, TESTAXA1, TESTVF, TESTUPOSTG, TESTAOSVNBANK, OP_TESTKBANK, TESTAPPLE, TESTVCBHM1, TESTTVLOKA, TESTVCB_7399, TESTVAGROUP, TESTVCB_6300, OP_TESTM8211, OP_TESTK7372, OP_TESTK7991, TESTPCI4, TESTOPVCB2, TESTSACOMCYBER, TESTUSD, OP_TESTVP5732, OP_TESTVP6300, ONEPAYMT, TESTATM, OPTEST, TESTTNSUSD, MPAYVND, TESTBIDV1, OP_VPBMPGS3, TESTAWPOSTG, TESTTRAGOP1, TESTSAMSUNG, TESTOP_VTB2, TESTUPOSKBANK, TESTAPLBANK, TESTVCB_8211, TESTMERCHANTID20KYTU, MONPAYOUT, TESTVIETQR, TESTINVOICETG, OP_TESTVP8211, TESTPAYOUT2, TEST, OP_SACOMCBSU, OP_VPBMPGS1, TESTONEPAY20, TESTVJU, OP_TESTCONFIG, OP_TESTSS, OP_TESTBHX, OPTESTHB, TESTAWPOS, TESTACVNC, OP_TESTK4900, TESTONEPAYTHB, ONEPAY_REFUND, TESTAPLTG, OP_TESTK5814, TESTVCB_4722, OP_TESTK5722, TESTPAYOUT1, TESTOPVCB, TESTIDTEST4, TESTONEPAYDVC, OP_SACOMCBSV, OP_SACOMTRVU, ****************, OPMPAY, FALSE, TESTDUONG, OP_VPBMPGS2, TESTSACOMCBU, TESTLZD, TESTSAPO, OP_TESTAUTH, TESTMEGA1, OP_TESTMID, TESTVTASABRE, OP_TESTK4722, TESTOP_VTB3, OP_SACOMVIN, OP_TESTK8220, TESTCNY, TESTVCB_4900, OP_TESTK5698, TESTPCI3, ANHTVTEST, TESTTOKEN, TESTONEPAY2, MONPAYCOLLECT, OP_TESTVP5411, TESTSHOPIFYTG, TESTOP_D, TESTONEPAYDVTT, TESTTRAGOP2, TESTBIDVU3, TESTBIDVV2, TESTTOKENUSD, TESTMEGA, TEST3DS2_MK, TEST3DS2_TN, TESTVFVND, TESTVTB3D2, TESTENETVIET, TESTSHOPIFY, TESTAPPLEND, TESTVCBHM2, TESTTRAGOP3, TESTOKENOP, TESTKONEVA, TESTOP_VTB1, AUTOAPLND, TESTSSPAY, TESTSAMSUNGPL, TESTVCB_5732, TESTAES, OP_TESTK7832, TESTPCI2, TESTJBAUSD, TESTVCBCYBER, TESTMERCHANT15K, OPPREPAID, TESTONEPAYUSD, OP_MPAYVND, TESTSACOMCBV, OP_VCBVND, TESTSAPO2, TESTBIDVCBSU, TESTVCB3D2O, TESTMAFC, OP_TESTOP20, TESTAXA, TESTTGDD3D2, TESTPR, TESTHC1, ONEPAYHB, TESTSSBNPL, OP_TESTK8299, TESTHD2BEN, TESTPCIEM, TESTMCD, OP_TESTK5732, TESTPROMMG, OP_TESTTT, TESTAUTOMSP, OP_TESTUSD2, OP_TESTAPPAY, TESTTHB, TESTSOS, TESTAPPLETG, OP_TESTK5977, EKKO1, TESTIDTEST6, TESTONEPAYDVDT, TESTBIDVCSU2, ****************, OP_VCBUSD, TESTBIDVCBSV, OP_TEST3DS, OP_TEST3DS2V, OP_CYBSUSD, TESTTHSC, TESTBNPL, TESTAOSVNC, TESTPROM, OP_SACOMECOM, TESTQRTINH, TESTTOKENDV, TESTUPOS, OP_TESTK5969, OP_TESTK5816, TESTONEPAY3, TESTPCI1, TESTKBANKCYBER, TESTVPBCYBER, TEST_OP2B, OP_TESTVP4900, TESTIDTEST3, TESTPAYPAL]
2025-08-18 05:35:06.413 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-08-18 05:35:06.413 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-08-18 05:35:06.414 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api approveOrRejectTransaction MA-----------
2025-08-18 05:35:06.414 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/refund/868850 ,transactionId868850 ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811 ,realIp=127.0.0.1 ,jsonData={"id":"868850","op":"replace","path":"/reject","value":{"merchant_id":"868850","transaction_reference":"TESTPCI_1755226501571","currency":"VND"},"skipCallSynchronize":false,"service":"QT"}
2025-08-18 05:35:06.415 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api approveOrRejectTransaction MA------------
2025-08-18 05:35:06.419 [ForkJoinPool.commonPool-worker-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE REFUND API: response code=400 ,response body={"code":400,"message":"Invalid request"}
2025-08-18 05:35:06.486 [ForkJoinPool.commonPool-worker-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE REFUND API: response code=400 ,response body={"code":400,"message":"Invalid request"}
2025-08-18 07:00:52.140 [ForkJoinPool.commonPool-worker-2] ERROR v.o.t.s.i.TransactionOldServiceImpl - Approve or reject failed: 868850
2025-08-18 07:02:31.112 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 1217050 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-08-18 07:02:31.114 [main] INFO  v.o.t.TransactionAppApplication - The following 1 profile is active: "dev"
2025-08-18 07:02:31.917 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-08-18 07:02:32.098 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 173 ms. Found 2 R2DBC repository interfaces.
2025-08-18 07:02:33.704 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Starting...
2025-08-18 07:02:34.012 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleMerchantPortalHikariPool - Added connection oracle.jdbc.driver.T4CConnection@156cfa20
2025-08-18 07:02:34.014 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Start completed.
2025-08-18 07:02:34.087 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Starting...
2025-08-18 07:02:34.200 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleOneReportHikariPool - Added connection oracle.jdbc.driver.T4CConnection@6728370a
2025-08-18 07:02:34.200 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Start completed.
2025-08-18 07:02:34.310 [main] INFO  v.o.t.job.PromotionReportJob - Scheduling PromotionReportJob with cron: 0 05 16 * * *
2025-08-18 07:02:34.318 [main] INFO  v.o.t.job.PromotionReportJob - End PromotionReportJob!
2025-08-18 07:02:34.320 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-08-18 07:02:34.321 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-08-18 07:02:34.324 [main] INFO  v.o.transaction.job.UposReportJob - Scheduling UposReportJob with cron: 0 10 16 * * *
2025-08-18 07:02:34.324 [main] INFO  v.o.transaction.job.UposReportJob - End UposReportJob!
2025-08-18 07:02:35.200 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 8394 (http)
2025-08-18 07:02:35.387 [main] INFO  v.o.t.TransactionAppApplication - Started TransactionAppApplication in 4.93 seconds (process running for 6.507)
2025-08-18 07:03:14.395 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://localhost:8394/ma-service/api/v1/transactions/transaction/multiple/approve-reject
2025-08-18 07:03:14.396 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-08-18 07:03:14.396 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-08-18 07:03:14.396 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-08-18 07:03:14.396 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-08-18 07:03:14.396 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-08-18 07:03:14.396 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev18-ma.onepay.vn
2025-08-18 07:03:14.396 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-08-18 07:03:14.396 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev18-ma.onepay.vn/mav2-main/
2025-08-18 07:03:14.396 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-08-18 07:03:14.396 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-08-18 07:03:14.396 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-08-18 07:03:14.396 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-08-18 07:03:14.396 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-08-18 07:03:14.397 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-08-18 07:03:14.397 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-08-18 07:03:14.397 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 417604
2025-08-18 07:03:14.397 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.*********.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; JSESSIONID=dummy; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************.3Eg84dqXHjJ3ixV4g4NZFYS0-5c28TzIa5mju5_L1Lg
2025-08-18 07:03:14.397 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-18 07:03:14.397 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = baa0e2ea-88a5-4b1a-9203-a77d833677ed
2025-08-18 07:03:14.397 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8394
2025-08-18 07:03:14.397 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-08-18 07:03:14.397 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 522
2025-08-18 07:03:14.400 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-08-18 07:03:31.347 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api approveOrRejectMultipleTransaction MA-----------
2025-08-18 07:03:31.362 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api approveOrRejectMultipleTransaction MA------------
2025-08-18 07:03:31.370 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=868849 ,paymentMethod=QT ,transactionType=Request Refund ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-18 07:03:31.371 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-08-18 07:03:31.429 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/868849 ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-18 07:03:31.510 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"transaction_id":"868849","original_transaction_id":"*********","transaction_type":"Request Refund","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"17","acquirer_name":"","acquirer_short_name":""},"transaction_reference":"TESTPCI_1755170440838","amount":{"total":1000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"512345****0008","card_type":"Mastercard","name_on_card":"123","card_date":{"month":"12","year":"25"}},"merchant_id":"TESTPCI","authentication":{},"transaction_time":"2025-08-14T18:20:40Z","transaction_status":"","transaction_ref_number":"*********","ip_address":"************","ip_proxy":"","bin_country":"","csc_result_code":"","verification_security_level":"","response_code":"","can_void":false,"risk_assesment":"Not Assessed","bank_id":"","wait_for_approval_amount":"0.00","advance_status":"Merchant Rejected","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"OTHER","acqResponseCode":"","status3ds":"","riskOverAllResult":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"123","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00","installment_statement_date":""}
2025-08-18 07:03:31.511 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-08-18 07:03:31.516 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-08-18 07:03:31.525 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-08-18 07:03:31.542 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-08-14T18:20:40, orderAmount=1000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=868849, parentTransactionId=*********, transactionType=Request Refund, paymentMethod=QT, transactionCreatedTime=2025-08-14T18:20:40, transactionCompletedTime=2025-08-14T18:20:40, transactionAmount=1000.0, transactionCurrency=VND, transactionStatus=Merchant Rejected, responseCode=, merchantTransRef=TESTPCI_1755170440838, paymentChannel=null, installmentStatus=), paymentMethod=PaymentMethod(cardNumber=512345****0008, cardBrand=null, cardType=Mastercard, issuer=, acquirer=17, cardExpiry=1225, nameOnCard=123, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, settlementAmount=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=InstallmentDetail(priceDifference=0, period=, amountMonthly=0.00), promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=************, ipProxy=, ipCountry=************, binCountry=, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null, amountAdvance=null), actions=null, histories=null)
2025-08-18 07:03:31.543 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-08-18 07:03:31.543 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/refund/868849 ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-18 07:03:31.556 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"row_num":"0","merchant_id":"TESTPCI","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"17","acquirer_name":"Vietinbank - MPGS","acquirer_short_name":"Vietinbank - MPGS"},"original_transaction_id":"*********","transaction_id":"868849","transaction_time":"2025-08-14T18:20:40Z","transaction_type":"Request Refund","transaction_status":"404","authentication":"","amount":{"currency":"VND","total":1000.0,"purchase_total":1000.0},"transaction_purchase_time":"2025-08-14T14:03:49Z","card":{"card_number":"512345****0008","card_type":"Mastercard","card_date":{"month":"12","year":"25"}},"csc_result_code":"","refund_type":"1"}
2025-08-18 07:03:31.556 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-08-18 07:03:31.557 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonRequestRefundIntenational...
2025-08-18 07:03:31.557 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonRequestRefundIntenational...
2025-08-18 07:03:31.558 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE REFUND INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-08-14T18:20:40, orderAmount=1000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=868849, parentTransactionId=*********, transactionType=Request Refund, paymentMethod=QT, transactionCreatedTime=2025-08-14T18:20:40, transactionCompletedTime=2025-08-14T18:20:40, transactionAmount=1000.0, transactionCurrency=VND, transactionStatus=404, responseCode=null, merchantTransRef=null, paymentChannel=null, installmentStatus=null), paymentMethod=PaymentMethod(cardNumber=512345****0008, cardBrand=null, cardType=Mastercard, issuer=Vietinbank - MPGS, acquirer=17, cardExpiry=null, nameOnCard=null, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, settlementAmount=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=null, promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=null), riskManagement=RiskManagement(ipAddress=************, ipProxy=, ipCountry=************, binCountry=, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null, amountAdvance=null), actions=null, histories=null)
2025-08-18 07:03:31.558 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-08-18 07:03:31.558 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getAdvanceFeeInfo ===================
2025-08-18 07:03:31.747 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-08-18 07:03:31.747 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-08-18 07:03:31.761 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=868850 ,paymentMethod=QT ,transactionType=Request Refund ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-18 07:03:31.761 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-08-18 07:03:31.762 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/868850 ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-18 07:03:31.780 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"transaction_id":"868850","original_transaction_id":"*********","transaction_type":"Request Refund","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"4","acquirer_name":"","acquirer_short_name":""},"transaction_reference":"TESTPCI_1755226501571","amount":{"total":1000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"531358****3430","card_type":"Mastercard","name_on_card":"123","card_date":{"month":"11","year":"26"}},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y"},"transaction_time":"2025-08-15T09:55:02Z","transaction_status":"","transaction_ref_number":"op3d20_vcb-82781","ip_address":"***************","ip_proxy":"","bin_country":"","csc_result_code":"","verification_security_level":"05","response_code":"","can_void":false,"risk_assesment":"Not Assessed","bank_id":"","wait_for_approval_amount":"0.00","advance_status":"Merchant Rejected","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"CREDIT","acqResponseCode":"","status3ds":"Y","riskOverAllResult":"ACCEPT","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"123","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00","installment_statement_date":""}
2025-08-18 07:03:31.780 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-08-18 07:03:31.781 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-08-18 07:03:31.782 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-08-18 07:03:31.782 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-08-15T09:55:02, orderAmount=1000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=868850, parentTransactionId=*********, transactionType=Request Refund, paymentMethod=QT, transactionCreatedTime=2025-08-15T09:55:02, transactionCompletedTime=2025-08-15T09:55:02, transactionAmount=1000.0, transactionCurrency=VND, transactionStatus=Merchant Rejected, responseCode=, merchantTransRef=TESTPCI_1755226501571, paymentChannel=null, installmentStatus=), paymentMethod=PaymentMethod(cardNumber=531358****3430, cardBrand=null, cardType=Mastercard, issuer=, acquirer=4, cardExpiry=1126, nameOnCard=123, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, settlementAmount=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=InstallmentDetail(priceDifference=0, period=, amountMonthly=0.00), promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=***************, ipProxy=, ipCountry=***************, binCountry=, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null, amountAdvance=null), actions=null, histories=null)
2025-08-18 07:03:31.782 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-08-18 07:03:31.783 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/refund/868850 ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-18 07:03:31.792 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"row_num":"0","merchant_id":"TESTPCI","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"4","acquirer_name":"Vietcombank - CyberSource","acquirer_short_name":"Vietcombank - CyberSource"},"original_transaction_id":"*********","transaction_id":"868850","transaction_time":"2025-08-15T09:55:02Z","transaction_type":"Request Refund","transaction_status":"404","authentication":"","amount":{"currency":"VND","total":1000.0,"purchase_total":1000.0},"transaction_purchase_time":"2025-08-15T09:51:47Z","card":{"card_number":"531358****3430","card_type":"Mastercard","card_date":{"month":"11","year":"26"}},"csc_result_code":"","refund_type":"1"}
2025-08-18 07:03:31.793 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-08-18 07:03:31.793 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonRequestRefundIntenational...
2025-08-18 07:03:31.793 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonRequestRefundIntenational...
2025-08-18 07:03:31.794 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE REFUND INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-08-15T09:55:02, orderAmount=1000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=868850, parentTransactionId=*********, transactionType=Request Refund, paymentMethod=QT, transactionCreatedTime=2025-08-15T09:55:02, transactionCompletedTime=2025-08-15T09:55:02, transactionAmount=1000.0, transactionCurrency=VND, transactionStatus=404, responseCode=null, merchantTransRef=null, paymentChannel=null, installmentStatus=null), paymentMethod=PaymentMethod(cardNumber=531358****3430, cardBrand=null, cardType=Mastercard, issuer=Vietcombank - CyberSource, acquirer=4, cardExpiry=null, nameOnCard=null, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, settlementAmount=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=null, promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=null), riskManagement=RiskManagement(ipAddress=***************, ipProxy=, ipCountry=***************, binCountry=, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null, amountAdvance=null), actions=null, histories=null)
2025-08-18 07:03:31.794 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-08-18 07:03:31.794 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getAdvanceFeeInfo ===================
2025-08-18 07:03:31.796 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-08-18 07:03:31.797 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-08-18 07:03:31.831 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-08-18 07:03:31.832 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - list merchant from permission: [OP_TESTVND, OP_VND01, OP_MPGSVND, OP_MPGSUSD, OP_MPAYVND3D, TESTBIDVV3, ONEPAYHM, D_TEST, OP_CYBSVND, TEST3DSMPGS, OP_MPAYVN1, TESTND, OP_TESTK7011, TESTHD3BEN, TESTSTATICQR, OP_TESTK4121, OP_SACOMTH, TESTAPLVNC, TESTORIFLCBS, TESTVCB_5411, OP_TESTK7399, TESTGGPAY, OP_TESTK5331, TESTKOVENA, TESTSTGINVOICE, TESTMERCHANT16KY, TESTOPVPB, TESTVTBCYBER, HUONG1, TESTOPBIDV, TESTTRAGOP, TESTPCI, TESTONEPAY, OP_TESTUSD, OP_SACOMTRVV, ONEPAY, INVOICETG, TESTDMX, TESTTNS, TESTINVOICE, TESTVCB3D2, TESTBIDV, TESTHC, TEST3DS2_LT, MIGS3DS, TESTAXA1, TESTVF, TESTUPOSTG, TESTAOSVNBANK, OP_TESTKBANK, TESTAPPLE, TESTVCBHM1, TESTTVLOKA, TESTVCB_7399, TESTVAGROUP, TESTVCB_6300, OP_TESTM8211, OP_TESTK7372, OP_TESTK7991, TESTPCI4, TESTOPVCB2, TESTSACOMCYBER, TESTUSD, OP_TESTVP5732, OP_TESTVP6300, ONEPAYMT, TESTATM, OPTEST, TESTTNSUSD, MPAYVND, TESTBIDV1, OP_VPBMPGS3, TESTAWPOSTG, TESTTRAGOP1, TESTSAMSUNG, TESTOP_VTB2, TESTUPOSKBANK, TESTAPLBANK, TESTVCB_8211, TESTMERCHANTID20KYTU, MONPAYOUT, TESTVIETQR, TESTINVOICETG, OP_TESTVP8211, TESTPAYOUT2, TEST, OP_SACOMCBSU, OP_VPBMPGS1, TESTONEPAY20, TESTVJU, OP_TESTCONFIG, OP_TESTSS, OP_TESTBHX, OPTESTHB, TESTAWPOS, TESTACVNC, OP_TESTK4900, TESTONEPAYTHB, ONEPAY_REFUND, TESTAPLTG, OP_TESTK5814, TESTVCB_4722, OP_TESTK5722, TESTPAYOUT1, TESTOPVCB, TESTIDTEST4, TESTONEPAYDVC, OP_SACOMCBSV, OP_SACOMTRVU, ****************, OPMPAY, FALSE, TESTDUONG, OP_VPBMPGS2, TESTSACOMCBU, TESTLZD, TESTSAPO, OP_TESTAUTH, TESTMEGA1, OP_TESTMID, TESTVTASABRE, OP_TESTK4722, TESTOP_VTB3, OP_SACOMVIN, OP_TESTK8220, TESTCNY, TESTVCB_4900, OP_TESTK5698, TESTPCI3, ANHTVTEST, TESTTOKEN, TESTONEPAY2, MONPAYCOLLECT, OP_TESTVP5411, TESTSHOPIFYTG, TESTOP_D, TESTONEPAYDVTT, TESTTRAGOP2, TESTBIDVU3, TESTBIDVV2, TESTTOKENUSD, TESTMEGA, TEST3DS2_MK, TEST3DS2_TN, TESTVFVND, TESTVTB3D2, TESTENETVIET, TESTSHOPIFY, TESTAPPLEND, TESTVCBHM2, TESTTRAGOP3, TESTOKENOP, TESTKONEVA, TESTOP_VTB1, AUTOAPLND, TESTSSPAY, TESTSAMSUNGPL, TESTVCB_5732, TESTAES, OP_TESTK7832, TESTPCI2, TESTJBAUSD, TESTVCBCYBER, TESTMERCHANT15K, OPPREPAID, TESTONEPAYUSD, OP_MPAYVND, TESTSACOMCBV, OP_VCBVND, TESTSAPO2, TESTBIDVCBSU, TESTVCB3D2O, TESTMAFC, OP_TESTOP20, TESTAXA, TESTTGDD3D2, TESTPR, TESTHC1, ONEPAYHB, TESTSSBNPL, OP_TESTK8299, TESTHD2BEN, TESTPCIEM, TESTMCD, OP_TESTK5732, TESTPROMMG, OP_TESTTT, TESTAUTOMSP, OP_TESTUSD2, OP_TESTAPPAY, TESTTHB, TESTSOS, TESTAPPLETG, OP_TESTK5977, EKKO1, TESTIDTEST6, TESTONEPAYDVDT, TESTBIDVCSU2, ****************, OP_VCBUSD, TESTBIDVCBSV, OP_TEST3DS, OP_TEST3DS2V, OP_CYBSUSD, TESTTHSC, TESTBNPL, TESTAOSVNC, TESTPROM, OP_SACOMECOM, TESTQRTINH, TESTTOKENDV, TESTUPOS, OP_TESTK5969, OP_TESTK5816, TESTONEPAY3, TESTPCI1, TESTKBANKCYBER, TESTVPBCYBER, TEST_OP2B, OP_TESTVP4900, TESTIDTEST3, TESTPAYPAL]
2025-08-18 07:03:31.835 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-08-18 07:03:31.836 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-08-18 07:03:31.839 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-08-18 07:03:31.839 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - list merchant from permission: [OP_TESTVND, OP_VND01, OP_MPGSVND, OP_MPGSUSD, OP_MPAYVND3D, TESTBIDVV3, ONEPAYHM, D_TEST, OP_CYBSVND, TEST3DSMPGS, OP_MPAYVN1, TESTND, OP_TESTK7011, TESTHD3BEN, TESTSTATICQR, OP_TESTK4121, OP_SACOMTH, TESTAPLVNC, TESTORIFLCBS, TESTVCB_5411, OP_TESTK7399, TESTGGPAY, OP_TESTK5331, TESTKOVENA, TESTSTGINVOICE, TESTMERCHANT16KY, TESTOPVPB, TESTVTBCYBER, HUONG1, TESTOPBIDV, TESTTRAGOP, TESTPCI, TESTONEPAY, OP_TESTUSD, OP_SACOMTRVV, ONEPAY, INVOICETG, TESTDMX, TESTTNS, TESTINVOICE, TESTVCB3D2, TESTBIDV, TESTHC, TEST3DS2_LT, MIGS3DS, TESTAXA1, TESTVF, TESTUPOSTG, TESTAOSVNBANK, OP_TESTKBANK, TESTAPPLE, TESTVCBHM1, TESTTVLOKA, TESTVCB_7399, TESTVAGROUP, TESTVCB_6300, OP_TESTM8211, OP_TESTK7372, OP_TESTK7991, TESTPCI4, TESTOPVCB2, TESTSACOMCYBER, TESTUSD, OP_TESTVP5732, OP_TESTVP6300, ONEPAYMT, TESTATM, OPTEST, TESTTNSUSD, MPAYVND, TESTBIDV1, OP_VPBMPGS3, TESTAWPOSTG, TESTTRAGOP1, TESTSAMSUNG, TESTOP_VTB2, TESTUPOSKBANK, TESTAPLBANK, TESTVCB_8211, TESTMERCHANTID20KYTU, MONPAYOUT, TESTVIETQR, TESTINVOICETG, OP_TESTVP8211, TESTPAYOUT2, TEST, OP_SACOMCBSU, OP_VPBMPGS1, TESTONEPAY20, TESTVJU, OP_TESTCONFIG, OP_TESTSS, OP_TESTBHX, OPTESTHB, TESTAWPOS, TESTACVNC, OP_TESTK4900, TESTONEPAYTHB, ONEPAY_REFUND, TESTAPLTG, OP_TESTK5814, TESTVCB_4722, OP_TESTK5722, TESTPAYOUT1, TESTOPVCB, TESTIDTEST4, TESTONEPAYDVC, OP_SACOMCBSV, OP_SACOMTRVU, ****************, OPMPAY, FALSE, TESTDUONG, OP_VPBMPGS2, TESTSACOMCBU, TESTLZD, TESTSAPO, OP_TESTAUTH, TESTMEGA1, OP_TESTMID, TESTVTASABRE, OP_TESTK4722, TESTOP_VTB3, OP_SACOMVIN, OP_TESTK8220, TESTCNY, TESTVCB_4900, OP_TESTK5698, TESTPCI3, ANHTVTEST, TESTTOKEN, TESTONEPAY2, MONPAYCOLLECT, OP_TESTVP5411, TESTSHOPIFYTG, TESTOP_D, TESTONEPAYDVTT, TESTTRAGOP2, TESTBIDVU3, TESTBIDVV2, TESTTOKENUSD, TESTMEGA, TEST3DS2_MK, TEST3DS2_TN, TESTVFVND, TESTVTB3D2, TESTENETVIET, TESTSHOPIFY, TESTAPPLEND, TESTVCBHM2, TESTTRAGOP3, TESTOKENOP, TESTKONEVA, TESTOP_VTB1, AUTOAPLND, TESTSSPAY, TESTSAMSUNGPL, TESTVCB_5732, TESTAES, OP_TESTK7832, TESTPCI2, TESTJBAUSD, TESTVCBCYBER, TESTMERCHANT15K, OPPREPAID, TESTONEPAYUSD, OP_MPAYVND, TESTSACOMCBV, OP_VCBVND, TESTSAPO2, TESTBIDVCBSU, TESTVCB3D2O, TESTMAFC, OP_TESTOP20, TESTAXA, TESTTGDD3D2, TESTPR, TESTHC1, ONEPAYHB, TESTSSBNPL, OP_TESTK8299, TESTHD2BEN, TESTPCIEM, TESTMCD, OP_TESTK5732, TESTPROMMG, OP_TESTTT, TESTAUTOMSP, OP_TESTUSD2, OP_TESTAPPAY, TESTTHB, TESTSOS, TESTAPPLETG, OP_TESTK5977, EKKO1, TESTIDTEST6, TESTONEPAYDVDT, TESTBIDVCSU2, ****************, OP_VCBUSD, TESTBIDVCBSV, OP_TEST3DS, OP_TEST3DS2V, OP_CYBSUSD, TESTTHSC, TESTBNPL, TESTAOSVNC, TESTPROM, OP_SACOMECOM, TESTQRTINH, TESTTOKENDV, TESTUPOS, OP_TESTK5969, OP_TESTK5816, TESTONEPAY3, TESTPCI1, TESTKBANKCYBER, TESTVPBCYBER, TEST_OP2B, OP_TESTVP4900, TESTIDTEST3, TESTPAYPAL]
2025-08-18 07:03:31.839 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-08-18 07:03:31.839 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-08-18 07:03:31.846 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api approveOrRejectTransaction MA-----------
2025-08-18 07:03:31.846 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api approveOrRejectTransaction MA-----------
2025-08-18 07:03:31.847 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/refund/868849 ,transactionId868849 ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811 ,realIp=127.0.0.1 ,jsonData={"id":"868849","op":"replace","path":"/reject","value":{"merchant_id":"868849","transaction_reference":"TESTPCI_1755170440838","currency":"VND"},"skipCallSynchronize":false,"service":"QT"}
2025-08-18 07:03:31.847 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/refund/868850 ,transactionId868850 ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811 ,realIp=127.0.0.1 ,jsonData={"id":"868850","op":"replace","path":"/reject","value":{"merchant_id":"868850","transaction_reference":"TESTPCI_1755226501571","currency":"VND"},"skipCallSynchronize":false,"service":"QT"}
2025-08-18 07:03:35.443 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api approveOrRejectTransaction MA------------
2025-08-18 07:03:35.447 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE REFUND API: response code=400 ,response body={"code":400,"message":"Invalid request"}
2025-08-18 07:04:12.480 [reactor-http-epoll-4] ERROR v.o.t.s.i.TransactionOldServiceImpl - Approve or reject failed: 868850
2025-08-18 07:05:59.389 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 1218540 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-08-18 07:05:59.391 [main] INFO  v.o.t.TransactionAppApplication - The following 1 profile is active: "dev"
2025-08-18 07:06:00.140 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-08-18 07:06:00.309 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 161 ms. Found 2 R2DBC repository interfaces.
2025-08-18 07:06:01.853 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Starting...
2025-08-18 07:06:02.254 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleMerchantPortalHikariPool - Added connection oracle.jdbc.driver.T4CConnection@39a7eca5
2025-08-18 07:06:02.256 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Start completed.
2025-08-18 07:06:02.331 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Starting...
2025-08-18 07:06:02.444 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleOneReportHikariPool - Added connection oracle.jdbc.driver.T4CConnection@38ef1a0a
2025-08-18 07:06:02.444 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Start completed.
2025-08-18 07:06:02.554 [main] INFO  v.o.t.job.PromotionReportJob - Scheduling PromotionReportJob with cron: 0 05 16 * * *
2025-08-18 07:06:02.562 [main] INFO  v.o.t.job.PromotionReportJob - End PromotionReportJob!
2025-08-18 07:06:02.564 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-08-18 07:06:02.565 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-08-18 07:06:02.567 [main] INFO  v.o.transaction.job.UposReportJob - Scheduling UposReportJob with cron: 0 10 16 * * *
2025-08-18 07:06:02.568 [main] INFO  v.o.transaction.job.UposReportJob - End UposReportJob!
2025-08-18 07:06:03.562 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 8394 (http)
2025-08-18 07:06:03.770 [main] INFO  v.o.t.TransactionAppApplication - Started TransactionAppApplication in 5.004 seconds (process running for 6.661)
2025-08-18 07:07:48.247 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 1219269 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-08-18 07:07:48.250 [main] INFO  v.o.t.TransactionAppApplication - The following 1 profile is active: "dev"
2025-08-18 07:07:48.998 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-08-18 07:07:49.172 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 166 ms. Found 2 R2DBC repository interfaces.
2025-08-18 07:07:50.647 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Starting...
2025-08-18 07:07:50.921 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleMerchantPortalHikariPool - Added connection oracle.jdbc.driver.T4CConnection@58aa5c94
2025-08-18 07:07:50.922 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Start completed.
2025-08-18 07:07:50.979 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Starting...
2025-08-18 07:07:51.120 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleOneReportHikariPool - Added connection oracle.jdbc.driver.T4CConnection@6ec3d8e4
2025-08-18 07:07:51.120 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Start completed.
2025-08-18 07:07:51.229 [main] INFO  v.o.t.job.PromotionReportJob - Scheduling PromotionReportJob with cron: 0 05 16 * * *
2025-08-18 07:07:51.236 [main] INFO  v.o.t.job.PromotionReportJob - End PromotionReportJob!
2025-08-18 07:07:51.238 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-08-18 07:07:51.239 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-08-18 07:07:51.241 [main] INFO  v.o.transaction.job.UposReportJob - Scheduling UposReportJob with cron: 0 10 16 * * *
2025-08-18 07:07:51.242 [main] INFO  v.o.transaction.job.UposReportJob - End UposReportJob!
2025-08-18 07:07:52.051 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 8394 (http)
2025-08-18 07:07:52.257 [main] INFO  v.o.t.TransactionAppApplication - Started TransactionAppApplication in 4.642 seconds (process running for 6.065)
2025-08-18 07:08:34.825 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://localhost:8394/ma-service/api/v1/transactions/transaction/multiple/approve-reject
2025-08-18 07:08:34.826 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-08-18 07:08:34.826 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-08-18 07:08:34.826 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-08-18 07:08:34.826 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-08-18 07:08:34.826 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-08-18 07:08:34.826 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev18-ma.onepay.vn
2025-08-18 07:08:34.826 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-08-18 07:08:34.827 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev18-ma.onepay.vn/mav2-main/
2025-08-18 07:08:34.827 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-08-18 07:08:34.827 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-08-18 07:08:34.827 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-08-18 07:08:34.827 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-08-18 07:08:34.827 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-08-18 07:08:34.827 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-08-18 07:08:34.827 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-08-18 07:08:34.827 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 417604
2025-08-18 07:08:34.827 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.*********.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; JSESSIONID=dummy; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************.3Eg84dqXHjJ3ixV4g4NZFYS0-5c28TzIa5mju5_L1Lg
2025-08-18 07:08:34.827 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-18 07:08:34.827 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 70bd75ac-9e6a-48fb-a163-f5e8ada5be09
2025-08-18 07:08:34.827 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8394
2025-08-18 07:08:34.828 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-08-18 07:08:34.828 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 522
2025-08-18 07:08:34.831 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-08-18 07:08:44.394 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api approveOrRejectMultipleTransaction MA-----------
2025-08-18 07:08:44.406 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api approveOrRejectMultipleTransaction MA------------
2025-08-18 07:08:44.412 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=868849 ,paymentMethod=QT ,transactionType=Request Refund ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-18 07:08:44.412 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-08-18 07:08:44.467 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/868849 ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-18 07:08:44.544 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"transaction_id":"868849","original_transaction_id":"*********","transaction_type":"Request Refund","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"17","acquirer_name":"","acquirer_short_name":""},"transaction_reference":"TESTPCI_1755170440838","amount":{"total":1000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"512345****0008","card_type":"Mastercard","name_on_card":"123","card_date":{"month":"12","year":"25"}},"merchant_id":"TESTPCI","authentication":{},"transaction_time":"2025-08-14T18:20:40Z","transaction_status":"","transaction_ref_number":"*********","ip_address":"************","ip_proxy":"","bin_country":"","csc_result_code":"","verification_security_level":"","response_code":"","can_void":false,"risk_assesment":"Not Assessed","bank_id":"","wait_for_approval_amount":"0.00","advance_status":"Merchant Rejected","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"OTHER","acqResponseCode":"","status3ds":"","riskOverAllResult":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"123","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00","installment_statement_date":""}
2025-08-18 07:08:44.544 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-08-18 07:08:44.550 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-08-18 07:08:44.558 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-08-18 07:08:44.575 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-08-14T18:20:40, orderAmount=1000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=868849, parentTransactionId=*********, transactionType=Request Refund, paymentMethod=QT, transactionCreatedTime=2025-08-14T18:20:40, transactionCompletedTime=2025-08-14T18:20:40, transactionAmount=1000.0, transactionCurrency=VND, transactionStatus=Merchant Rejected, responseCode=, merchantTransRef=TESTPCI_1755170440838, paymentChannel=null, installmentStatus=), paymentMethod=PaymentMethod(cardNumber=512345****0008, cardBrand=null, cardType=Mastercard, issuer=, acquirer=17, cardExpiry=1225, nameOnCard=123, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, settlementAmount=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=InstallmentDetail(priceDifference=0, period=, amountMonthly=0.00), promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=************, ipProxy=, ipCountry=************, binCountry=, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null, amountAdvance=null), actions=null, histories=null)
2025-08-18 07:08:44.575 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-08-18 07:08:44.576 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/refund/868849 ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-18 07:08:44.586 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"row_num":"0","merchant_id":"TESTPCI","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"17","acquirer_name":"Vietinbank - MPGS","acquirer_short_name":"Vietinbank - MPGS"},"original_transaction_id":"*********","transaction_id":"868849","transaction_time":"2025-08-14T18:20:40Z","transaction_type":"Request Refund","transaction_status":"404","authentication":"","amount":{"currency":"VND","total":1000.0,"purchase_total":1000.0},"transaction_purchase_time":"2025-08-14T14:03:49Z","card":{"card_number":"512345****0008","card_type":"Mastercard","card_date":{"month":"12","year":"25"}},"csc_result_code":"","refund_type":"1"}
2025-08-18 07:08:44.586 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-08-18 07:08:44.587 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonRequestRefundIntenational...
2025-08-18 07:08:44.587 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonRequestRefundIntenational...
2025-08-18 07:08:44.588 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE REFUND INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-08-14T18:20:40, orderAmount=1000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=868849, parentTransactionId=*********, transactionType=Request Refund, paymentMethod=QT, transactionCreatedTime=2025-08-14T18:20:40, transactionCompletedTime=2025-08-14T18:20:40, transactionAmount=1000.0, transactionCurrency=VND, transactionStatus=404, responseCode=null, merchantTransRef=null, paymentChannel=null, installmentStatus=null), paymentMethod=PaymentMethod(cardNumber=512345****0008, cardBrand=null, cardType=Mastercard, issuer=Vietinbank - MPGS, acquirer=17, cardExpiry=null, nameOnCard=null, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, settlementAmount=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=null, promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=null), riskManagement=RiskManagement(ipAddress=************, ipProxy=, ipCountry=************, binCountry=, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null, amountAdvance=null), actions=null, histories=null)
2025-08-18 07:08:44.588 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-08-18 07:08:44.588 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getAdvanceFeeInfo ===================
2025-08-18 07:08:44.754 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-08-18 07:08:44.754 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-08-18 07:08:44.762 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=868850 ,paymentMethod=QT ,transactionType=Request Refund ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-18 07:08:44.762 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-08-18 07:08:44.763 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/868850 ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-18 07:08:44.778 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"transaction_id":"868850","original_transaction_id":"*********","transaction_type":"Request Refund","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"4","acquirer_name":"","acquirer_short_name":""},"transaction_reference":"TESTPCI_1755226501571","amount":{"total":1000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"531358****3430","card_type":"Mastercard","name_on_card":"123","card_date":{"month":"11","year":"26"}},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y"},"transaction_time":"2025-08-15T09:55:02Z","transaction_status":"","transaction_ref_number":"op3d20_vcb-82781","ip_address":"***************","ip_proxy":"","bin_country":"","csc_result_code":"","verification_security_level":"05","response_code":"","can_void":false,"risk_assesment":"Not Assessed","bank_id":"","wait_for_approval_amount":"0.00","advance_status":"Merchant Rejected","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"CREDIT","acqResponseCode":"","status3ds":"Y","riskOverAllResult":"ACCEPT","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"123","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00","installment_statement_date":""}
2025-08-18 07:08:44.778 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-08-18 07:08:44.779 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-08-18 07:08:44.779 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-08-18 07:08:44.780 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-08-15T09:55:02, orderAmount=1000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=868850, parentTransactionId=*********, transactionType=Request Refund, paymentMethod=QT, transactionCreatedTime=2025-08-15T09:55:02, transactionCompletedTime=2025-08-15T09:55:02, transactionAmount=1000.0, transactionCurrency=VND, transactionStatus=Merchant Rejected, responseCode=, merchantTransRef=TESTPCI_1755226501571, paymentChannel=null, installmentStatus=), paymentMethod=PaymentMethod(cardNumber=531358****3430, cardBrand=null, cardType=Mastercard, issuer=, acquirer=4, cardExpiry=1126, nameOnCard=123, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, settlementAmount=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=InstallmentDetail(priceDifference=0, period=, amountMonthly=0.00), promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=***************, ipProxy=, ipCountry=***************, binCountry=, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null, amountAdvance=null), actions=null, histories=null)
2025-08-18 07:08:44.780 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-08-18 07:08:44.780 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/refund/868850 ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-18 07:08:44.790 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"row_num":"0","merchant_id":"TESTPCI","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"4","acquirer_name":"Vietcombank - CyberSource","acquirer_short_name":"Vietcombank - CyberSource"},"original_transaction_id":"*********","transaction_id":"868850","transaction_time":"2025-08-15T09:55:02Z","transaction_type":"Request Refund","transaction_status":"404","authentication":"","amount":{"currency":"VND","total":1000.0,"purchase_total":1000.0},"transaction_purchase_time":"2025-08-15T09:51:47Z","card":{"card_number":"531358****3430","card_type":"Mastercard","card_date":{"month":"11","year":"26"}},"csc_result_code":"","refund_type":"1"}
2025-08-18 07:08:44.791 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-08-18 07:08:44.791 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonRequestRefundIntenational...
2025-08-18 07:08:44.792 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonRequestRefundIntenational...
2025-08-18 07:08:44.792 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-08-18 07:08:44.792 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE REFUND INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-08-15T09:55:02, orderAmount=1000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=868850, parentTransactionId=*********, transactionType=Request Refund, paymentMethod=QT, transactionCreatedTime=2025-08-15T09:55:02, transactionCompletedTime=2025-08-15T09:55:02, transactionAmount=1000.0, transactionCurrency=VND, transactionStatus=404, responseCode=null, merchantTransRef=null, paymentChannel=null, installmentStatus=null), paymentMethod=PaymentMethod(cardNumber=531358****3430, cardBrand=null, cardType=Mastercard, issuer=Vietcombank - CyberSource, acquirer=4, cardExpiry=null, nameOnCard=null, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, settlementAmount=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=null, promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=null), riskManagement=RiskManagement(ipAddress=***************, ipProxy=, ipCountry=***************, binCountry=, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null, amountAdvance=null), actions=null, histories=null)
2025-08-18 07:08:44.792 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - list merchant from permission: [OP_TESTVND, OP_VND01, OP_MPGSVND, OP_MPGSUSD, OP_MPAYVND3D, TESTBIDVV3, ONEPAYHM, D_TEST, OP_CYBSVND, TEST3DSMPGS, OP_MPAYVN1, TESTND, OP_TESTK7011, TESTHD3BEN, TESTSTATICQR, OP_TESTK4121, OP_SACOMTH, TESTAPLVNC, TESTORIFLCBS, TESTVCB_5411, OP_TESTK7399, TESTGGPAY, OP_TESTK5331, TESTKOVENA, TESTSTGINVOICE, TESTMERCHANT16KY, TESTOPVPB, TESTVTBCYBER, HUONG1, TESTOPBIDV, TESTTRAGOP, TESTPCI, TESTONEPAY, OP_TESTUSD, OP_SACOMTRVV, ONEPAY, INVOICETG, TESTDMX, TESTTNS, TESTINVOICE, TESTVCB3D2, TESTBIDV, TESTHC, TEST3DS2_LT, MIGS3DS, TESTAXA1, TESTVF, TESTUPOSTG, TESTAOSVNBANK, OP_TESTKBANK, TESTAPPLE, TESTVCBHM1, TESTTVLOKA, TESTVCB_7399, TESTVAGROUP, TESTVCB_6300, OP_TESTM8211, OP_TESTK7372, OP_TESTK7991, TESTPCI4, TESTOPVCB2, TESTSACOMCYBER, TESTUSD, OP_TESTVP5732, OP_TESTVP6300, ONEPAYMT, TESTATM, OPTEST, TESTTNSUSD, MPAYVND, TESTBIDV1, OP_VPBMPGS3, TESTAWPOSTG, TESTTRAGOP1, TESTSAMSUNG, TESTOP_VTB2, TESTUPOSKBANK, TESTAPLBANK, TESTVCB_8211, TESTMERCHANTID20KYTU, MONPAYOUT, TESTVIETQR, TESTINVOICETG, OP_TESTVP8211, TESTPAYOUT2, TEST, OP_SACOMCBSU, OP_VPBMPGS1, TESTONEPAY20, TESTVJU, OP_TESTCONFIG, OP_TESTSS, OP_TESTBHX, OPTESTHB, TESTAWPOS, TESTACVNC, OP_TESTK4900, TESTONEPAYTHB, ONEPAY_REFUND, TESTAPLTG, OP_TESTK5814, TESTVCB_4722, OP_TESTK5722, TESTPAYOUT1, TESTOPVCB, TESTIDTEST4, TESTONEPAYDVC, OP_SACOMCBSV, OP_SACOMTRVU, ****************, OPMPAY, FALSE, TESTDUONG, OP_VPBMPGS2, TESTSACOMCBU, TESTLZD, TESTSAPO, OP_TESTAUTH, TESTMEGA1, OP_TESTMID, TESTVTASABRE, OP_TESTK4722, TESTOP_VTB3, OP_SACOMVIN, OP_TESTK8220, TESTCNY, TESTVCB_4900, OP_TESTK5698, TESTPCI3, ANHTVTEST, TESTTOKEN, TESTONEPAY2, MONPAYCOLLECT, OP_TESTVP5411, TESTSHOPIFYTG, TESTOP_D, TESTONEPAYDVTT, TESTTRAGOP2, TESTBIDVU3, TESTBIDVV2, TESTTOKENUSD, TESTMEGA, TEST3DS2_MK, TEST3DS2_TN, TESTVFVND, TESTVTB3D2, TESTENETVIET, TESTSHOPIFY, TESTAPPLEND, TESTVCBHM2, TESTTRAGOP3, TESTOKENOP, TESTKONEVA, TESTOP_VTB1, AUTOAPLND, TESTSSPAY, TESTSAMSUNGPL, TESTVCB_5732, TESTAES, OP_TESTK7832, TESTPCI2, TESTJBAUSD, TESTVCBCYBER, TESTMERCHANT15K, OPPREPAID, TESTONEPAYUSD, OP_MPAYVND, TESTSACOMCBV, OP_VCBVND, TESTSAPO2, TESTBIDVCBSU, TESTVCB3D2O, TESTMAFC, OP_TESTOP20, TESTAXA, TESTTGDD3D2, TESTPR, TESTHC1, ONEPAYHB, TESTSSBNPL, OP_TESTK8299, TESTHD2BEN, TESTPCIEM, TESTMCD, OP_TESTK5732, TESTPROMMG, OP_TESTTT, TESTAUTOMSP, OP_TESTUSD2, OP_TESTAPPAY, TESTTHB, TESTSOS, TESTAPPLETG, OP_TESTK5977, EKKO1, TESTIDTEST6, TESTONEPAYDVDT, TESTBIDVCSU2, ****************, OP_VCBUSD, TESTBIDVCBSV, OP_TEST3DS, OP_TEST3DS2V, OP_CYBSUSD, TESTTHSC, TESTBNPL, TESTAOSVNC, TESTPROM, OP_SACOMECOM, TESTQRTINH, TESTTOKENDV, TESTUPOS, OP_TESTK5969, OP_TESTK5816, TESTONEPAY3, TESTPCI1, TESTKBANKCYBER, TESTVPBCYBER, TEST_OP2B, OP_TESTVP4900, TESTIDTEST3, TESTPAYPAL]
2025-08-18 07:08:44.792 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-08-18 07:08:44.793 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getAdvanceFeeInfo ===================
2025-08-18 07:08:44.795 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-08-18 07:08:44.796 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-08-18 07:08:44.797 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-08-18 07:08:44.797 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-08-18 07:08:44.805 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api approveOrRejectTransaction MA-----------
2025-08-18 07:08:44.806 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/refund/868849 ,transactionId868849 ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811 ,realIp=127.0.0.1 ,jsonData={"id":"868849","op":"replace","path":"/reject","value":{"merchant_id":"868849","transaction_reference":"TESTPCI_1755170440838","currency":"VND"},"skipCallSynchronize":false,"service":"QT"}
2025-08-18 07:08:44.810 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api approveOrRejectTransaction MA------------
2025-08-18 07:08:44.819 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-08-18 07:08:44.819 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - list merchant from permission: [OP_TESTVND, OP_VND01, OP_MPGSVND, OP_MPGSUSD, OP_MPAYVND3D, TESTBIDVV3, ONEPAYHM, D_TEST, OP_CYBSVND, TEST3DSMPGS, OP_MPAYVN1, TESTND, OP_TESTK7011, TESTHD3BEN, TESTSTATICQR, OP_TESTK4121, OP_SACOMTH, TESTAPLVNC, TESTORIFLCBS, TESTVCB_5411, OP_TESTK7399, TESTGGPAY, OP_TESTK5331, TESTKOVENA, TESTSTGINVOICE, TESTMERCHANT16KY, TESTOPVPB, TESTVTBCYBER, HUONG1, TESTOPBIDV, TESTTRAGOP, TESTPCI, TESTONEPAY, OP_TESTUSD, OP_SACOMTRVV, ONEPAY, INVOICETG, TESTDMX, TESTTNS, TESTINVOICE, TESTVCB3D2, TESTBIDV, TESTHC, TEST3DS2_LT, MIGS3DS, TESTAXA1, TESTVF, TESTUPOSTG, TESTAOSVNBANK, OP_TESTKBANK, TESTAPPLE, TESTVCBHM1, TESTTVLOKA, TESTVCB_7399, TESTVAGROUP, TESTVCB_6300, OP_TESTM8211, OP_TESTK7372, OP_TESTK7991, TESTPCI4, TESTOPVCB2, TESTSACOMCYBER, TESTUSD, OP_TESTVP5732, OP_TESTVP6300, ONEPAYMT, TESTATM, OPTEST, TESTTNSUSD, MPAYVND, TESTBIDV1, OP_VPBMPGS3, TESTAWPOSTG, TESTTRAGOP1, TESTSAMSUNG, TESTOP_VTB2, TESTUPOSKBANK, TESTAPLBANK, TESTVCB_8211, TESTMERCHANTID20KYTU, MONPAYOUT, TESTVIETQR, TESTINVOICETG, OP_TESTVP8211, TESTPAYOUT2, TEST, OP_SACOMCBSU, OP_VPBMPGS1, TESTONEPAY20, TESTVJU, OP_TESTCONFIG, OP_TESTSS, OP_TESTBHX, OPTESTHB, TESTAWPOS, TESTACVNC, OP_TESTK4900, TESTONEPAYTHB, ONEPAY_REFUND, TESTAPLTG, OP_TESTK5814, TESTVCB_4722, OP_TESTK5722, TESTPAYOUT1, TESTOPVCB, TESTIDTEST4, TESTONEPAYDVC, OP_SACOMCBSV, OP_SACOMTRVU, ****************, OPMPAY, FALSE, TESTDUONG, OP_VPBMPGS2, TESTSACOMCBU, TESTLZD, TESTSAPO, OP_TESTAUTH, TESTMEGA1, OP_TESTMID, TESTVTASABRE, OP_TESTK4722, TESTOP_VTB3, OP_SACOMVIN, OP_TESTK8220, TESTCNY, TESTVCB_4900, OP_TESTK5698, TESTPCI3, ANHTVTEST, TESTTOKEN, TESTONEPAY2, MONPAYCOLLECT, OP_TESTVP5411, TESTSHOPIFYTG, TESTOP_D, TESTONEPAYDVTT, TESTTRAGOP2, TESTBIDVU3, TESTBIDVV2, TESTTOKENUSD, TESTMEGA, TEST3DS2_MK, TEST3DS2_TN, TESTVFVND, TESTVTB3D2, TESTENETVIET, TESTSHOPIFY, TESTAPPLEND, TESTVCBHM2, TESTTRAGOP3, TESTOKENOP, TESTKONEVA, TESTOP_VTB1, AUTOAPLND, TESTSSPAY, TESTSAMSUNGPL, TESTVCB_5732, TESTAES, OP_TESTK7832, TESTPCI2, TESTJBAUSD, TESTVCBCYBER, TESTMERCHANT15K, OPPREPAID, TESTONEPAYUSD, OP_MPAYVND, TESTSACOMCBV, OP_VCBVND, TESTSAPO2, TESTBIDVCBSU, TESTVCB3D2O, TESTMAFC, OP_TESTOP20, TESTAXA, TESTTGDD3D2, TESTPR, TESTHC1, ONEPAYHB, TESTSSBNPL, OP_TESTK8299, TESTHD2BEN, TESTPCIEM, TESTMCD, OP_TESTK5732, TESTPROMMG, OP_TESTTT, TESTAUTOMSP, OP_TESTUSD2, OP_TESTAPPAY, TESTTHB, TESTSOS, TESTAPPLETG, OP_TESTK5977, EKKO1, TESTIDTEST6, TESTONEPAYDVDT, TESTBIDVCSU2, ****************, OP_VCBUSD, TESTBIDVCBSV, OP_TEST3DS, OP_TEST3DS2V, OP_CYBSUSD, TESTTHSC, TESTBNPL, TESTAOSVNC, TESTPROM, OP_SACOMECOM, TESTQRTINH, TESTTOKENDV, TESTUPOS, OP_TESTK5969, OP_TESTK5816, TESTONEPAY3, TESTPCI1, TESTKBANKCYBER, TESTVPBCYBER, TEST_OP2B, OP_TESTVP4900, TESTIDTEST3, TESTPAYPAL]
2025-08-18 07:08:44.819 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-08-18 07:08:44.820 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-08-18 07:08:44.820 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api approveOrRejectTransaction MA-----------
2025-08-18 07:08:44.821 [ForkJoinPool.commonPool-worker-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE REFUND API: response code=400 ,response body={"code":400,"message":"Invalid request"}
2025-08-18 07:08:44.821 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/refund/868850 ,transactionId868850 ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811 ,realIp=127.0.0.1 ,jsonData={"id":"868850","op":"replace","path":"/reject","value":{"merchant_id":"868850","transaction_reference":"TESTPCI_1755226501571","currency":"VND"},"skipCallSynchronize":false,"service":"QT"}
2025-08-18 07:08:44.821 [ForkJoinPool.commonPool-worker-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - approveOrRejectTransaction result for id 868849: {message=Fail}
2025-08-18 07:08:44.821 [ForkJoinPool.commonPool-worker-1] ERROR v.o.t.s.i.TransactionOldServiceImpl - Approve or reject failed: 868849
2025-08-18 07:08:44.822 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api approveOrRejectTransaction MA------------
2025-08-18 07:08:44.833 [ForkJoinPool.commonPool-worker-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE REFUND API: response code=400 ,response body={"code":400,"message":"Invalid request"}
2025-08-18 07:08:44.833 [ForkJoinPool.commonPool-worker-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - approveOrRejectTransaction result for id 868850: {message=Fail}
2025-08-18 07:08:44.833 [ForkJoinPool.commonPool-worker-1] ERROR v.o.t.s.i.TransactionOldServiceImpl - Approve or reject failed: 868850
2025-08-18 07:08:44.863 [ForkJoinPool.commonPool-worker-1] INFO  v.o.t.exception.RequestLoggingFilter - Response: PATCH http://localhost:8394/ma-service/api/v1/transactions/transaction/multiple/approve-reject - Status: 200 OK
2025-08-18 07:09:54.975 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: PATCH http://localhost:8394/ma-service/api/v1/transactions/transaction/multiple/approve-reject
2025-08-18 07:09:54.975 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-08-18 07:09:54.976 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-08-18 07:09:54.976 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-08-18 07:09:54.976 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-08-18 07:09:54.976 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-08-18 07:09:54.976 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Origin = https://dev18-ma.onepay.vn
2025-08-18 07:09:54.976 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-08-18 07:09:54.976 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev18-ma.onepay.vn/mav2-main/
2025-08-18 07:09:54.976 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-08-18 07:09:54.976 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-08-18 07:09:54.976 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-08-18 07:09:54.976 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-08-18 07:09:54.976 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-08-18 07:09:54.976 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-08-18 07:09:54.976 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-08-18 07:09:54.976 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 417604
2025-08-18 07:09:54.976 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.*********.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; JSESSIONID=dummy; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************.3Eg84dqXHjJ3ixV4g4NZFYS0-5c28TzIa5mju5_L1Lg
2025-08-18 07:09:54.976 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-18 07:09:54.976 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 31d7cc8d-76e2-4923-a9d9-503cf246417e
2025-08-18 07:09:54.976 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8394
2025-08-18 07:09:54.976 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-08-18 07:09:54.976 [reactor-http-epoll-5] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Length = 522
2025-08-18 07:09:54.977 [reactor-http-epoll-5] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-08-18 07:10:03.647 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api approveOrRejectMultipleTransaction MA-----------
2025-08-18 07:10:03.649 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api approveOrRejectMultipleTransaction MA------------
2025-08-18 07:10:03.649 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=868849 ,paymentMethod=QT ,transactionType=Request Refund ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-18 07:10:03.649 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-08-18 07:10:03.650 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/868849 ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-18 07:10:03.736 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"transaction_id":"868849","original_transaction_id":"*********","transaction_type":"Request Refund","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"17","acquirer_name":"","acquirer_short_name":""},"transaction_reference":"TESTPCI_1755170440838","amount":{"total":1000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"512345****0008","card_type":"Mastercard","name_on_card":"123","card_date":{"month":"12","year":"25"}},"merchant_id":"TESTPCI","authentication":{},"transaction_time":"2025-08-14T18:20:40Z","transaction_status":"","transaction_ref_number":"*********","ip_address":"************","ip_proxy":"","bin_country":"","csc_result_code":"","verification_security_level":"","response_code":"","can_void":false,"risk_assesment":"Not Assessed","bank_id":"","wait_for_approval_amount":"0.00","advance_status":"Merchant Rejected","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"OTHER","acqResponseCode":"","status3ds":"","riskOverAllResult":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"123","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00","installment_statement_date":""}
2025-08-18 07:10:03.736 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-08-18 07:10:03.737 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-08-18 07:10:03.738 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-08-18 07:10:03.738 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-08-14T18:20:40, orderAmount=1000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=868849, parentTransactionId=*********, transactionType=Request Refund, paymentMethod=QT, transactionCreatedTime=2025-08-14T18:20:40, transactionCompletedTime=2025-08-14T18:20:40, transactionAmount=1000.0, transactionCurrency=VND, transactionStatus=Merchant Rejected, responseCode=, merchantTransRef=TESTPCI_1755170440838, paymentChannel=null, installmentStatus=), paymentMethod=PaymentMethod(cardNumber=512345****0008, cardBrand=null, cardType=Mastercard, issuer=, acquirer=17, cardExpiry=1225, nameOnCard=123, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, settlementAmount=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=InstallmentDetail(priceDifference=0, period=, amountMonthly=0.00), promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=************, ipProxy=, ipCountry=************, binCountry=, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null, amountAdvance=null), actions=null, histories=null)
2025-08-18 07:10:03.738 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-08-18 07:10:03.739 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/refund/868849 ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-18 07:10:03.750 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"row_num":"0","merchant_id":"TESTPCI","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"17","acquirer_name":"Vietinbank - MPGS","acquirer_short_name":"Vietinbank - MPGS"},"original_transaction_id":"*********","transaction_id":"868849","transaction_time":"2025-08-14T18:20:40Z","transaction_type":"Request Refund","transaction_status":"404","authentication":"","amount":{"currency":"VND","total":1000.0,"purchase_total":1000.0},"transaction_purchase_time":"2025-08-14T14:03:49Z","card":{"card_number":"512345****0008","card_type":"Mastercard","card_date":{"month":"12","year":"25"}},"csc_result_code":"","refund_type":"1"}
2025-08-18 07:10:03.750 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-08-18 07:10:03.751 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonRequestRefundIntenational...
2025-08-18 07:10:03.751 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonRequestRefundIntenational...
2025-08-18 07:10:03.751 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE REFUND INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-08-14T18:20:40, orderAmount=1000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=868849, parentTransactionId=*********, transactionType=Request Refund, paymentMethod=QT, transactionCreatedTime=2025-08-14T18:20:40, transactionCompletedTime=2025-08-14T18:20:40, transactionAmount=1000.0, transactionCurrency=VND, transactionStatus=404, responseCode=null, merchantTransRef=null, paymentChannel=null, installmentStatus=null), paymentMethod=PaymentMethod(cardNumber=512345****0008, cardBrand=null, cardType=Mastercard, issuer=Vietinbank - MPGS, acquirer=17, cardExpiry=null, nameOnCard=null, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, settlementAmount=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=null, promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=null), riskManagement=RiskManagement(ipAddress=************, ipProxy=, ipCountry=************, binCountry=, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null, amountAdvance=null), actions=null, histories=null)
2025-08-18 07:10:03.751 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-08-18 07:10:03.751 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getAdvanceFeeInfo ===================
2025-08-18 07:10:03.755 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-08-18 07:10:03.755 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-08-18 07:10:03.756 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=868850 ,paymentMethod=QT ,transactionType=Request Refund ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-18 07:10:03.756 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-08-18 07:10:03.757 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/868850 ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-18 07:10:03.803 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"transaction_id":"868850","original_transaction_id":"*********","transaction_type":"Request Refund","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"4","acquirer_name":"","acquirer_short_name":""},"transaction_reference":"TESTPCI_1755226501571","amount":{"total":1000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"531358****3430","card_type":"Mastercard","name_on_card":"123","card_date":{"month":"11","year":"26"}},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y"},"transaction_time":"2025-08-15T09:55:02Z","transaction_status":"","transaction_ref_number":"op3d20_vcb-82781","ip_address":"***************","ip_proxy":"","bin_country":"","csc_result_code":"","verification_security_level":"05","response_code":"","can_void":false,"risk_assesment":"Not Assessed","bank_id":"","wait_for_approval_amount":"0.00","advance_status":"Merchant Rejected","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"CREDIT","acqResponseCode":"","status3ds":"Y","riskOverAllResult":"ACCEPT","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"123","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00","installment_statement_date":""}
2025-08-18 07:10:03.803 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-08-18 07:10:03.804 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-08-18 07:10:03.804 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-08-18 07:10:03.804 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-08-15T09:55:02, orderAmount=1000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=868850, parentTransactionId=*********, transactionType=Request Refund, paymentMethod=QT, transactionCreatedTime=2025-08-15T09:55:02, transactionCompletedTime=2025-08-15T09:55:02, transactionAmount=1000.0, transactionCurrency=VND, transactionStatus=Merchant Rejected, responseCode=, merchantTransRef=TESTPCI_1755226501571, paymentChannel=null, installmentStatus=), paymentMethod=PaymentMethod(cardNumber=531358****3430, cardBrand=null, cardType=Mastercard, issuer=, acquirer=4, cardExpiry=1126, nameOnCard=123, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, settlementAmount=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=InstallmentDetail(priceDifference=0, period=, amountMonthly=0.00), promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=***************, ipProxy=, ipCountry=***************, binCountry=, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null, amountAdvance=null), actions=null, histories=null)
2025-08-18 07:10:03.805 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-08-18 07:10:03.805 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/refund/868850 ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-18 07:10:03.824 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"row_num":"0","merchant_id":"TESTPCI","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"4","acquirer_name":"Vietcombank - CyberSource","acquirer_short_name":"Vietcombank - CyberSource"},"original_transaction_id":"*********","transaction_id":"868850","transaction_time":"2025-08-15T09:55:02Z","transaction_type":"Request Refund","transaction_status":"404","authentication":"","amount":{"currency":"VND","total":1000.0,"purchase_total":1000.0},"transaction_purchase_time":"2025-08-15T09:51:47Z","card":{"card_number":"531358****3430","card_type":"Mastercard","card_date":{"month":"11","year":"26"}},"csc_result_code":"","refund_type":"1"}
2025-08-18 07:10:03.825 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-08-18 07:10:03.825 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonRequestRefundIntenational...
2025-08-18 07:10:03.826 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonRequestRefundIntenational...
2025-08-18 07:10:03.826 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE REFUND INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-08-15T09:55:02, orderAmount=1000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=868850, parentTransactionId=*********, transactionType=Request Refund, paymentMethod=QT, transactionCreatedTime=2025-08-15T09:55:02, transactionCompletedTime=2025-08-15T09:55:02, transactionAmount=1000.0, transactionCurrency=VND, transactionStatus=404, responseCode=null, merchantTransRef=null, paymentChannel=null, installmentStatus=null), paymentMethod=PaymentMethod(cardNumber=531358****3430, cardBrand=null, cardType=Mastercard, issuer=Vietcombank - CyberSource, acquirer=4, cardExpiry=null, nameOnCard=null, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, settlementAmount=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=null, promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=null), riskManagement=RiskManagement(ipAddress=***************, ipProxy=, ipCountry=***************, binCountry=, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null, amountAdvance=null), actions=null, histories=null)
2025-08-18 07:10:03.826 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-08-18 07:10:03.826 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getAdvanceFeeInfo ===================
2025-08-18 07:10:03.829 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-08-18 07:10:03.829 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-08-18 07:10:03.908 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-08-18 07:10:03.908 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - list merchant from permission: [OP_TESTVND, OP_VND01, OP_MPGSVND, OP_MPGSUSD, OP_MPAYVND3D, TESTBIDVV3, ONEPAYHM, D_TEST, OP_CYBSVND, TEST3DSMPGS, OP_MPAYVN1, TESTND, OP_TESTK7011, TESTHD3BEN, TESTSTATICQR, OP_TESTK4121, OP_SACOMTH, TESTAPLVNC, TESTORIFLCBS, TESTVCB_5411, OP_TESTK7399, TESTGGPAY, OP_TESTK5331, TESTKOVENA, TESTSTGINVOICE, TESTMERCHANT16KY, TESTOPVPB, TESTVTBCYBER, HUONG1, TESTOPBIDV, TESTTRAGOP, TESTPCI, TESTONEPAY, OP_TESTUSD, OP_SACOMTRVV, ONEPAY, INVOICETG, TESTDMX, TESTTNS, TESTINVOICE, TESTVCB3D2, TESTBIDV, TESTHC, TEST3DS2_LT, MIGS3DS, TESTAXA1, TESTVF, TESTUPOSTG, TESTAOSVNBANK, OP_TESTKBANK, TESTAPPLE, TESTVCBHM1, TESTTVLOKA, TESTVCB_7399, TESTVAGROUP, TESTVCB_6300, OP_TESTM8211, OP_TESTK7372, OP_TESTK7991, TESTPCI4, TESTOPVCB2, TESTSACOMCYBER, TESTUSD, OP_TESTVP5732, OP_TESTVP6300, ONEPAYMT, TESTATM, OPTEST, TESTTNSUSD, MPAYVND, TESTBIDV1, OP_VPBMPGS3, TESTAWPOSTG, TESTTRAGOP1, TESTSAMSUNG, TESTOP_VTB2, TESTUPOSKBANK, TESTAPLBANK, TESTVCB_8211, TESTMERCHANTID20KYTU, MONPAYOUT, TESTVIETQR, TESTINVOICETG, OP_TESTVP8211, TESTPAYOUT2, TEST, OP_SACOMCBSU, OP_VPBMPGS1, TESTONEPAY20, TESTVJU, OP_TESTCONFIG, OP_TESTSS, OP_TESTBHX, OPTESTHB, TESTAWPOS, TESTACVNC, OP_TESTK4900, TESTONEPAYTHB, ONEPAY_REFUND, TESTAPLTG, OP_TESTK5814, TESTVCB_4722, OP_TESTK5722, TESTPAYOUT1, TESTOPVCB, TESTIDTEST4, TESTONEPAYDVC, OP_SACOMCBSV, OP_SACOMTRVU, ****************, OPMPAY, FALSE, TESTDUONG, OP_VPBMPGS2, TESTSACOMCBU, TESTLZD, TESTSAPO, OP_TESTAUTH, TESTMEGA1, OP_TESTMID, TESTVTASABRE, OP_TESTK4722, TESTOP_VTB3, OP_SACOMVIN, OP_TESTK8220, TESTCNY, TESTVCB_4900, OP_TESTK5698, TESTPCI3, ANHTVTEST, TESTTOKEN, TESTONEPAY2, MONPAYCOLLECT, OP_TESTVP5411, TESTSHOPIFYTG, TESTOP_D, TESTONEPAYDVTT, TESTTRAGOP2, TESTBIDVU3, TESTBIDVV2, TESTTOKENUSD, TESTMEGA, TEST3DS2_MK, TEST3DS2_TN, TESTVFVND, TESTVTB3D2, TESTENETVIET, TESTSHOPIFY, TESTAPPLEND, TESTVCBHM2, TESTTRAGOP3, TESTOKENOP, TESTKONEVA, TESTOP_VTB1, AUTOAPLND, TESTSSPAY, TESTSAMSUNGPL, TESTVCB_5732, TESTAES, OP_TESTK7832, TESTPCI2, TESTJBAUSD, TESTVCBCYBER, TESTMERCHANT15K, OPPREPAID, TESTONEPAYUSD, OP_MPAYVND, TESTSACOMCBV, OP_VCBVND, TESTSAPO2, TESTBIDVCBSU, TESTVCB3D2O, TESTMAFC, OP_TESTOP20, TESTAXA, TESTTGDD3D2, TESTPR, TESTHC1, ONEPAYHB, TESTSSBNPL, OP_TESTK8299, TESTHD2BEN, TESTPCIEM, TESTMCD, OP_TESTK5732, TESTPROMMG, OP_TESTTT, TESTAUTOMSP, OP_TESTUSD2, OP_TESTAPPAY, TESTTHB, TESTSOS, TESTAPPLETG, OP_TESTK5977, EKKO1, TESTIDTEST6, TESTONEPAYDVDT, TESTBIDVCSU2, ****************, OP_VCBUSD, TESTBIDVCBSV, OP_TEST3DS, OP_TEST3DS2V, OP_CYBSUSD, TESTTHSC, TESTBNPL, TESTAOSVNC, TESTPROM, OP_SACOMECOM, TESTQRTINH, TESTTOKENDV, TESTUPOS, OP_TESTK5969, OP_TESTK5816, TESTONEPAY3, TESTPCI1, TESTKBANKCYBER, TESTVPBCYBER, TEST_OP2B, OP_TESTVP4900, TESTIDTEST3, TESTPAYPAL]
2025-08-18 07:10:03.908 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-08-18 07:10:03.908 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-08-18 07:10:03.909 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api approveOrRejectTransaction MA-----------
2025-08-18 07:10:03.909 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/refund/868849 ,transactionId868849 ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811 ,realIp=127.0.0.1 ,jsonData={"id":"868849","op":"replace","path":"/reject","value":{"merchant_id":"868849","transaction_reference":"TESTPCI_1755170440838","currency":"VND"},"skipCallSynchronize":false,"service":"QT"}
2025-08-18 07:10:03.910 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api approveOrRejectTransaction MA------------
2025-08-18 07:10:03.939 [ForkJoinPool.commonPool-worker-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE REFUND API: response code=400 ,response body={"code":400,"message":"Invalid request"}
2025-08-18 07:10:03.939 [ForkJoinPool.commonPool-worker-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - approveOrRejectTransaction result for id 868849: {message=Fail}
2025-08-18 07:10:04.005 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-08-18 07:10:04.005 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - list merchant from permission: [OP_TESTVND, OP_VND01, OP_MPGSVND, OP_MPGSUSD, OP_MPAYVND3D, TESTBIDVV3, ONEPAYHM, D_TEST, OP_CYBSVND, TEST3DSMPGS, OP_MPAYVN1, TESTND, OP_TESTK7011, TESTHD3BEN, TESTSTATICQR, OP_TESTK4121, OP_SACOMTH, TESTAPLVNC, TESTORIFLCBS, TESTVCB_5411, OP_TESTK7399, TESTGGPAY, OP_TESTK5331, TESTKOVENA, TESTSTGINVOICE, TESTMERCHANT16KY, TESTOPVPB, TESTVTBCYBER, HUONG1, TESTOPBIDV, TESTTRAGOP, TESTPCI, TESTONEPAY, OP_TESTUSD, OP_SACOMTRVV, ONEPAY, INVOICETG, TESTDMX, TESTTNS, TESTINVOICE, TESTVCB3D2, TESTBIDV, TESTHC, TEST3DS2_LT, MIGS3DS, TESTAXA1, TESTVF, TESTUPOSTG, TESTAOSVNBANK, OP_TESTKBANK, TESTAPPLE, TESTVCBHM1, TESTTVLOKA, TESTVCB_7399, TESTVAGROUP, TESTVCB_6300, OP_TESTM8211, OP_TESTK7372, OP_TESTK7991, TESTPCI4, TESTOPVCB2, TESTSACOMCYBER, TESTUSD, OP_TESTVP5732, OP_TESTVP6300, ONEPAYMT, TESTATM, OPTEST, TESTTNSUSD, MPAYVND, TESTBIDV1, OP_VPBMPGS3, TESTAWPOSTG, TESTTRAGOP1, TESTSAMSUNG, TESTOP_VTB2, TESTUPOSKBANK, TESTAPLBANK, TESTVCB_8211, TESTMERCHANTID20KYTU, MONPAYOUT, TESTVIETQR, TESTINVOICETG, OP_TESTVP8211, TESTPAYOUT2, TEST, OP_SACOMCBSU, OP_VPBMPGS1, TESTONEPAY20, TESTVJU, OP_TESTCONFIG, OP_TESTSS, OP_TESTBHX, OPTESTHB, TESTAWPOS, TESTACVNC, OP_TESTK4900, TESTONEPAYTHB, ONEPAY_REFUND, TESTAPLTG, OP_TESTK5814, TESTVCB_4722, OP_TESTK5722, TESTPAYOUT1, TESTOPVCB, TESTIDTEST4, TESTONEPAYDVC, OP_SACOMCBSV, OP_SACOMTRVU, ****************, OPMPAY, FALSE, TESTDUONG, OP_VPBMPGS2, TESTSACOMCBU, TESTLZD, TESTSAPO, OP_TESTAUTH, TESTMEGA1, OP_TESTMID, TESTVTASABRE, OP_TESTK4722, TESTOP_VTB3, OP_SACOMVIN, OP_TESTK8220, TESTCNY, TESTVCB_4900, OP_TESTK5698, TESTPCI3, ANHTVTEST, TESTTOKEN, TESTONEPAY2, MONPAYCOLLECT, OP_TESTVP5411, TESTSHOPIFYTG, TESTOP_D, TESTONEPAYDVTT, TESTTRAGOP2, TESTBIDVU3, TESTBIDVV2, TESTTOKENUSD, TESTMEGA, TEST3DS2_MK, TEST3DS2_TN, TESTVFVND, TESTVTB3D2, TESTENETVIET, TESTSHOPIFY, TESTAPPLEND, TESTVCBHM2, TESTTRAGOP3, TESTOKENOP, TESTKONEVA, TESTOP_VTB1, AUTOAPLND, TESTSSPAY, TESTSAMSUNGPL, TESTVCB_5732, TESTAES, OP_TESTK7832, TESTPCI2, TESTJBAUSD, TESTVCBCYBER, TESTMERCHANT15K, OPPREPAID, TESTONEPAYUSD, OP_MPAYVND, TESTSACOMCBV, OP_VCBVND, TESTSAPO2, TESTBIDVCBSU, TESTVCB3D2O, TESTMAFC, OP_TESTOP20, TESTAXA, TESTTGDD3D2, TESTPR, TESTHC1, ONEPAYHB, TESTSSBNPL, OP_TESTK8299, TESTHD2BEN, TESTPCIEM, TESTMCD, OP_TESTK5732, TESTPROMMG, OP_TESTTT, TESTAUTOMSP, OP_TESTUSD2, OP_TESTAPPAY, TESTTHB, TESTSOS, TESTAPPLETG, OP_TESTK5977, EKKO1, TESTIDTEST6, TESTONEPAYDVDT, TESTBIDVCSU2, ****************, OP_VCBUSD, TESTBIDVCBSV, OP_TEST3DS, OP_TEST3DS2V, OP_CYBSUSD, TESTTHSC, TESTBNPL, TESTAOSVNC, TESTPROM, OP_SACOMECOM, TESTQRTINH, TESTTOKENDV, TESTUPOS, OP_TESTK5969, OP_TESTK5816, TESTONEPAY3, TESTPCI1, TESTKBANKCYBER, TESTVPBCYBER, TEST_OP2B, OP_TESTVP4900, TESTIDTEST3, TESTPAYPAL]
2025-08-18 07:10:04.006 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-08-18 07:10:04.006 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-08-18 07:10:04.006 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api approveOrRejectTransaction MA-----------
2025-08-18 07:10:04.006 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/refund/868850 ,transactionId868850 ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811 ,realIp=127.0.0.1 ,jsonData={"id":"868850","op":"replace","path":"/reject","value":{"merchant_id":"868850","transaction_reference":"TESTPCI_1755226501571","currency":"VND"},"skipCallSynchronize":false,"service":"QT"}
2025-08-18 07:10:04.007 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api approveOrRejectTransaction MA------------
2025-08-18 07:10:04.037 [ForkJoinPool.commonPool-worker-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE REFUND API: response code=400 ,response body={"code":400,"message":"Invalid request"}
2025-08-18 07:10:04.037 [ForkJoinPool.commonPool-worker-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - approveOrRejectTransaction result for id 868850: {message=Fail}
2025-08-18 07:10:26.177 [ForkJoinPool.commonPool-worker-3] ERROR v.o.t.s.i.TransactionOldServiceImpl - Approve or reject failed: 868850
