2025-08-26 04:13:19.357 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 3944821 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-08-26 04:13:19.361 [main] INFO  v.o.t.TransactionAppApplication - The following 1 profile is active: "dev"
2025-08-26 04:13:20.109 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-08-26 04:13:20.281 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 165 ms. Found 2 R2DBC repository interfaces.
2025-08-26 04:13:21.806 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Starting...
2025-08-26 04:13:22.095 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleMerchantPortalHikariPool - Added connection oracle.jdbc.driver.T4CConnection@16b76858
2025-08-26 04:13:22.097 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Start completed.
2025-08-26 04:13:22.170 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Starting...
2025-08-26 04:13:22.286 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleOneReportHikariPool - Added connection oracle.jdbc.driver.T4CConnection@6115846e
2025-08-26 04:13:22.287 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Start completed.
2025-08-26 04:13:22.388 [main] INFO  v.o.t.job.PromotionReportJob - Scheduling PromotionReportJob with cron: 0 05 16 * * *
2025-08-26 04:13:22.396 [main] INFO  v.o.t.job.PromotionReportJob - End PromotionReportJob!
2025-08-26 04:13:22.398 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-08-26 04:13:22.399 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-08-26 04:13:22.401 [main] INFO  v.o.transaction.job.UposReportJob - Scheduling UposReportJob with cron: 0 10 16 * * *
2025-08-26 04:13:22.402 [main] INFO  v.o.transaction.job.UposReportJob - End UposReportJob!
2025-08-26 04:13:23.305 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 8397 (http)
2025-08-26 04:13:23.500 [main] INFO  v.o.t.TransactionAppApplication - Started TransactionAppApplication in 4.791 seconds (process running for 6.801)
2025-08-26 04:14:44.894 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: GET http://localhost:8397/ma-service/api/v1/transactions/transaction/list?page=0&size=20&sort=&merchantId=&keyword=&fromDate=2025-08-23T17:00:00.000Z&toDate=2025-08-29T16:59:00.000Z&transactionType=&transactionStatus=&orderSource=&promotion=&paymentMethod=&searchType=all
2025-08-26 04:14:44.895 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-08-26 04:14:44.895 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-08-26 04:14:44.895 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-08-26 04:14:44.895 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-08-26 04:14:44.895 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-08-26 04:14:44.895 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-08-26 04:14:44.895 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/mav2-main/
2025-08-26 04:14:44.895 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-08-26 04:14:44.896 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-08-26 04:14:44.896 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-08-26 04:14:44.896 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-08-26 04:14:44.896 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-08-26 04:14:44.896 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-08-26 04:14:44.896 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-08-26 04:14:44.896 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 417604
2025-08-26 04:14:44.896 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; JSESSIONID=dummy; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQyNTMzNzEsInVzZXIiOnsiZW1haWwiOiJhZG1pbkBvbmVwYXkudm4iLCJmdWxsX25hbWUiOiJBZG1pbiAxIiwiaWQiOiI1MUREN0M5QzdEQTZBMjc4OEY0QUI1M0I1NkJFNjgxMSIsImpvYl90aXRsZSI6IkFkbWluIGFsw7QyMjIiLCJwaG9uZSI6IjA5MDEyMzQ1NjEifX0.v6Hxf8G3cQ8lR330Qq7yAeil8vSpdSiJCEPy0mz3oEg
2025-08-26 04:14:44.896 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-26 04:14:44.896 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 343ba82f-1ff5-4473-9c88-6180f59b8c44
2025-08-26 04:14:44.896 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8397
2025-08-26 04:14:44.896 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-08-26 04:14:44.900 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-08-26 04:14:45.408 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------------start call api getTransactionAll-------------------------
2025-08-26 04:14:45.415 [reactor-tcp-epoll-1] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------------end call api getTransactionAll-------------------------
2025-08-26 04:14:45.480 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Input merchantId param: 
2025-08-26 04:14:45.480 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - list merchant from permission: [OP_TESTVND, OP_VND01, OP_MPGSVND, OP_MPGSUSD, OP_MPAYVND3D, TESTBIDVV3, ONEPAYHM, D_TEST, OP_CYBSVND, TEST3DSMPGS, OP_MPAYVN1, TESTND, OP_TESTK7011, TESTHD3BEN, TESTSTATICQR, OP_TESTK4121, OP_SACOMTH, TESTAPLVNC, TESTORIFLCBS, TESTVCB_5411, OP_TESTK7399, TESTGGPAY, OP_TESTK5331, TESTKOVENA, TESTSTGINVOICE, TESTMERCHANT16KY, TESTOPVPB, TESTVTBCYBER, HUONG1, TESTOPBIDV, TESTTRAGOP, TESTPCI, TESTONEPAY, OP_TESTUSD, OP_SACOMTRVV, ONEPAY, INVOICETG, TESTDMX, TESTTNS, TESTINVOICE, TESTVCB3D2, TESTBIDV, TESTHC, TEST3DS2_LT, MIGS3DS, TESTAXA1, TESTVF, TESTUPOSTG, TESTAOSVNBANK, OP_TESTKBANK, TESTAPPLE, TESTVCBHM1, TESTTVLOKA, TESTVCB_7399, TESTVAGROUP, TESTVCB_6300, OP_TESTM8211, OP_TESTK7372, OP_TESTK7991, TESTPCI4, TESTOPVCB2, TESTSACOMCYBER, TESTUSD, OP_TESTVP5732, OP_TESTVP6300, ONEPAYMT, TESTATM, OPTEST, TESTTNSUSD, MPAYVND, TESTBIDV1, OP_VPBMPGS3, TESTAWPOSTG, TESTTRAGOP1, TESTSAMSUNG, TESTOP_VTB2, TESTUPOSKBANK, TESTAPLBANK, TESTVCB_8211, TESTMERCHANTID20KYTU, MONPAYOUT, TESTVIETQR, TESTINVOICETG, OP_TESTVP8211, TESTPAYOUT2, TEST, OP_SACOMCBSU, OP_VPBMPGS1, TESTONEPAY20, TESTVJU, OP_TESTCONFIG, OP_TESTSS, OP_TESTBHX, OPTESTHB, TESTAWPOS, TESTACVNC, OP_TESTK4900, TESTONEPAYTHB, ONEPAY_REFUND, TESTAPLTG, OP_TESTK5814, TESTVCB_4722, OP_TESTK5722, TESTPAYOUT1, TESTOPVCB, TESTIDTEST4, TESTONEPAYDVC, OP_SACOMCBSV, OP_SACOMTRVU, ****************, OPMPAY, FALSE, TESTDUONG, OP_VPBMPGS2, TESTSACOMCBU, TESTLZD, TESTSAPO, OP_TESTAUTH, TESTMEGA1, OP_TESTMID, TESTVTASABRE, OP_TESTK4722, TESTOP_VTB3, OP_SACOMVIN, OP_TESTK8220, TESTCNY, TESTVCB_4900, OP_TESTK5698, TESTPCI3, ANHTVTEST, TESTTOKEN, TESTONEPAY2, MONPAYCOLLECT, OP_TESTVP5411, TESTSHOPIFYTG, TESTOP_D, TESTONEPAYDVTT, TESTTRAGOP2, TESTBIDVU3, TESTBIDVV2, TESTTOKENUSD, TESTMEGA, TEST3DS2_MK, TEST3DS2_TN, TESTVFVND, TESTVTB3D2, TESTENETVIET, TESTSHOPIFY, TESTAPPLEND, TESTVCBHM2, TESTTRAGOP3, TESTOKENOP, TESTKONEVA, TESTOP_VTB1, AUTOAPLND, TESTSSPAY, TESTSAMSUNGPL, TESTVCB_5732, TESTAES, OP_TESTK7832, TESTPCI2, TESTJBAUSD, TESTVCBCYBER, TESTMERCHANT15K, OPPREPAID, TESTONEPAYUSD, OP_MPAYVND, TESTSACOMCBV, OP_VCBVND, TESTSAPO2, TESTBIDVCBSU, TESTVCB3D2O, TESTMAFC, OP_TESTOP20, TESTAXA, TESTTGDD3D2, TESTPR, TESTHC1, ONEPAYHB, TESTSSBNPL, OP_TESTK8299, TESTHD2BEN, TESTPCIEM, TESTMCD, OP_TESTK5732, TESTPROMMG, OP_TESTTT, TESTAUTOMSP, OP_TESTUSD2, OP_TESTAPPAY, TESTTHB, TESTSOS, TESTAPPLETG, OP_TESTK5977, EKKO1, TESTONEPAY1, TESTIDTEST6, TESTONEPAYDVDT, TESTBIDVCSU2, ****************, OP_VCBUSD, TESTBIDVCBSV, OP_TEST3DS, OP_TEST3DS2V, OP_CYBSUSD, TESTTHSC, TESTBNPL, TESTAOSVNC, TESTPROM, OP_SACOMECOM, TESTQRTINH, TESTTOKENDV, TESTUPOS, OP_TESTK5969, OP_TESTK5816, TESTONEPAY3, TESTPCI1, TESTKBANKCYBER, TESTVPBCYBER, TEST_OP2B, OP_TESTVP4900, TESTIDTEST3, TESTPAYPAL]
2025-08-26 04:14:51.688 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - Result: ORA-01789: query block has incorrect number of result columns
        SELECT t1.* FROM (
            SELECT * FROM (
                SELECT ROWNUM rnum, tmp.*
                FROM (( SELECT
                                qr.s_app_name          AS s_card_type,
                                NULL                AS s_response_code,
                                'QR'                AS payment_channel,
                                NULL                AS BIN_COUNTRY,
                                qr.s_merchant_id, -- Qualified
                                qr.s_id                AS s_transaction_id,
                                TO_CHAR(qr.s_original_id) AS s_original_id,
                                qr.s_order_info,
                                qr.s_merch_txn_ref     AS S_MERCHANT_TRANS_REF,
                                qr.n_amount,
                                qr.s_currency,
                                qr.d_create            AS d_transaction_date,
                                qr.s_instrument_number AS s_card_number,
                                qr.s_advance_status    AS s_transaction_state,
                                qr.s_transaction_type,
                                'QR'                AS s_input_type,
                                qr.s_app_name,
                                qr.s_order_id,
								parent_qr.d_create AS d_order_date 
                            FROM
                                tb_ma_qr_txn qr
							JOIN tb_ma_qr_txn parent_qr ON parent_qr.s_id = qr.s_id
							LEFT JOIN TB_MA_PR_TXN pr
					            ON pr.S_MERCHANT_ID = qr.S_MERCHANT_ID
					            AND pr.S_TRANSACTION_REFERENCE = qr.s_merch_txn_ref    WHERE qr.d_create >= :v_d_from_date AND qr.d_create < :v_d_to_date AND qr.s_transaction_type <> 'Request Refund' AND qr.S_STATE NOT IN ( '400', '404', '403') and qr.S_MERCHANT_ID in ('OP_TESTVND','OP_VND01','OP_MPGSVND','OP_MPGSUSD','OP_MPAYVND3D','TESTBIDVV3','ONEPAYHM','D_TEST','OP_CYBSVND','TEST3DSMPGS','OP_MPAYVN1','TESTND','OP_TESTK7011','TESTHD3BEN','TESTSTATICQR','OP_TESTK4121','OP_SACOMTH','TESTAPLVNC','TESTORIFLCBS','TESTVCB_5411','OP_TESTK7399','TESTGGPAY','OP_TESTK5331','TESTKOVENA','TESTSTGINVOICE','TESTMERCHANT16KY','TESTOPVPB','TESTVTBCYBER','HUONG1','TESTOPBIDV','TESTTRAGOP','TESTPCI','TESTONEPAY','OP_TESTUSD','OP_SACOMTRVV','ONEPAY','INVOICETG','TESTDMX','TESTTNS','TESTINVOICE','TESTVCB3D2','TESTBIDV','TESTHC','TEST3DS2_LT','MIGS3DS','TESTAXA1','TESTVF','TESTUPOSTG','TESTAOSVNBANK','OP_TESTKBANK','TESTAPPLE','TESTVCBHM1','TESTTVLOKA','TESTVCB_7399','TESTVAGROUP','TESTVCB_6300','OP_TESTM8211','OP_TESTK7372','OP_TESTK7991','TESTPCI4','TESTOPVCB2','TESTSACOMCYBER','TESTUSD','OP_TESTVP5732','OP_TESTVP6300','ONEPAYMT','TESTATM','OPTEST','TESTTNSUSD','MPAYVND','TESTBIDV1','OP_VPBMPGS3','TESTAWPOSTG','TESTTRAGOP1','TESTSAMSUNG','TESTOP_VTB2','TESTUPOSKBANK','TESTAPLBANK','TESTVCB_8211','TESTMERCHANTID20KYTU','MONPAYOUT','TESTVIETQR','TESTINVOICETG','OP_TESTVP8211','TESTPAYOUT2','TEST','OP_SACOMCBSU','OP_VPBMPGS1','TESTONEPAY20','TESTVJU','OP_TESTCONFIG','OP_TESTSS','OP_TESTBHX','OPTESTHB','TESTAWPOS','TESTACVNC','OP_TESTK4900','TESTONEPAYTHB','ONEPAY_REFUND','TESTAPLTG','OP_TESTK5814','TESTVCB_4722','OP_TESTK5722','TESTPAYOUT1','TESTOPVCB','TESTIDTEST4','TESTONEPAYDVC','OP_SACOMCBSV','OP_SACOMTRVU','****************','OPMPAY','FALSE','TESTDUONG','OP_VPBMPGS2','TESTSACOMCBU','TESTLZD','TESTSAPO','OP_TESTAUTH','TESTMEGA1','OP_TESTMID','TESTVTASABRE','OP_TESTK4722','TESTOP_VTB3','OP_SACOMVIN','OP_TESTK8220','TESTCNY','TESTVCB_4900','OP_TESTK5698','TESTPCI3','ANHTVTEST','TESTTOKEN','TESTONEPAY2','MONPAYCOLLECT','OP_TESTVP5411','TESTSHOPIFYTG','TESTOP_D','TESTONEPAYDVTT','TESTTRAGOP2','TESTBIDVU3','TESTBIDVV2','TESTTOKENUSD','TESTMEGA','TEST3DS2_MK','TEST3DS2_TN','TESTVFVND','TESTVTB3D2','TESTENETVIET','TESTSHOPIFY','TESTAPPLEND','TESTVCBHM2','TESTTRAGOP3','TESTOKENOP','TESTKONEVA','TESTOP_VTB1','AUTOAPLND','TESTSSPAY','TESTSAMSUNGPL','TESTVCB_5732','TESTAES','OP_TESTK7832','TESTPCI2','TESTJBAUSD','TESTVCBCYBER','TESTMERCHANT15K','OPPREPAID','TESTONEPAYUSD','OP_MPAYVND','TESTSACOMCBV','OP_VCBVND','TESTSAPO2','TESTBIDVCBSU','TESTVCB3D2O','TESTMAFC','OP_TESTOP20','TESTAXA','TESTTGDD3D2','TESTPR','TESTHC1','ONEPAYHB','TESTSSBNPL','OP_TESTK8299','TESTHD2BEN','TESTPCIEM','TESTMCD','OP_TESTK5732','TESTPROMMG','OP_TESTTT','TESTAUTOMSP','OP_TESTUSD2','OP_TESTAPPAY','TESTTHB','TESTSOS','TESTAPPLETG','OP_TESTK5977','EKKO1','TESTONEPAY1','TESTIDTEST6','TESTONEPAYDVDT','TESTBIDVCSU2','****************','OP_VCBUSD','TESTBIDVCBSV','OP_TEST3DS','OP_TEST3DS2V','OP_CYBSUSD','TESTTHSC','TESTBNPL','TESTAOSVNC','TESTPROM','OP_SACOMECOM','TESTQRTINH','TESTTOKENDV','TESTUPOS','OP_TESTK5969','OP_TESTK5816','TESTONEPAY3','TESTPCI1','TESTKBANKCYBER','TESTVPBCYBER','TEST_OP2B','OP_TESTVP4900','TESTIDTEST3','TESTPAYPAL')  UNION ALL SELECT
                                dom.s_acquirer_name             AS s_card_type,
                                dom.s_response_code             AS s_response_code,
                                'DOM'                       AS payment_channel,
                                NULL                        AS BIN_COUNTRY,
                                dom.s_merchant_id, -- Qualified
                                to_char(dom.n_transaction_id)   AS s_transaction_id,
                                to_char(dom.n_original_id)      AS s_original_id,
                                dom.s_order_info,
                                dom.s_merchant_transaction_ref  AS S_MERCHANT_TRANS_REF,
                                dom.n_amount,
                                dom.s_currency_code             AS s_currency,
                                dom.d_merchant_transaction_date AS d_transaction_date,
                                dom.s_card_number               AS s_card_number,
                                dom.s_advance_status            AS s_transaction_state,
                                dom.s_transaction_type,
                                'ND'                        AS s_input_type,
                                ''                          AS s_app_name,
                                dom.s_order_id,
								dom.d_merchant_transaction_date AS d_order_date
                            FROM
                                tb_ma_domestic_txn dom
							JOIN tb_ma_domestic_txn parent_dom ON parent_dom.n_transaction_id = dom.n_original_id
                            LEFT JOIN TB_MA_PR_TXN pr
                                ON pr.S_MERCHANT_ID = dom.S_MERCHANT_ID
                                AND pr.S_TRANSACTION_REFERENCE = dom.s_merchant_transaction_ref   WHERE dom.d_merchant_transaction_date >= :v_d_from_date AND dom.d_merchant_transaction_date < :v_d_to_date AND dom.s_transaction_type <> 'Request Refund'
            AND (dom.S_TRANSACTION_TYPE != 'Request Refund' or (dom.S_TRANSACTION_TYPE = 'Request Refund' AND dom.N_TRANSACTION_STATUS IN (300, 401))) and dom.S_MERCHANT_ID in ('OP_TESTVND','OP_VND01','OP_MPGSVND','OP_MPGSUSD','OP_MPAYVND3D','TESTBIDVV3','ONEPAYHM','D_TEST','OP_CYBSVND','TEST3DSMPGS','OP_MPAYVN1','TESTND','OP_TESTK7011','TESTHD3BEN','TESTSTATICQR','OP_TESTK4121','OP_SACOMTH','TESTAPLVNC','TESTORIFLCBS','TESTVCB_5411','OP_TESTK7399','TESTGGPAY','OP_TESTK5331','TESTKOVENA','TESTSTGINVOICE','TESTMERCHANT16KY','TESTOPVPB','TESTVTBCYBER','HUONG1','TESTOPBIDV','TESTTRAGOP','TESTPCI','TESTONEPAY','OP_TESTUSD','OP_SACOMTRVV','ONEPAY','INVOICETG','TESTDMX','TESTTNS','TESTINVOICE','TESTVCB3D2','TESTBIDV','TESTHC','TEST3DS2_LT','MIGS3DS','TESTAXA1','TESTVF','TESTUPOSTG','TESTAOSVNBANK','OP_TESTKBANK','TESTAPPLE','TESTVCBHM1','TESTTVLOKA','TESTVCB_7399','TESTVAGROUP','TESTVCB_6300','OP_TESTM8211','OP_TESTK7372','OP_TESTK7991','TESTPCI4','TESTOPVCB2','TESTSACOMCYBER','TESTUSD','OP_TESTVP5732','OP_TESTVP6300','ONEPAYMT','TESTATM','OPTEST','TESTTNSUSD','MPAYVND','TESTBIDV1','OP_VPBMPGS3','TESTAWPOSTG','TESTTRAGOP1','TESTSAMSUNG','TESTOP_VTB2','TESTUPOSKBANK','TESTAPLBANK','TESTVCB_8211','TESTMERCHANTID20KYTU','MONPAYOUT','TESTVIETQR','TESTINVOICETG','OP_TESTVP8211','TESTPAYOUT2','TEST','OP_SACOMCBSU','OP_VPBMPGS1','TESTONEPAY20','TESTVJU','OP_TESTCONFIG','OP_TESTSS','OP_TESTBHX','OPTESTHB','TESTAWPOS','TESTACVNC','OP_TESTK4900','TESTONEPAYTHB','ONEPAY_REFUND','TESTAPLTG','OP_TESTK5814','TESTVCB_4722','OP_TESTK5722','TESTPAYOUT1','TESTOPVCB','TESTIDTEST4','TESTONEPAYDVC','OP_SACOMCBSV','OP_SACOMTRVU','****************','OPMPAY','FALSE','TESTDUONG','OP_VPBMPGS2','TESTSACOMCBU','TESTLZD','TESTSAPO','OP_TESTAUTH','TESTMEGA1','OP_TESTMID','TESTVTASABRE','OP_TESTK4722','TESTOP_VTB3','OP_SACOMVIN','OP_TESTK8220','TESTCNY','TESTVCB_4900','OP_TESTK5698','TESTPCI3','ANHTVTEST','TESTTOKEN','TESTONEPAY2','MONPAYCOLLECT','OP_TESTVP5411','TESTSHOPIFYTG','TESTOP_D','TESTONEPAYDVTT','TESTTRAGOP2','TESTBIDVU3','TESTBIDVV2','TESTTOKENUSD','TESTMEGA','TEST3DS2_MK','TEST3DS2_TN','TESTVFVND','TESTVTB3D2','TESTENETVIET','TESTSHOPIFY','TESTAPPLEND','TESTVCBHM2','TESTTRAGOP3','TESTOKENOP','TESTKONEVA','TESTOP_VTB1','AUTOAPLND','TESTSSPAY','TESTSAMSUNGPL','TESTVCB_5732','TESTAES','OP_TESTK7832','TESTPCI2','TESTJBAUSD','TESTVCBCYBER','TESTMERCHANT15K','OPPREPAID','TESTONEPAYUSD','OP_MPAYVND','TESTSACOMCBV','OP_VCBVND','TESTSAPO2','TESTBIDVCBSU','TESTVCB3D2O','TESTMAFC','OP_TESTOP20','TESTAXA','TESTTGDD3D2','TESTPR','TESTHC1','ONEPAYHB','TESTSSBNPL','OP_TESTK8299','TESTHD2BEN','TESTPCIEM','TESTMCD','OP_TESTK5732','TESTPROMMG','OP_TESTTT','TESTAUTOMSP','OP_TESTUSD2','OP_TESTAPPAY','TESTTHB','TESTSOS','TESTAPPLETG','OP_TESTK5977','EKKO1','TESTONEPAY1','TESTIDTEST6','TESTONEPAYDVDT','TESTBIDVCSU2','****************','OP_VCBUSD','TESTBIDVCBSV','OP_TEST3DS','OP_TEST3DS2V','OP_CYBSUSD','TESTTHSC','TESTBNPL','TESTAOSVNC','TESTPROM','OP_SACOMECOM','TESTQRTINH','TESTTOKENDV','TESTUPOS','OP_TESTK5969','OP_TESTK5816','TESTONEPAY3','TESTPCI1','TESTKBANKCYBER','TESTVPBCYBER','TEST_OP2B','OP_TESTVP4900','TESTIDTEST3','TESTPAYPAL')  AND NOT (dom.N_CARD_VERIFICATION_CODE = 99 AND dom.n_transaction_status = 100)  -- case normal user
     AND NOT (dom.N_CARD_VERIFICATION_CODE = 100 AND dom.n_transaction_status = 100)
     AND NOT (dom.N_CARD_VERIFICATION_CODE <> 99 AND (dom.N_BANK_ID = 2 OR dom.N_BANK_ID = 6 OR dom.N_BANK_ID = 5 OR dom.N_BANK_ID = 31 OR dom.N_BANK_ID = 21) AND dom.n_transaction_status = 200 )
     AND NOT (
              dom.n_transaction_status = 100
              and dom.s_card_number is null
              and dom.n_bank_id  >= 0
            ) UNION ALL SELECT
                                int.s_card_type            AS s_card_type,
                                int.s_response_code        AS s_response_code,
                                'INT'                  AS payment_channel,
                                int.bin_country            AS BIN_COUNTRY,
                                int.s_merchant_id          AS s_merchant_id,
                                to_char(int.n_id) AS s_transaction_id,
                                to_char(int.onecredit_trans_id) AS s_original_id,
                                int.s_order_ref            AS s_order_info,
                                int.s_trans_ref            AS S_MERCHANT_TRANS_REF,
                                int.n_amount               AS n_amount,
                                int.s_currency             AS s_currency,
                                int.d_date                 AS d_transaction_date,
                                int.s_cardno               AS s_card_number,
                                int.s_advance_status       AS s_transaction_state,
                                int.s_trans_type           AS s_transaction_type,
                                'QT'                   AS s_input_type,
                                ''                     AS s_app_name,
                                int.s_order_id,
								parent_int.d_date AS d_order_date
                            FROM
                                tb_ma_international_txn int
							JOIN tb_ma_international_txn parent_int ON parent_int.n_id = int.n_id
                            LEFT JOIN TB_MA_PR_TXN pr
                                ON pr.S_MERCHANT_ID = int.S_MERCHANT_ID
                                AND pr.S_TRANSACTION_REFERENCE = int.S_TRANS_REF   WHERE int.d_date >= :v_d_from_date AND int.d_date < :v_d_to_date AND int.s_trans_type <> 'Request Refund'
            AND int.s_advance_status NOT IN ('Merchant Rejected','OnePays rejected','Merchant Approved','OnePays approved' )  and int.S_MERCHANT_ID in ('OP_TESTVND','OP_VND01','OP_MPGSVND','OP_MPGSUSD','OP_MPAYVND3D','TESTBIDVV3','ONEPAYHM','D_TEST','OP_CYBSVND','TEST3DSMPGS','OP_MPAYVN1','TESTND','OP_TESTK7011','TESTHD3BEN','TESTSTATICQR','OP_TESTK4121','OP_SACOMTH','TESTAPLVNC','TESTORIFLCBS','TESTVCB_5411','OP_TESTK7399','TESTGGPAY','OP_TESTK5331','TESTKOVENA','TESTSTGINVOICE','TESTMERCHANT16KY','TESTOPVPB','TESTVTBCYBER','HUONG1','TESTOPBIDV','TESTTRAGOP','TESTPCI','TESTONEPAY','OP_TESTUSD','OP_SACOMTRVV','ONEPAY','INVOICETG','TESTDMX','TESTTNS','TESTINVOICE','TESTVCB3D2','TESTBIDV','TESTHC','TEST3DS2_LT','MIGS3DS','TESTAXA1','TESTVF','TESTUPOSTG','TESTAOSVNBANK','OP_TESTKBANK','TESTAPPLE','TESTVCBHM1','TESTTVLOKA','TESTVCB_7399','TESTVAGROUP','TESTVCB_6300','OP_TESTM8211','OP_TESTK7372','OP_TESTK7991','TESTPCI4','TESTOPVCB2','TESTSACOMCYBER','TESTUSD','OP_TESTVP5732','OP_TESTVP6300','ONEPAYMT','TESTATM','OPTEST','TESTTNSUSD','MPAYVND','TESTBIDV1','OP_VPBMPGS3','TESTAWPOSTG','TESTTRAGOP1','TESTSAMSUNG','TESTOP_VTB2','TESTUPOSKBANK','TESTAPLBANK','TESTVCB_8211','TESTMERCHANTID20KYTU','MONPAYOUT','TESTVIETQR','TESTINVOICETG','OP_TESTVP8211','TESTPAYOUT2','TEST','OP_SACOMCBSU','OP_VPBMPGS1','TESTONEPAY20','TESTVJU','OP_TESTCONFIG','OP_TESTSS','OP_TESTBHX','OPTESTHB','TESTAWPOS','TESTACVNC','OP_TESTK4900','TESTONEPAYTHB','ONEPAY_REFUND','TESTAPLTG','OP_TESTK5814','TESTVCB_4722','OP_TESTK5722','TESTPAYOUT1','TESTOPVCB','TESTIDTEST4','TESTONEPAYDVC','OP_SACOMCBSV','OP_SACOMTRVU','****************','OPMPAY','FALSE','TESTDUONG','OP_VPBMPGS2','TESTSACOMCBU','TESTLZD','TESTSAPO','OP_TESTAUTH','TESTMEGA1','OP_TESTMID','TESTVTASABRE','OP_TESTK4722','TESTOP_VTB3','OP_SACOMVIN','OP_TESTK8220','TESTCNY','TESTVCB_4900','OP_TESTK5698','TESTPCI3','ANHTVTEST','TESTTOKEN','TESTONEPAY2','MONPAYCOLLECT','OP_TESTVP5411','TESTSHOPIFYTG','TESTOP_D','TESTONEPAYDVTT','TESTTRAGOP2','TESTBIDVU3','TESTBIDVV2','TESTTOKENUSD','TESTMEGA','TEST3DS2_MK','TEST3DS2_TN','TESTVFVND','TESTVTB3D2','TESTENETVIET','TESTSHOPIFY','TESTAPPLEND','TESTVCBHM2','TESTTRAGOP3','TESTOKENOP','TESTKONEVA','TESTOP_VTB1','AUTOAPLND','TESTSSPAY','TESTSAMSUNGPL','TESTVCB_5732','TESTAES','OP_TESTK7832','TESTPCI2','TESTJBAUSD','TESTVCBCYBER','TESTMERCHANT15K','OPPREPAID','TESTONEPAYUSD','OP_MPAYVND','TESTSACOMCBV','OP_VCBVND','TESTSAPO2','TESTBIDVCBSU','TESTVCB3D2O','TESTMAFC','OP_TESTOP20','TESTAXA','TESTTGDD3D2','TESTPR','TESTHC1','ONEPAYHB','TESTSSBNPL','OP_TESTK8299','TESTHD2BEN','TESTPCIEM','TESTMCD','OP_TESTK5732','TESTPROMMG','OP_TESTTT','TESTAUTOMSP','OP_TESTUSD2','OP_TESTAPPAY','TESTTHB','TESTSOS','TESTAPPLETG','OP_TESTK5977','EKKO1','TESTONEPAY1','TESTIDTEST6','TESTONEPAYDVDT','TESTBIDVCSU2','****************','OP_VCBUSD','TESTBIDVCBSV','OP_TEST3DS','OP_TEST3DS2V','OP_CYBSUSD','TESTTHSC','TESTBNPL','TESTAOSVNC','TESTPROM','OP_SACOMECOM','TESTQRTINH','TESTTOKENDV','TESTUPOS','OP_TESTK5969','OP_TESTK5816','TESTONEPAY3','TESTPCI1','TESTKBANKCYBER','TESTVPBCYBER','TEST_OP2B','OP_TESTVP4900','TESTIDTEST3','TESTPAYPAL')  UNION ALL SELECT
                                ''                                                     AS s_card_type,
                                onedata.CONVERT_RESPONSE_CODE_BNPL_V2(
                                    bnpl.S_TRANSACTION_TYPE,
                                    bnpl.S_TRANSACTION_TYPE,
                                    bnpl.S_TRANSACTION_TYPE
                                )                                                      AS s_response_code,
                                'BNPL'                                                 AS payment_channel,
                                ''                                                     AS BIN_COUNTRY,
                                bnpl.s_merchant_id                                     AS s_merchant_id,
                                bnpl.s_id                                              AS s_transaction_id,
                                bnpl.s_original_id                                              AS s_original_id,
                                bnpl.S_ORDER_INFO                                      AS s_order_info,
                                bnpl.S_MERCH_TXN_REF                                   AS S_MERCHANT_TRANS_REF,
                                bnpl.n_amount                                          AS n_amount,
                                bnpl.s_currency                                        AS s_currency,
                                bnpl.d_create                                          AS d_transaction_date,
                                ''                                                     AS s_card_number, -- sửa lại từ dòng cũ sai cú pháp: '' state AS...
                                bnpl.s_state                                           AS s_transaction_state,
                                bnpl.S_TRANSACTION_TYPE                                AS s_transaction_type,
                                'BNPL'                                                 AS s_input_type,
                                ''                                                     AS s_app_name,
                                ''                                                     AS s_order_id,
								parent_bnpl.d_create 					               AS d_order_date
                            FROM
                                tb_ma_bnpl_txn bnpl
							JOIN tb_ma_bnpl_txn parent_bnpl ON parent_bnpl.s_id = bnpl.s_id
                            LEFT JOIN TB_MA_PR_TXN pr
                                ON pr.S_MERCHANT_ID = bnpl.S_MERCHANT_ID
                                AND pr.S_TRANSACTION_REFERENCE = bnpl.S_MERCH_TXN_REF   WHERE bnpl.d_create >= :v_d_from_date AND bnpl.d_create < :v_d_to_date AND bnpl.S_TRANSACTION_TYPE <> 'Request Refund'  and bnpl.S_MERCHANT_ID in ('OP_TESTVND','OP_VND01','OP_MPGSVND','OP_MPGSUSD','OP_MPAYVND3D','TESTBIDVV3','ONEPAYHM','D_TEST','OP_CYBSVND','TEST3DSMPGS','OP_MPAYVN1','TESTND','OP_TESTK7011','TESTHD3BEN','TESTSTATICQR','OP_TESTK4121','OP_SACOMTH','TESTAPLVNC','TESTORIFLCBS','TESTVCB_5411','OP_TESTK7399','TESTGGPAY','OP_TESTK5331','TESTKOVENA','TESTSTGINVOICE','TESTMERCHANT16KY','TESTOPVPB','TESTVTBCYBER','HUONG1','TESTOPBIDV','TESTTRAGOP','TESTPCI','TESTONEPAY','OP_TESTUSD','OP_SACOMTRVV','ONEPAY','INVOICETG','TESTDMX','TESTTNS','TESTINVOICE','TESTVCB3D2','TESTBIDV','TESTHC','TEST3DS2_LT','MIGS3DS','TESTAXA1','TESTVF','TESTUPOSTG','TESTAOSVNBANK','OP_TESTKBANK','TESTAPPLE','TESTVCBHM1','TESTTVLOKA','TESTVCB_7399','TESTVAGROUP','TESTVCB_6300','OP_TESTM8211','OP_TESTK7372','OP_TESTK7991','TESTPCI4','TESTOPVCB2','TESTSACOMCYBER','TESTUSD','OP_TESTVP5732','OP_TESTVP6300','ONEPAYMT','TESTATM','OPTEST','TESTTNSUSD','MPAYVND','TESTBIDV1','OP_VPBMPGS3','TESTAWPOSTG','TESTTRAGOP1','TESTSAMSUNG','TESTOP_VTB2','TESTUPOSKBANK','TESTAPLBANK','TESTVCB_8211','TESTMERCHANTID20KYTU','MONPAYOUT','TESTVIETQR','TESTINVOICETG','OP_TESTVP8211','TESTPAYOUT2','TEST','OP_SACOMCBSU','OP_VPBMPGS1','TESTONEPAY20','TESTVJU','OP_TESTCONFIG','OP_TESTSS','OP_TESTBHX','OPTESTHB','TESTAWPOS','TESTACVNC','OP_TESTK4900','TESTONEPAYTHB','ONEPAY_REFUND','TESTAPLTG','OP_TESTK5814','TESTVCB_4722','OP_TESTK5722','TESTPAYOUT1','TESTOPVCB','TESTIDTEST4','TESTONEPAYDVC','OP_SACOMCBSV','OP_SACOMTRVU','****************','OPMPAY','FALSE','TESTDUONG','OP_VPBMPGS2','TESTSACOMCBU','TESTLZD','TESTSAPO','OP_TESTAUTH','TESTMEGA1','OP_TESTMID','TESTVTASABRE','OP_TESTK4722','TESTOP_VTB3','OP_SACOMVIN','OP_TESTK8220','TESTCNY','TESTVCB_4900','OP_TESTK5698','TESTPCI3','ANHTVTEST','TESTTOKEN','TESTONEPAY2','MONPAYCOLLECT','OP_TESTVP5411','TESTSHOPIFYTG','TESTOP_D','TESTONEPAYDVTT','TESTTRAGOP2','TESTBIDVU3','TESTBIDVV2','TESTTOKENUSD','TESTMEGA','TEST3DS2_MK','TEST3DS2_TN','TESTVFVND','TESTVTB3D2','TESTENETVIET','TESTSHOPIFY','TESTAPPLEND','TESTVCBHM2','TESTTRAGOP3','TESTOKENOP','TESTKONEVA','TESTOP_VTB1','AUTOAPLND','TESTSSPAY','TESTSAMSUNGPL','TESTVCB_5732','TESTAES','OP_TESTK7832','TESTPCI2','TESTJBAUSD','TESTVCBCYBER','TESTMERCHANT15K','OPPREPAID','TESTONEPAYUSD','OP_MPAYVND','TESTSACOMCBV','OP_VCBVND','TESTSAPO2','TESTBIDVCBSU','TESTVCB3D2O','TESTMAFC','OP_TESTOP20','TESTAXA','TESTTGDD3D2','TESTPR','TESTHC1','ONEPAYHB','TESTSSBNPL','OP_TESTK8299','TESTHD2BEN','TESTPCIEM','TESTMCD','OP_TESTK5732','TESTPROMMG','OP_TESTTT','TESTAUTOMSP','OP_TESTUSD2','OP_TESTAPPAY','TESTTHB','TESTSOS','TESTAPPLETG','OP_TESTK5977','EKKO1','TESTONEPAY1','TESTIDTEST6','TESTONEPAYDVDT','TESTBIDVCSU2','****************','OP_VCBUSD','TESTBIDVCBSV','OP_TEST3DS','OP_TEST3DS2V','OP_CYBSUSD','TESTTHSC','TESTBNPL','TESTAOSVNC','TESTPROM','OP_SACOMECOM','TESTQRTINH','TESTTOKENDV','TESTUPOS','OP_TESTK5969','OP_TESTK5816','TESTONEPAY3','TESTPCI1','TESTKBANKCYBER','TESTVPBCYBER','TEST_OP2B','OP_TESTVP4900','TESTIDTEST3','TESTPAYPAL')  UNION ALL SELECT
                                vietqr.s_card_type       AS s_card_type,
                                NULL              AS s_response_code,
                                'VIETQR'          AS payment_channel,
                                NULL              AS bin_country,
                                vietqr.s_merchant_id,
                                vietqr.s_id              AS s_transaction_id,
                                vietqr.s_original_id     AS s_original_id,
                                vietqr.s_merch_order_ref AS s_order_info,
                                vietqr.s_merch_txn_ref   AS s_merchant_trans_ref,
                                vietqr.n_amount,
                                vietqr.s_currency,
                                vietqr.d_transaction     AS d_transaction_date,
                                '***'             AS s_card_number,
                                vietqr.s_advance_status  AS s_transaction_state,
                                vietqr.s_trans_type      AS s_transaction_type,
                                'VIETQR'          AS s_input_type,
                                ''                AS s_app_name,
                                vietqr.s_order_id
                            FROM
                                tb_ma_vietqr_txn vietqr
                            LEFT JOIN TB_MA_PR_TXN pr
                                ON pr.S_MERCHANT_ID = vietqr.S_MERCHANT_ID
                                AND pr.S_TRANSACTION_REFERENCE = vietqr.S_MERCH_TXN_REF   WHERE vietqr.d_transaction >= :v_d_from_date AND vietqr.d_transaction < :v_d_to_date AND vietqr.s_trans_type <> 'Request Refund'
                                AND vietqr.s_state NOT IN ( '200', '310', '400', '404', '403' )  and vietqr.S_MERCHANT_ID in ('OP_TESTVND','OP_VND01','OP_MPGSVND','OP_MPGSUSD','OP_MPAYVND3D','TESTBIDVV3','ONEPAYHM','D_TEST','OP_CYBSVND','TEST3DSMPGS','OP_MPAYVN1','TESTND','OP_TESTK7011','TESTHD3BEN','TESTSTATICQR','OP_TESTK4121','OP_SACOMTH','TESTAPLVNC','TESTORIFLCBS','TESTVCB_5411','OP_TESTK7399','TESTGGPAY','OP_TESTK5331','TESTKOVENA','TESTSTGINVOICE','TESTMERCHANT16KY','TESTOPVPB','TESTVTBCYBER','HUONG1','TESTOPBIDV','TESTTRAGOP','TESTPCI','TESTONEPAY','OP_TESTUSD','OP_SACOMTRVV','ONEPAY','INVOICETG','TESTDMX','TESTTNS','TESTINVOICE','TESTVCB3D2','TESTBIDV','TESTHC','TEST3DS2_LT','MIGS3DS','TESTAXA1','TESTVF','TESTUPOSTG','TESTAOSVNBANK','OP_TESTKBANK','TESTAPPLE','TESTVCBHM1','TESTTVLOKA','TESTVCB_7399','TESTVAGROUP','TESTVCB_6300','OP_TESTM8211','OP_TESTK7372','OP_TESTK7991','TESTPCI4','TESTOPVCB2','TESTSACOMCYBER','TESTUSD','OP_TESTVP5732','OP_TESTVP6300','ONEPAYMT','TESTATM','OPTEST','TESTTNSUSD','MPAYVND','TESTBIDV1','OP_VPBMPGS3','TESTAWPOSTG','TESTTRAGOP1','TESTSAMSUNG','TESTOP_VTB2','TESTUPOSKBANK','TESTAPLBANK','TESTVCB_8211','TESTMERCHANTID20KYTU','MONPAYOUT','TESTVIETQR','TESTINVOICETG','OP_TESTVP8211','TESTPAYOUT2','TEST','OP_SACOMCBSU','OP_VPBMPGS1','TESTONEPAY20','TESTVJU','OP_TESTCONFIG','OP_TESTSS','OP_TESTBHX','OPTESTHB','TESTAWPOS','TESTACVNC','OP_TESTK4900','TESTONEPAYTHB','ONEPAY_REFUND','TESTAPLTG','OP_TESTK5814','TESTVCB_4722','OP_TESTK5722','TESTPAYOUT1','TESTOPVCB','TESTIDTEST4','TESTONEPAYDVC','OP_SACOMCBSV','OP_SACOMTRVU','****************','OPMPAY','FALSE','TESTDUONG','OP_VPBMPGS2','TESTSACOMCBU','TESTLZD','TESTSAPO','OP_TESTAUTH','TESTMEGA1','OP_TESTMID','TESTVTASABRE','OP_TESTK4722','TESTOP_VTB3','OP_SACOMVIN','OP_TESTK8220','TESTCNY','TESTVCB_4900','OP_TESTK5698','TESTPCI3','ANHTVTEST','TESTTOKEN','TESTONEPAY2','MONPAYCOLLECT','OP_TESTVP5411','TESTSHOPIFYTG','TESTOP_D','TESTONEPAYDVTT','TESTTRAGOP2','TESTBIDVU3','TESTBIDVV2','TESTTOKENUSD','TESTMEGA','TEST3DS2_MK','TEST3DS2_TN','TESTVFVND','TESTVTB3D2','TESTENETVIET','TESTSHOPIFY','TESTAPPLEND','TESTVCBHM2','TESTTRAGOP3','TESTOKENOP','TESTKONEVA','TESTOP_VTB1','AUTOAPLND','TESTSSPAY','TESTSAMSUNGPL','TESTVCB_5732','TESTAES','OP_TESTK7832','TESTPCI2','TESTJBAUSD','TESTVCBCYBER','TESTMERCHANT15K','OPPREPAID','TESTONEPAYUSD','OP_MPAYVND','TESTSACOMCBV','OP_VCBVND','TESTSAPO2','TESTBIDVCBSU','TESTVCB3D2O','TESTMAFC','OP_TESTOP20','TESTAXA','TESTTGDD3D2','TESTPR','TESTHC1','ONEPAYHB','TESTSSBNPL','OP_TESTK8299','TESTHD2BEN','TESTPCIEM','TESTMCD','OP_TESTK5732','TESTPROMMG','OP_TESTTT','TESTAUTOMSP','OP_TESTUSD2','OP_TESTAPPAY','TESTTHB','TESTSOS','TESTAPPLETG','OP_TESTK5977','EKKO1','TESTONEPAY1','TESTIDTEST6','TESTONEPAYDVDT','TESTBIDVCSU2','****************','OP_VCBUSD','TESTBIDVCBSV','OP_TEST3DS','OP_TEST3DS2V','OP_CYBSUSD','TESTTHSC','TESTBNPL','TESTAOSVNC','TESTPROM','OP_SACOMECOM','TESTQRTINH','TESTTOKENDV','TESTUPOS','OP_TESTK5969','OP_TESTK5816','TESTONEPAY3','TESTPCI1','TESTKBANKCYBER','TESTVPBCYBER','TEST_OP2B','OP_TESTVP4900','TESTIDTEST3','TESTPAYPAL') )
                ORDER BY D_TRANSACTION_DATE DESC
         ) tmp
					WHERE ROWNUM <= (:p_page * :p_page_size + :p_page_size)
				) mp
				WHERE mp.rnum > (:p_page * :p_page_size)
			) t1
		
2025-08-26 04:14:51.699 [reactor-http-epoll-3] ERROR v.o.t.s.i.TransactionOldServiceImpl - Error searchGeneralReport: 
java.lang.NullPointerException: Cannot invoke "java.sql.ResultSet.next()" because "rs" is null
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.lambda$2(TransactionOldServiceImpl.java:203)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:346)
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.searchGeneralReport(TransactionOldServiceImpl.java:157)
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.lambda$6(TransactionOldServiceImpl.java:535)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondComplete(MonoFlatMap.java:245)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onNext(MonoFlatMap.java:305)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
	at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
	at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-26 04:14:51.734 [reactor-http-epoll-3] ERROR v.o.t.s.i.TransactionOldServiceImpl - Error fetchTotalRecords: 
java.lang.NullPointerException: Cannot invoke "java.sql.ResultSet.next()" because "rs" is null
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.lambda$4(TransactionOldServiceImpl.java:408)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:346)
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.fetchTotalRecords(TransactionOldServiceImpl.java:366)
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.lambda$6(TransactionOldServiceImpl.java:540)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondComplete(MonoFlatMap.java:245)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onNext(MonoFlatMap.java:305)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
	at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
	at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-26 04:14:51.753 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Response: GET http://localhost:8397/ma-service/api/v1/transactions/transaction/list?page=0&size=20&sort=&merchantId=&keyword=&fromDate=2025-08-23T17:00:00.000Z&toDate=2025-08-29T16:59:00.000Z&transactionType=&transactionStatus=&orderSource=&promotion=&paymentMethod=&searchType=all - Status: 200 OK
2025-08-26 04:23:30.848 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: GET http://localhost:8397/ma-service/api/v1/transactions/transaction/list?page=0&size=20&sort=&merchantId=&keyword=&fromDate=2025-08-23T17:00:00.000Z&toDate=2025-08-29T16:59:00.000Z&transactionType=&transactionStatus=&orderSource=&promotion=&paymentMethod=&searchType=all
2025-08-26 04:23:30.849 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-08-26 04:23:30.849 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-08-26 04:23:30.849 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-08-26 04:23:30.849 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-08-26 04:23:30.849 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-08-26 04:23:30.849 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-08-26 04:23:30.849 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/mav2-main/
2025-08-26 04:23:30.849 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-08-26 04:23:30.849 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-08-26 04:23:30.849 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-08-26 04:23:30.849 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-08-26 04:23:30.849 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-08-26 04:23:30.849 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-08-26 04:23:30.849 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-08-26 04:23:30.849 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 417604
2025-08-26 04:23:30.849 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; JSESSIONID=dummy; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQyNTMzNzEsInVzZXIiOnsiZW1haWwiOiJhZG1pbkBvbmVwYXkudm4iLCJmdWxsX25hbWUiOiJBZG1pbiAxIiwiaWQiOiI1MUREN0M5QzdEQTZBMjc4OEY0QUI1M0I1NkJFNjgxMSIsImpvYl90aXRsZSI6IkFkbWluIGFsw7QyMjIiLCJwaG9uZSI6IjA5MDEyMzQ1NjEifX0.v6Hxf8G3cQ8lR330Qq7yAeil8vSpdSiJCEPy0mz3oEg
2025-08-26 04:23:30.850 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-26 04:23:30.850 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 9b304e06-6d14-4d68-a5e6-389e104ef110
2025-08-26 04:23:30.850 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8397
2025-08-26 04:23:30.850 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-08-26 04:23:30.850 [reactor-http-epoll-4] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-08-26 04:23:30.902 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------------start call api getTransactionAll-------------------------
2025-08-26 04:23:30.902 [reactor-tcp-epoll-2] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------------end call api getTransactionAll-------------------------
2025-08-26 04:23:30.918 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Input merchantId param: 
2025-08-26 04:23:30.918 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - list merchant from permission: [OP_TESTVND, OP_VND01, OP_MPGSVND, OP_MPGSUSD, OP_MPAYVND3D, TESTBIDVV3, ONEPAYHM, D_TEST, OP_CYBSVND, TEST3DSMPGS, OP_MPAYVN1, TESTND, OP_TESTK7011, TESTHD3BEN, TESTSTATICQR, OP_TESTK4121, OP_SACOMTH, TESTAPLVNC, TESTORIFLCBS, TESTVCB_5411, OP_TESTK7399, TESTGGPAY, OP_TESTK5331, TESTKOVENA, TESTSTGINVOICE, TESTMERCHANT16KY, TESTOPVPB, TESTVTBCYBER, HUONG1, TESTOPBIDV, TESTTRAGOP, TESTPCI, TESTONEPAY, OP_TESTUSD, OP_SACOMTRVV, ONEPAY, INVOICETG, TESTDMX, TESTTNS, TESTINVOICE, TESTVCB3D2, TESTBIDV, TESTHC, TEST3DS2_LT, MIGS3DS, TESTAXA1, TESTVF, TESTUPOSTG, TESTAOSVNBANK, OP_TESTKBANK, TESTAPPLE, TESTVCBHM1, TESTTVLOKA, TESTVCB_7399, TESTVAGROUP, TESTVCB_6300, OP_TESTM8211, OP_TESTK7372, OP_TESTK7991, TESTPCI4, TESTOPVCB2, TESTSACOMCYBER, TESTUSD, OP_TESTVP5732, OP_TESTVP6300, ONEPAYMT, TESTATM, OPTEST, TESTTNSUSD, MPAYVND, TESTBIDV1, OP_VPBMPGS3, TESTAWPOSTG, TESTTRAGOP1, TESTSAMSUNG, TESTOP_VTB2, TESTUPOSKBANK, TESTAPLBANK, TESTVCB_8211, TESTMERCHANTID20KYTU, MONPAYOUT, TESTVIETQR, TESTINVOICETG, OP_TESTVP8211, TESTPAYOUT2, TEST, OP_SACOMCBSU, OP_VPBMPGS1, TESTONEPAY20, TESTVJU, OP_TESTCONFIG, OP_TESTSS, OP_TESTBHX, OPTESTHB, TESTAWPOS, TESTACVNC, OP_TESTK4900, TESTONEPAYTHB, ONEPAY_REFUND, TESTAPLTG, OP_TESTK5814, TESTVCB_4722, OP_TESTK5722, TESTPAYOUT1, TESTOPVCB, TESTIDTEST4, TESTONEPAYDVC, OP_SACOMCBSV, OP_SACOMTRVU, ****************, OPMPAY, FALSE, TESTDUONG, OP_VPBMPGS2, TESTSACOMCBU, TESTLZD, TESTSAPO, OP_TESTAUTH, TESTMEGA1, OP_TESTMID, TESTVTASABRE, OP_TESTK4722, TESTOP_VTB3, OP_SACOMVIN, OP_TESTK8220, TESTCNY, TESTVCB_4900, OP_TESTK5698, TESTPCI3, ANHTVTEST, TESTTOKEN, TESTONEPAY2, MONPAYCOLLECT, OP_TESTVP5411, TESTSHOPIFYTG, TESTOP_D, TESTONEPAYDVTT, TESTTRAGOP2, TESTBIDVU3, TESTBIDVV2, TESTTOKENUSD, TESTMEGA, TEST3DS2_MK, TEST3DS2_TN, TESTVFVND, TESTVTB3D2, TESTENETVIET, TESTSHOPIFY, TESTAPPLEND, TESTVCBHM2, TESTTRAGOP3, TESTOKENOP, TESTKONEVA, TESTOP_VTB1, AUTOAPLND, TESTSSPAY, TESTSAMSUNGPL, TESTVCB_5732, TESTAES, OP_TESTK7832, TESTPCI2, TESTJBAUSD, TESTVCBCYBER, TESTMERCHANT15K, OPPREPAID, TESTONEPAYUSD, OP_MPAYVND, TESTSACOMCBV, OP_VCBVND, TESTSAPO2, TESTBIDVCBSU, TESTVCB3D2O, TESTMAFC, OP_TESTOP20, TESTAXA, TESTTGDD3D2, TESTPR, TESTHC1, ONEPAYHB, TESTSSBNPL, OP_TESTK8299, TESTHD2BEN, TESTPCIEM, TESTMCD, OP_TESTK5732, TESTPROMMG, OP_TESTTT, TESTAUTOMSP, OP_TESTUSD2, OP_TESTAPPAY, TESTTHB, TESTSOS, TESTAPPLETG, OP_TESTK5977, EKKO1, TESTONEPAY1, TESTIDTEST6, TESTONEPAYDVDT, TESTBIDVCSU2, ****************, OP_VCBUSD, TESTBIDVCBSV, OP_TEST3DS, OP_TEST3DS2V, OP_CYBSUSD, TESTTHSC, TESTBNPL, TESTAOSVNC, TESTPROM, OP_SACOMECOM, TESTQRTINH, TESTTOKENDV, TESTUPOS, OP_TESTK5969, OP_TESTK5816, TESTONEPAY3, TESTPCI1, TESTKBANKCYBER, TESTVPBCYBER, TEST_OP2B, OP_TESTVP4900, TESTIDTEST3, TESTPAYPAL]
2025-08-26 04:23:31.146 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - Result: SUCCESS
2025-08-26 04:23:31.323 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Response: GET http://localhost:8397/ma-service/api/v1/transactions/transaction/list?page=0&size=20&sort=&merchantId=&keyword=&fromDate=2025-08-23T17:00:00.000Z&toDate=2025-08-29T16:59:00.000Z&transactionType=&transactionStatus=&orderSource=&promotion=&paymentMethod=&searchType=all - Status: 200 OK
