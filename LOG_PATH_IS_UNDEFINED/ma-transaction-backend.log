2025-08-21 10:43:35.462 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 2155493 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-08-21 10:43:35.465 [main] INFO  v.o.t.TransactionAppApplication - The following 1 profile is active: "dev"
2025-08-21 10:43:36.276 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-08-21 10:43:36.465 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 179 ms. Found 2 R2DBC repository interfaces.
2025-08-21 10:43:38.149 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Starting...
2025-08-21 10:43:38.470 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleMerchantPortalHikariPool - Added connection oracle.jdbc.driver.T4CConnection@58aa5c94
2025-08-21 10:43:38.471 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Start completed.
2025-08-21 10:43:38.545 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Starting...
2025-08-21 10:43:38.661 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleOneReportHikariPool - Added connection oracle.jdbc.driver.T4CConnection@6ec3d8e4
2025-08-21 10:43:38.661 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Start completed.
2025-08-21 10:43:38.810 [main] INFO  v.o.t.job.PromotionReportJob - Scheduling PromotionReportJob with cron: 0 05 16 * * *
2025-08-21 10:43:38.821 [main] INFO  v.o.t.job.PromotionReportJob - End PromotionReportJob!
2025-08-21 10:43:38.824 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-08-21 10:43:38.825 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-08-21 10:43:38.827 [main] INFO  v.o.transaction.job.UposReportJob - Scheduling UposReportJob with cron: 0 10 16 * * *
2025-08-21 10:43:38.831 [main] INFO  v.o.transaction.job.UposReportJob - End UposReportJob!
2025-08-21 10:43:39.740 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 8396 (http)
2025-08-21 10:43:39.923 [main] INFO  v.o.t.TransactionAppApplication - Started TransactionAppApplication in 5.142 seconds (process running for 7.106)
2025-08-21 10:44:23.081 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: GET http://localhost:8396/ma-service/api/v1/transactions/transaction/detail?transactionId=868987&transactionType=Request%20Refund&paymentMethod=Apple%20Pay
2025-08-21 10:44:23.082 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-08-21 10:44:23.082 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-08-21 10:44:23.082 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-08-21 10:44:23.082 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-08-21 10:44:23.082 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-08-21 10:44:23.082 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-08-21 10:44:23.082 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/mav2-main/
2025-08-21 10:44:23.082 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-08-21 10:44:23.082 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-08-21 10:44:23.083 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-08-21 10:44:23.083 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-08-21 10:44:23.083 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-08-21 10:44:23.083 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-08-21 10:44:23.083 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-08-21 10:44:23.083 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 417604
2025-08-21 10:44:23.083 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; JSESSIONID=dummy; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ1ODc1NjIsInVzZXIiOnsiZW1haWwiOiJhZG1pbkBvbmVwYXkudm4iLCJmdWxsX25hbWUiOiJBZG1pbiAxIiwiaWQiOiI1MUREN0M5QzdEQTZBMjc4OEY0QUI1M0I1NkJFNjgxMSIsImpvYl90aXRsZSI6IkFkbWluIGFsw7QyMjIiLCJwaG9uZSI6IjA5MDEyMzQ1NjEifX0.hEjfsrAg9gqUPMiXcjcugUJwwz7RVYeT8g3lnbyOc2Y
2025-08-21 10:44:23.083 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 10:44:23.083 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 3aed8870-ff51-403a-91dc-fd01f0c09dd2
2025-08-21 10:44:23.083 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8396
2025-08-21 10:44:23.083 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-08-21 10:44:23.087 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-08-21 10:44:23.139 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=868987 ,paymentMethod=Apple Pay ,transactionType=Request Refund ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 10:44:23.140 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-08-21 10:44:23.205 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/868987 ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 10:44:23.288 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"transaction_id":"868987","original_transaction_id":"*********","transaction_type":"Request Refund","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"17","acquirer_name":"","acquirer_short_name":""},"transaction_reference":"TESTPCI_1755770718806","amount":{"total":107.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"APP Visa 1741","card_type":"Visa","card_date":{"month":"01","year":"30"},"commercical_card":"!01","commercial_card_indicator":"0"},"merchant_id":"TESTPCI","authentication":{"authentication_type":"Verified by Visa"},"transaction_time":"2025-08-21T17:05:19Z","transaction_status":"","transaction_ref_number":"************","ip_address":"***************","ip_proxy":"","bin_country":"","csc_result_code":"","verification_security_level":"5","response_code":"","can_void":false,"risk_assesment":"Not Assessed","bank_id":"","wait_for_approval_amount":"0.00","advance_status":"Merchant Rejected","source":"Apple Pay","networkTransId":"","tokenNumber":"401200***1112","deviceId":"************","tokenExpiry":"30/01/2031","type":"CREDIT","acqResponseCode":"","status3ds":"","riskOverAllResult":"","cardLevelIndicator":"","customer_mobile":"","customer_email":"","fraud_check":"","customer_address":"","customer_name":"","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"","installment_cus_email":"","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00","installment_statement_date":"","reasonName":""}
2025-08-21 10:44:23.289 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-08-21 10:44:23.304 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-08-21 10:44:23.313 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-08-21 10:44:23.333 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-08-21T17:05:19, orderAmount=107.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=868987, parentTransactionId=*********, transactionType=Request Refund, paymentMethod=Apple Pay, transactionCreatedTime=2025-08-21T17:05:19, transactionCompletedTime=2025-08-21T17:05:19, transactionAmount=107.0, transactionCurrency=VND, transactionStatus=Merchant Rejected, responseCode=, merchantTransRef=TESTPCI_1755770718806, parentMerchantTransRef=null, paymentChannel=null, installmentStatus=, operator=null), paymentMethod=PaymentMethod(cardNumber=APP Visa 1741, cardBrand=null, cardType=Visa, issuer=, acquirer=17, cardExpiry=01/30, nameOnCard=null, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, settlementAmount=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=InstallmentDetail(priceDifference=0, period=, amountMonthly=0.00), promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=***************, ipProxy=, ipCountry=***************, binCountry=, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null, amountAdvance=null), actions=null, histories=null)
2025-08-21 10:44:23.333 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-08-21 10:44:23.334 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/refund/868987 ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 10:44:23.344 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"row_num":"0","merchant_id":"TESTPCI","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"17","acquirer_name":"Vietinbank - MPGS","acquirer_short_name":"Vietinbank - MPGS"},"original_transaction_id":"*********","transaction_id":"868987","transaction_time":"2025-08-21T17:05:19Z","transaction_type":"Request Refund","transaction_status":"404","authentication":"","amount":{"currency":"VND","total":107.0,"purchase_total":107.0},"transaction_purchase_time":"2025-08-20T16:53:19Z","card":{"card_number":"APP Visa 1741","card_type":"Visa","card_date":{"month":"01","year":"30"}},"csc_result_code":"","refund_type":"1","reasonName":""}
2025-08-21 10:44:23.345 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-08-21 10:44:23.345 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonRequestRefundIntenational...
2025-08-21 10:44:23.346 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonRequestRefundIntenational...
2025-08-21 10:44:23.346 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE REFUND INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-08-21T17:05:19, orderAmount=107.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=868987, parentTransactionId=*********, transactionType=Request Refund, paymentMethod=Apple Pay, transactionCreatedTime=2025-08-21T17:05:19, transactionCompletedTime=2025-08-21T17:05:19, transactionAmount=107.0, transactionCurrency=VND, transactionStatus=404, responseCode=null, merchantTransRef=null, parentMerchantTransRef=null, paymentChannel=null, installmentStatus=null, operator=null), paymentMethod=PaymentMethod(cardNumber=APP Visa 1741, cardBrand=null, cardType=Visa, issuer=Vietinbank - MPGS, acquirer=17, cardExpiry=01/30, nameOnCard=null, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, settlementAmount=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=null, promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=null), riskManagement=RiskManagement(ipAddress=***************, ipProxy=, ipCountry=***************, binCountry=, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null, amountAdvance=null), actions=null, histories=null)
2025-08-21 10:44:23.346 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-08-21 10:44:23.346 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getAdvanceFeeInfo ===================
2025-08-21 10:44:23.535 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ==================== Start getMerchantById ====================
2025-08-21 10:44:23.544 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getTransactionInfo ===================
2025-08-21 10:44:23.549 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getParentMerchantTransRef ===================
2025-08-21 10:44:27.577 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getPromotionInfo ===================
2025-08-21 10:44:27.584 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call getTransactionHistory-----------
2025-08-21 10:44:27.585 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: originalTransactionId=********* ,paymentMethod=Apple Pay ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 10:44:27.585 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionHistory MA-----------
2025-08-21 10:44:27.585 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/*********/history ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 10:44:27.596 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION HISTORY API: response code=200 ,response body={"transactions":[{"transaction_id":"868987","original_transaction_id":"*********","response_code":"","reference_number":"************","merchant_transaction_ref":"TESTPCI_1755770718806","transaction_type":"Request Refund","amount":{"total":107.0,"currency":"VND"},"status":"404","operator_id":"<EMAIL>","merchant_id":"TESTPCI","transaction_time":"2025-08-21T17:05:19Z","advance_status":"Merchant Rejected","financial_transaction_id":"868987","note":"","parent_id":"*********","dadId":"*********","subHistories":""},{"transaction_id":"*********","original_transaction_id":"*********","response_code":"0","reference_number":"************","merchant_transaction_ref":"TEST_1755683507","transaction_type":"Purchase","amount":{"total":107.0,"currency":"VND"},"status":"0","operator_id":"","merchant_id":"TESTPCI","transaction_time":"2025-08-20T16:53:19Z","advance_status":"Successful","financial_transaction_id":"************","note":"","parent_id":"*********","dadId":"","subHistories":""}]}
2025-08-21 10:44:27.597 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionHistory MA------------
2025-08-21 10:44:27.597 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION HISTORY INTERNATIONAL: {"transactions":[{"transaction_id":"868987","original_transaction_id":"*********","response_code":"","reference_number":"************","merchant_transaction_ref":"TESTPCI_1755770718806","transaction_type":"Request Refund","amount":{"total":107.0,"currency":"VND"},"status":"404","operator_id":"<EMAIL>","merchant_id":"TESTPCI","transaction_time":"2025-08-21T17:05:19Z","advance_status":"Merchant Rejected","financial_transaction_id":"868987","note":"","parent_id":"*********","dadId":"*********","subHistories":""},{"transaction_id":"*********","original_transaction_id":"*********","response_code":"0","reference_number":"************","merchant_transaction_ref":"TEST_1755683507","transaction_type":"Purchase","amount":{"total":107.0,"currency":"VND"},"status":"0","operator_id":"","merchant_id":"TESTPCI","transaction_time":"2025-08-20T16:53:19Z","advance_status":"Successful","financial_transaction_id":"************","note":"","parent_id":"*********","dadId":"","subHistories":""}]}
2025-08-21 10:44:27.603 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call getTransactionHistory------------
2025-08-21 10:44:27.603 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-08-21 10:44:27.603 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=Apple Pay
2025-08-21 10:44:27.848 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-08-21 10:44:27.848 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - list merchant from permission: [OP_TESTVND, OP_VND01, OP_MPGSVND, OP_MPGSUSD, OP_MPAYVND3D, TESTBIDVV3, ONEPAYHM, D_TEST, OP_CYBSVND, TEST3DSMPGS, OP_MPAYVN1, TESTND, OP_TESTK7011, TESTHD3BEN, TESTSTATICQR, OP_TESTK4121, OP_SACOMTH, TESTAPLVNC, TESTORIFLCBS, TESTVCB_5411, OP_TESTK7399, TESTGGPAY, OP_TESTK5331, TESTKOVENA, TESTSTGINVOICE, TESTMERCHANT16KY, TESTOPVPB, TESTVTBCYBER, HUONG1, TESTOPBIDV, TESTTRAGOP, TESTPCI, TESTONEPAY, OP_TESTUSD, OP_SACOMTRVV, ONEPAY, INVOICETG, TESTDMX, TESTTNS, TESTINVOICE, TESTVCB3D2, TESTBIDV, TESTHC, TEST3DS2_LT, MIGS3DS, TESTAXA1, TESTVF, TESTUPOSTG, TESTAOSVNBANK, OP_TESTKBANK, TESTAPPLE, TESTVCBHM1, TESTTVLOKA, TESTVCB_7399, TESTVAGROUP, TESTVCB_6300, OP_TESTM8211, OP_TESTK7372, OP_TESTK7991, TESTPCI4, TESTOPVCB2, TESTSACOMCYBER, TESTUSD, OP_TESTVP5732, OP_TESTVP6300, ONEPAYMT, TESTATM, OPTEST, TESTTNSUSD, MPAYVND, TESTBIDV1, OP_VPBMPGS3, TESTAWPOSTG, TESTTRAGOP1, TESTSAMSUNG, TESTOP_VTB2, TESTUPOSKBANK, TESTAPLBANK, TESTVCB_8211, TESTMERCHANTID20KYTU, MONPAYOUT, TESTVIETQR, TESTINVOICETG, OP_TESTVP8211, TESTPAYOUT2, TEST, OP_SACOMCBSU, OP_VPBMPGS1, TESTONEPAY20, TESTVJU, OP_TESTCONFIG, OP_TESTSS, OP_TESTBHX, OPTESTHB, TESTAWPOS, TESTACVNC, OP_TESTK4900, TESTONEPAYTHB, ONEPAY_REFUND, TESTAPLTG, OP_TESTK5814, TESTVCB_4722, OP_TESTK5722, TESTPAYOUT1, TESTOPVCB, TESTIDTEST4, TESTONEPAYDVC, OP_SACOMCBSV, OP_SACOMTRVU, ****************, OPMPAY, FALSE, TESTDUONG, OP_VPBMPGS2, TESTSACOMCBU, TESTLZD, TESTSAPO, OP_TESTAUTH, TESTMEGA1, OP_TESTMID, TESTVTASABRE, OP_TESTK4722, TESTOP_VTB3, OP_SACOMVIN, OP_TESTK8220, TESTCNY, TESTVCB_4900, OP_TESTK5698, TESTPCI3, ANHTVTEST, TESTTOKEN, TESTONEPAY2, MONPAYCOLLECT, OP_TESTVP5411, TESTSHOPIFYTG, TESTOP_D, TESTONEPAYDVTT, TESTTRAGOP2, TESTBIDVU3, TESTBIDVV2, TESTTOKENUSD, TESTMEGA, TEST3DS2_MK, TEST3DS2_TN, TESTVFVND, TESTVTB3D2, TESTENETVIET, TESTSHOPIFY, TESTAPPLEND, TESTVCBHM2, TESTTRAGOP3, TESTOKENOP, TESTKONEVA, TESTOP_VTB1, AUTOAPLND, TESTSSPAY, TESTSAMSUNGPL, TESTVCB_5732, TESTAES, OP_TESTK7832, TESTPCI2, TESTJBAUSD, TESTVCBCYBER, TESTMERCHANT15K, OPPREPAID, TESTONEPAYUSD, OP_MPAYVND, TESTSACOMCBV, OP_VCBVND, TESTSAPO2, TESTBIDVCBSU, TESTVCB3D2O, TESTMAFC, OP_TESTOP20, TESTAXA, TESTTGDD3D2, TESTPR, TESTHC1, ONEPAYHB, TESTSSBNPL, OP_TESTK8299, TESTHD2BEN, TESTPCIEM, TESTMCD, OP_TESTK5732, TESTPROMMG, OP_TESTTT, TESTAUTOMSP, OP_TESTUSD2, OP_TESTAPPAY, TESTTHB, TESTSOS, TESTAPPLETG, OP_TESTK5977, EKKO1, TESTONEPAY1, TESTIDTEST6, TESTONEPAYDVDT, TESTBIDVCSU2, ****************, OP_VCBUSD, TESTBIDVCBSV, OP_TEST3DS, OP_TEST3DS2V, OP_CYBSUSD, TESTTHSC, TESTBNPL, TESTAOSVNC, TESTPROM, OP_SACOMECOM, TESTQRTINH, TESTTOKENDV, TESTUPOS, OP_TESTK5969, OP_TESTK5816, TESTONEPAY3, TESTPCI1, TESTKBANKCYBER, TESTVPBCYBER, TEST_OP2B, OP_TESTVP4900, TESTIDTEST3, TESTPAYPAL]
2025-08-21 10:44:27.851 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-08-21 10:44:27.852 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-08-21 10:44:27.853 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start checkPerAndActionByUserId-----------
2025-08-21 10:44:27.900 [reactor-http-epoll-3] INFO  v.o.t.repository.AcquirerRepository - Starting getListAcquirer - calling function ONEDATA.GET_LIST_ACQUIRER
2025-08-21 10:44:27.963 [reactor-http-epoll-3] INFO  v.o.t.util.CheckPermissionUtil - checkActionVoid paymentMethod invalid, transactionId=868987, transactionType=Request Refund
2025-08-21 10:44:27.964 [reactor-http-epoll-3] INFO  v.o.t.util.CheckPermissionUtil - checkActionRefund paymentMethod invalid, transactionId=868987, transactionType=Request Refund
2025-08-21 10:44:27.964 [reactor-http-epoll-3] INFO  v.o.t.util.CheckPermissionUtil - checkActionCapture transactionType invalid, transactionId=868987, transactionType=Request Refund
2025-08-21 10:44:27.964 [reactor-http-epoll-3] INFO  v.o.t.util.CheckPermissionUtil - checkApproveAndRejectRefund transactionStatus invalid, transactionId=868987, transactionStatus=Merchant Rejected
2025-08-21 10:44:28.025 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Response: GET http://localhost:8396/ma-service/api/v1/transactions/transaction/detail?transactionId=868987&transactionType=Request%20Refund&paymentMethod=Apple%20Pay - Status: 200 OK
2025-08-21 11:05:11.280 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: GET http://localhost:8396/ma-service/api/v1/transactions/transaction/detail?transactionId=111803842&transactionType=Authorize&paymentMethod=Q
2025-08-21 11:05:11.280 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-08-21 11:05:11.281 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-08-21 11:05:11.281 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-08-21 11:05:11.281 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-08-21 11:05:11.281 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-08-21 11:05:11.281 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-08-21 11:05:11.281 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/mav2-main/
2025-08-21 11:05:11.281 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-08-21 11:05:11.281 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-08-21 11:05:11.281 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-08-21 11:05:11.281 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-08-21 11:05:11.281 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-08-21 11:05:11.281 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-08-21 11:05:11.281 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-08-21 11:05:11.281 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 417604
2025-08-21 11:05:11.281 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; JSESSIONID=dummy; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ1ODc1NjIsInVzZXIiOnsiZW1haWwiOiJhZG1pbkBvbmVwYXkudm4iLCJmdWxsX25hbWUiOiJBZG1pbiAxIiwiaWQiOiI1MUREN0M5QzdEQTZBMjc4OEY0QUI1M0I1NkJFNjgxMSIsImpvYl90aXRsZSI6IkFkbWluIGFsw7QyMjIiLCJwaG9uZSI6IjA5MDEyMzQ1NjEifX0.hEjfsrAg9gqUPMiXcjcugUJwwz7RVYeT8g3lnbyOc2Y
2025-08-21 11:05:11.282 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 11:05:11.282 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 49b2eb26-5525-4377-b454-a5b1c63342db
2025-08-21 11:05:11.282 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8396
2025-08-21 11:05:11.282 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-08-21 11:05:11.282 [reactor-http-epoll-4] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-08-21 11:05:11.285 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=111803842 ,paymentMethod=Q ,transactionType=Authorize ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 11:05:11.285 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - Dont have payment method: Q
2025-08-21 11:05:11.285 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-08-21 11:05:11.300 [reactor-http-epoll-4] ERROR v.o.t.e.GlobalExceptionHandler - Unhandled exception: 
org.springframework.web.server.ResponseStatusException: 404 NOT_FOUND "Transaction ID not found"
	at vn.onepay.transaction.controller.TransactionController.getTransactionDetail(TransactionController.java:169)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#getTransactionDetail(ServerWebExchange, String, String, String, String) [DispatcherHandler]
Original Stack Trace:
		at vn.onepay.transaction.controller.TransactionController.getTransactionDetail(TransactionController.java:169)
		at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
		at java.base/java.lang.reflect.Method.invoke(Method.java:580)
		at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
		at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
		at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onComplete(FluxDefaultIfEmpty.java:134)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onComplete(FluxSwitchIfEmpty.java:85)
		at reactor.core.publisher.MonoSupplier$MonoSupplierSubscription.request(MonoSupplier.java:148)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2241)
		at reactor.core.publisher.MonoSupplier.subscribe(MonoSupplier.java:48)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onComplete(FluxSwitchIfEmpty.java:82)
		at reactor.core.publisher.Operators.complete(Operators.java:137)
		at reactor.core.publisher.FluxFlatMap.trySubscribeScalarMap(FluxFlatMap.java:146)
		at reactor.core.publisher.MonoFlatMap.subscribeOrReturn(MonoFlatMap.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:63)
		at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onSubscribe(MonoIgnoreThen.java:135)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
		at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:241)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onComplete(MonoFlatMap.java:189)
		at reactor.core.publisher.Operators.complete(Operators.java:137)
		at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:121)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)
		at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.innerNext(FluxConcatMapNoPrefetch.java:259)
		at reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:865)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
		at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
		at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
		at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.request(MonoPeekTerminal.java:139)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.request(FluxMapFuseable.java:171)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.request(Operators.java:2331)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:339)
		at reactor.core.publisher.MonoNext$NextSubscriber.request(MonoNext.java:108)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2241)
		at reactor.core.publisher.MonoNext$NextSubscriber.onSubscribe(MonoNext.java:70)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onSubscribe(FluxConcatMapNoPrefetch.java:164)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:201)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:83)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
		at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
		at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
		at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-21 11:05:11.308 [reactor-http-epoll-4] WARN  o.s.w.r.r.m.a.RequestMappingHandlerAdapter - [74a38240-2] Failure in @ExceptionHandler vn.onepay.transaction.exception.GlobalExceptionHandler#handleNotFound(ResponseStatusException, ServerWebExchange)
org.springframework.web.server.ResponseStatusException: 404 NOT_FOUND "Not found"
	at vn.onepay.transaction.exception.GlobalExceptionHandler.handleNotFound(GlobalExceptionHandler.java:23)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
	at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.request(MonoPeekTerminal.java:139)
	at reactor.core.publisher.MonoZip$ZipInner.onSubscribe(MonoZip.java:470)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onSubscribe(MonoPeekTerminal.java:152)
	at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onSubscribe(FluxOnErrorResume.java:74)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
	at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onError(FluxOnAssembly.java:544)
	at reactor.core.publisher.Operators.error(Operators.java:198)
	at reactor.core.publisher.FluxFlatMap.trySubscribeScalarMap(FluxFlatMap.java:136)
	at reactor.core.publisher.MonoFlatMap.subscribeOrReturn(MonoFlatMap.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:63)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onNext(FluxPeek.java:200)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.complete(MonoIgnoreThen.java:294)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onNext(MonoIgnoreThen.java:188)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
	at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
	at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onComplete(FluxDefaultIfEmpty.java:134)
	at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onComplete(FluxSwitchIfEmpty.java:85)
	at reactor.core.publisher.MonoSupplier$MonoSupplierSubscription.request(MonoSupplier.java:148)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2241)
	at reactor.core.publisher.MonoSupplier.subscribe(MonoSupplier.java:48)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onComplete(FluxSwitchIfEmpty.java:82)
	at reactor.core.publisher.Operators.complete(Operators.java:137)
	at reactor.core.publisher.FluxFlatMap.trySubscribeScalarMap(FluxFlatMap.java:146)
	at reactor.core.publisher.MonoFlatMap.subscribeOrReturn(MonoFlatMap.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:63)
	at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onSubscribe(MonoIgnoreThen.java:135)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
	at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:241)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onComplete(MonoFlatMap.java:189)
	at reactor.core.publisher.Operators.complete(Operators.java:137)
	at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:121)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)
	at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.innerNext(FluxConcatMapNoPrefetch.java:259)
	at reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:865)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.request(MonoPeekTerminal.java:139)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.request(FluxMapFuseable.java:171)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.request(Operators.java:2331)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:339)
	at reactor.core.publisher.MonoNext$NextSubscriber.request(MonoNext.java:108)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2241)
	at reactor.core.publisher.MonoNext$NextSubscriber.onSubscribe(MonoNext.java:70)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onSubscribe(FluxConcatMapNoPrefetch.java:164)
	at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:201)
	at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:83)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
	at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
	at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
	at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
	at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-21 11:05:11.310 [reactor-http-epoll-4] ERROR v.o.t.exception.RequestLoggingFilter - Request error: GET http://localhost:8396/ma-service/api/v1/transactions/transaction/detail?transactionId=111803842&transactionType=Authorize&paymentMethod=Q - Exception: 404 NOT_FOUND "Transaction ID not found"
org.springframework.web.server.ResponseStatusException: 404 NOT_FOUND "Transaction ID not found"
	at vn.onepay.transaction.controller.TransactionController.getTransactionDetail(TransactionController.java:169)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#getTransactionDetail(ServerWebExchange, String, String, String, String) [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x0000748bd060cba0 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
Original Stack Trace:
		at vn.onepay.transaction.controller.TransactionController.getTransactionDetail(TransactionController.java:169)
		at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
		at java.base/java.lang.reflect.Method.invoke(Method.java:580)
		at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
		at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
		at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onComplete(FluxDefaultIfEmpty.java:134)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onComplete(FluxSwitchIfEmpty.java:85)
		at reactor.core.publisher.MonoSupplier$MonoSupplierSubscription.request(MonoSupplier.java:148)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2241)
		at reactor.core.publisher.MonoSupplier.subscribe(MonoSupplier.java:48)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onComplete(FluxSwitchIfEmpty.java:82)
		at reactor.core.publisher.Operators.complete(Operators.java:137)
		at reactor.core.publisher.FluxFlatMap.trySubscribeScalarMap(FluxFlatMap.java:146)
		at reactor.core.publisher.MonoFlatMap.subscribeOrReturn(MonoFlatMap.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:63)
		at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onSubscribe(MonoIgnoreThen.java:135)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
		at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:241)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onComplete(MonoFlatMap.java:189)
		at reactor.core.publisher.Operators.complete(Operators.java:137)
		at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:121)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)
		at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.innerNext(FluxConcatMapNoPrefetch.java:259)
		at reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:865)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
		at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
		at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
		at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.request(MonoPeekTerminal.java:139)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.request(FluxMapFuseable.java:171)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.request(Operators.java:2331)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:339)
		at reactor.core.publisher.MonoNext$NextSubscriber.request(MonoNext.java:108)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2241)
		at reactor.core.publisher.MonoNext$NextSubscriber.onSubscribe(MonoNext.java:70)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onSubscribe(FluxConcatMapNoPrefetch.java:164)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:201)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:83)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
		at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
		at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
		at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-21 11:05:30.471 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: GET http://localhost:8396/ma-service/api/v1/transactions/transaction/detail?transactionId=111803842&transactionType=Authorize&paymentMethod=QT
2025-08-21 11:05:30.471 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-08-21 11:05:30.471 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-08-21 11:05:30.472 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-08-21 11:05:30.472 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-08-21 11:05:30.472 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-08-21 11:05:30.472 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-08-21 11:05:30.472 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/mav2-main/
2025-08-21 11:05:30.472 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-08-21 11:05:30.472 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-08-21 11:05:30.472 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-08-21 11:05:30.472 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-08-21 11:05:30.472 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-08-21 11:05:30.472 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-08-21 11:05:30.472 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-08-21 11:05:30.472 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 417604
2025-08-21 11:05:30.472 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; JSESSIONID=dummy; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ1ODc1NjIsInVzZXIiOnsiZW1haWwiOiJhZG1pbkBvbmVwYXkudm4iLCJmdWxsX25hbWUiOiJBZG1pbiAxIiwiaWQiOiI1MUREN0M5QzdEQTZBMjc4OEY0QUI1M0I1NkJFNjgxMSIsImpvYl90aXRsZSI6IkFkbWluIGFsw7QyMjIiLCJwaG9uZSI6IjA5MDEyMzQ1NjEifX0.hEjfsrAg9gqUPMiXcjcugUJwwz7RVYeT8g3lnbyOc2Y
2025-08-21 11:05:30.473 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 11:05:30.473 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 3ff579a9-6774-48cd-89a9-562854ae9449
2025-08-21 11:05:30.473 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8396
2025-08-21 11:05:30.473 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-08-21 11:05:30.473 [reactor-http-epoll-4] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-08-21 11:05:30.474 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=111803842 ,paymentMethod=QT ,transactionType=Authorize ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 11:05:30.475 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-08-21 11:05:30.476 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/111803842 ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 11:05:30.501 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"transaction_id":"111803842","original_transaction_id":"111803842","transaction_type":"Authorize","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"17","acquirer_name":"","acquirer_short_name":""},"transaction_reference":"TEST_1755506112099348164310","amount":{"total":12000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"340353****0900","card_type":"Amex","name_on_card":"NGUYEN VAN A","card_date":{"month":"12","year":"28"},"commercical_card":"888","commercial_card_indicator":"3"},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y","authentication_type":"Amex Safekey","authorization_code":"013834"},"transaction_time":"2025-08-18T15:35:24Z","transaction_status":"0","transaction_ref_number":"*********","ip_address":"************","ip_proxy":"N","bin_country":"UNITED STATES","csc_result_code":"","verification_security_level":"","response_code":"0","can_void":true,"risk_assesment":"Review Required","bank_id":"AMERICAN EXPRESS US CONSUMER","wait_for_approval_amount":"0.00","advance_status":"Successful","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"CREDIT","acqResponseCode":"","status3ds":"Y","riskOverAllResult":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"NGUYEN VAN A","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00","installment_statement_date":"","reasonName":""}
2025-08-21 11:05:30.501 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-08-21 11:05:30.502 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-08-21 11:05:30.503 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-08-21 11:05:30.504 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-08-18T15:35:24, orderAmount=12000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=111803842, parentTransactionId=111803842, transactionType=Authorize, paymentMethod=QT, transactionCreatedTime=2025-08-18T15:35:24, transactionCompletedTime=2025-08-18T15:35:24, transactionAmount=12000.0, transactionCurrency=VND, transactionStatus=Successful, responseCode=0, merchantTransRef=TEST_1755506112099348164310, parentMerchantTransRef=null, paymentChannel=null, installmentStatus=, operator=null), paymentMethod=PaymentMethod(cardNumber=340353****0900, cardBrand=null, cardType=Amex, issuer=, acquirer=17, cardExpiry=12/28, nameOnCard=NGUYEN VAN A, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, settlementAmount=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=InstallmentDetail(priceDifference=0, period=, amountMonthly=0.00), promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=************, ipProxy=N, ipCountry=************, binCountry=UNITED STATES, riskAssessment=Review Required), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null, amountAdvance=null), actions=null, histories=null)
2025-08-21 11:05:30.504 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-08-21 11:05:30.504 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getAdvanceFeeInfo ===================
2025-08-21 11:05:30.511 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ==================== Start getMerchantById ====================
2025-08-21 11:05:30.515 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getParentMerchantTransRef ===================
2025-08-21 11:05:30.518 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getPromotionInfo ===================
2025-08-21 11:05:30.521 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call getTransactionHistory-----------
2025-08-21 11:05:30.522 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: originalTransactionId=111803842 ,paymentMethod=QT ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 11:05:30.522 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionHistory MA-----------
2025-08-21 11:05:30.522 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/111803842/history ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 11:05:30.531 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION HISTORY API: response code=200 ,response body={"transactions":[{"transaction_id":"111803842","original_transaction_id":"111803842","response_code":"0","reference_number":"*********","merchant_transaction_ref":"TEST_1755506112099348164310","transaction_type":"Authorize","amount":{"total":12000.0,"currency":"VND"},"status":"0","operator_id":"","merchant_id":"TESTPCI","transaction_time":"2025-08-18T15:35:24Z","advance_status":"Successful","financial_transaction_id":"*********","note":"","parent_id":"111803842","dadId":"","subHistories":""}]}
2025-08-21 11:05:30.531 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionHistory MA------------
2025-08-21 11:05:30.531 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION HISTORY INTERNATIONAL: {"transactions":[{"transaction_id":"111803842","original_transaction_id":"111803842","response_code":"0","reference_number":"*********","merchant_transaction_ref":"TEST_1755506112099348164310","transaction_type":"Authorize","amount":{"total":12000.0,"currency":"VND"},"status":"0","operator_id":"","merchant_id":"TESTPCI","transaction_time":"2025-08-18T15:35:24Z","advance_status":"Successful","financial_transaction_id":"*********","note":"","parent_id":"111803842","dadId":"","subHistories":""}]}
2025-08-21 11:05:30.532 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call getTransactionHistory------------
2025-08-21 11:05:30.532 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-08-21 11:05:30.532 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-08-21 11:05:30.556 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-08-21 11:05:30.556 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - list merchant from permission: [OP_TESTVND, OP_VND01, OP_MPGSVND, OP_MPGSUSD, OP_MPAYVND3D, TESTBIDVV3, ONEPAYHM, D_TEST, OP_CYBSVND, TEST3DSMPGS, OP_MPAYVN1, TESTND, OP_TESTK7011, TESTHD3BEN, TESTSTATICQR, OP_TESTK4121, OP_SACOMTH, TESTAPLVNC, TESTORIFLCBS, TESTVCB_5411, OP_TESTK7399, TESTGGPAY, OP_TESTK5331, TESTKOVENA, TESTSTGINVOICE, TESTMERCHANT16KY, TESTOPVPB, TESTVTBCYBER, HUONG1, TESTOPBIDV, TESTTRAGOP, TESTPCI, TESTONEPAY, OP_TESTUSD, OP_SACOMTRVV, ONEPAY, INVOICETG, TESTDMX, TESTTNS, TESTINVOICE, TESTVCB3D2, TESTBIDV, TESTHC, TEST3DS2_LT, MIGS3DS, TESTAXA1, TESTVF, TESTUPOSTG, TESTAOSVNBANK, OP_TESTKBANK, TESTAPPLE, TESTVCBHM1, TESTTVLOKA, TESTVCB_7399, TESTVAGROUP, TESTVCB_6300, OP_TESTM8211, OP_TESTK7372, OP_TESTK7991, TESTPCI4, TESTOPVCB2, TESTSACOMCYBER, TESTUSD, OP_TESTVP5732, OP_TESTVP6300, ONEPAYMT, TESTATM, OPTEST, TESTTNSUSD, MPAYVND, TESTBIDV1, OP_VPBMPGS3, TESTAWPOSTG, TESTTRAGOP1, TESTSAMSUNG, TESTOP_VTB2, TESTUPOSKBANK, TESTAPLBANK, TESTVCB_8211, TESTMERCHANTID20KYTU, MONPAYOUT, TESTVIETQR, TESTINVOICETG, OP_TESTVP8211, TESTPAYOUT2, TEST, OP_SACOMCBSU, OP_VPBMPGS1, TESTONEPAY20, TESTVJU, OP_TESTCONFIG, OP_TESTSS, OP_TESTBHX, OPTESTHB, TESTAWPOS, TESTACVNC, OP_TESTK4900, TESTONEPAYTHB, ONEPAY_REFUND, TESTAPLTG, OP_TESTK5814, TESTVCB_4722, OP_TESTK5722, TESTPAYOUT1, TESTOPVCB, TESTIDTEST4, TESTONEPAYDVC, OP_SACOMCBSV, OP_SACOMTRVU, ****************, OPMPAY, FALSE, TESTDUONG, OP_VPBMPGS2, TESTSACOMCBU, TESTLZD, TESTSAPO, OP_TESTAUTH, TESTMEGA1, OP_TESTMID, TESTVTASABRE, OP_TESTK4722, TESTOP_VTB3, OP_SACOMVIN, OP_TESTK8220, TESTCNY, TESTVCB_4900, OP_TESTK5698, TESTPCI3, ANHTVTEST, TESTTOKEN, TESTONEPAY2, MONPAYCOLLECT, OP_TESTVP5411, TESTSHOPIFYTG, TESTOP_D, TESTONEPAYDVTT, TESTTRAGOP2, TESTBIDVU3, TESTBIDVV2, TESTTOKENUSD, TESTMEGA, TEST3DS2_MK, TEST3DS2_TN, TESTVFVND, TESTVTB3D2, TESTENETVIET, TESTSHOPIFY, TESTAPPLEND, TESTVCBHM2, TESTTRAGOP3, TESTOKENOP, TESTKONEVA, TESTOP_VTB1, AUTOAPLND, TESTSSPAY, TESTSAMSUNGPL, TESTVCB_5732, TESTAES, OP_TESTK7832, TESTPCI2, TESTJBAUSD, TESTVCBCYBER, TESTMERCHANT15K, OPPREPAID, TESTONEPAYUSD, OP_MPAYVND, TESTSACOMCBV, OP_VCBVND, TESTSAPO2, TESTBIDVCBSU, TESTVCB3D2O, TESTMAFC, OP_TESTOP20, TESTAXA, TESTTGDD3D2, TESTPR, TESTHC1, ONEPAYHB, TESTSSBNPL, OP_TESTK8299, TESTHD2BEN, TESTPCIEM, TESTMCD, OP_TESTK5732, TESTPROMMG, OP_TESTTT, TESTAUTOMSP, OP_TESTUSD2, OP_TESTAPPAY, TESTTHB, TESTSOS, TESTAPPLETG, OP_TESTK5977, EKKO1, TESTONEPAY1, TESTIDTEST6, TESTONEPAYDVDT, TESTBIDVCSU2, ****************, OP_VCBUSD, TESTBIDVCBSV, OP_TEST3DS, OP_TEST3DS2V, OP_CYBSUSD, TESTTHSC, TESTBNPL, TESTAOSVNC, TESTPROM, OP_SACOMECOM, TESTQRTINH, TESTTOKENDV, TESTUPOS, OP_TESTK5969, OP_TESTK5816, TESTONEPAY3, TESTPCI1, TESTKBANKCYBER, TESTVPBCYBER, TEST_OP2B, OP_TESTVP4900, TESTIDTEST3, TESTPAYPAL]
2025-08-21 11:05:30.556 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-08-21 11:05:30.557 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-08-21 11:05:30.557 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start checkPerAndActionByUserId-----------
2025-08-21 11:05:52.692 [reactor-http-epoll-4] ERROR v.o.t.util.CheckPermissionUtil - Error parsing date: 2025-08-18T15:35:24
java.time.temporal.UnsupportedTemporalTypeException: Unsupported field: HourOfDay
	at java.base/java.time.LocalDate.get0(LocalDate.java:698)
	at java.base/java.time.LocalDate.getLong(LocalDate.java:678)
	at java.base/java.time.format.DateTimePrintContext.getValue(DateTimePrintContext.java:308)
	at java.base/java.time.format.DateTimeFormatterBuilder$NumberPrinterParser.format(DateTimeFormatterBuilder.java:2914)
	at java.base/java.time.format.DateTimeFormatterBuilder$CompositePrinterParser.format(DateTimeFormatterBuilder.java:2529)
	at java.base/java.time.format.DateTimeFormatter.formatTo(DateTimeFormatter.java:1905)
	at java.base/java.time.format.DateTimeFormatter.format(DateTimeFormatter.java:1879)
	at java.base/java.time.LocalDate.format(LocalDate.java:1797)
	at vn.onepay.transaction.util.CheckPermissionUtil.checkDeadline(CheckPermissionUtil.java:950)
	at vn.onepay.transaction.util.CheckPermissionUtil.checkVoidAuthorizeQT(CheckPermissionUtil.java:540)
	at vn.onepay.transaction.util.CheckPermissionUtil.checkActionVoidAuthorize(CheckPermissionUtil.java:511)
	at vn.onepay.transaction.util.CheckPermissionUtil.checkPerActionVoid(CheckPermissionUtil.java:141)
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.lambda$35(TransactionOldServiceImpl.java:3368)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondComplete(MonoFlatMap.java:245)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onNext(MonoFlatMap.java:305)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
	at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
	at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-21 11:05:52.693 [reactor-http-epoll-4] INFO  v.o.t.util.CheckPermissionUtil - checkActionRefund paymentMethod invalid, transactionId=111803842, transactionType=Authorize
2025-08-21 11:07:18.617 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 2162757 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-08-21 11:07:18.619 [main] INFO  v.o.t.TransactionAppApplication - The following 1 profile is active: "dev"
2025-08-21 11:07:19.426 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-08-21 11:07:19.609 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 174 ms. Found 2 R2DBC repository interfaces.
2025-08-21 11:07:21.179 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Starting...
2025-08-21 11:07:21.476 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleMerchantPortalHikariPool - Added connection oracle.jdbc.driver.T4CConnection@58aa5c94
2025-08-21 11:07:21.478 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Start completed.
2025-08-21 11:07:21.561 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Starting...
2025-08-21 11:07:21.667 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleOneReportHikariPool - Added connection oracle.jdbc.driver.T4CConnection@6ec3d8e4
2025-08-21 11:07:21.667 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Start completed.
2025-08-21 11:07:21.787 [main] INFO  v.o.t.job.PromotionReportJob - Scheduling PromotionReportJob with cron: 0 05 16 * * *
2025-08-21 11:07:21.800 [main] INFO  v.o.t.job.PromotionReportJob - End PromotionReportJob!
2025-08-21 11:07:21.806 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-08-21 11:07:21.807 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-08-21 11:07:21.812 [main] INFO  v.o.transaction.job.UposReportJob - Scheduling UposReportJob with cron: 0 10 16 * * *
2025-08-21 11:07:21.813 [main] INFO  v.o.transaction.job.UposReportJob - End UposReportJob!
2025-08-21 11:07:22.706 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 8396 (http)
2025-08-21 11:07:22.903 [main] INFO  v.o.t.TransactionAppApplication - Started TransactionAppApplication in 4.988 seconds (process running for 6.7)
2025-08-21 11:08:29.415 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: GET http://localhost:8396/ma-service/api/v1/transactions/transaction/detail?transactionId=111803842&transactionType=Authorize&paymentMethod=QT
2025-08-21 11:08:29.415 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-08-21 11:08:29.416 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-08-21 11:08:29.416 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-08-21 11:08:29.416 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-08-21 11:08:29.416 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-08-21 11:08:29.416 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-08-21 11:08:29.416 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/mav2-main/
2025-08-21 11:08:29.416 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-08-21 11:08:29.416 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-08-21 11:08:29.416 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-08-21 11:08:29.416 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-08-21 11:08:29.416 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-08-21 11:08:29.416 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-08-21 11:08:29.417 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-08-21 11:08:29.417 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 417604
2025-08-21 11:08:29.417 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; JSESSIONID=dummy; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ1ODc1NjIsInVzZXIiOnsiZW1haWwiOiJhZG1pbkBvbmVwYXkudm4iLCJmdWxsX25hbWUiOiJBZG1pbiAxIiwiaWQiOiI1MUREN0M5QzdEQTZBMjc4OEY0QUI1M0I1NkJFNjgxMSIsImpvYl90aXRsZSI6IkFkbWluIGFsw7QyMjIiLCJwaG9uZSI6IjA5MDEyMzQ1NjEifX0.hEjfsrAg9gqUPMiXcjcugUJwwz7RVYeT8g3lnbyOc2Y
2025-08-21 11:08:29.417 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 11:08:29.417 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = bcc7ce7b-c6d3-4205-8b08-b2172636bde2
2025-08-21 11:08:29.417 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8396
2025-08-21 11:08:29.417 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-08-21 11:08:29.421 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-08-21 11:08:29.471 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=111803842 ,paymentMethod=QT ,transactionType=Authorize ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 11:08:29.472 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-08-21 11:08:29.552 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/111803842 ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 11:08:29.638 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"transaction_id":"111803842","original_transaction_id":"111803842","transaction_type":"Authorize","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"17","acquirer_name":"","acquirer_short_name":""},"transaction_reference":"TEST_1755506112099348164310","amount":{"total":12000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"340353****0900","card_type":"Amex","name_on_card":"NGUYEN VAN A","card_date":{"month":"12","year":"28"},"commercical_card":"888","commercial_card_indicator":"3"},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y","authentication_type":"Amex Safekey","authorization_code":"013834"},"transaction_time":"2025-08-18T15:35:24Z","transaction_status":"0","transaction_ref_number":"*********","ip_address":"************","ip_proxy":"N","bin_country":"UNITED STATES","csc_result_code":"","verification_security_level":"","response_code":"0","can_void":true,"risk_assesment":"Review Required","bank_id":"AMERICAN EXPRESS US CONSUMER","wait_for_approval_amount":"0.00","advance_status":"Successful","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"CREDIT","acqResponseCode":"","status3ds":"Y","riskOverAllResult":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"NGUYEN VAN A","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00","installment_statement_date":"","reasonName":""}
2025-08-21 11:08:29.638 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-08-21 11:08:29.654 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-08-21 11:08:29.664 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-08-21 11:08:29.684 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-08-18T15:35:24, orderAmount=12000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=111803842, parentTransactionId=111803842, transactionType=Authorize, paymentMethod=QT, transactionCreatedTime=2025-08-18T15:35:24, transactionCompletedTime=2025-08-18T15:35:24, transactionAmount=12000.0, transactionCurrency=VND, transactionStatus=Successful, responseCode=0, merchantTransRef=TEST_1755506112099348164310, parentMerchantTransRef=null, paymentChannel=null, installmentStatus=, operator=null), paymentMethod=PaymentMethod(cardNumber=340353****0900, cardBrand=null, cardType=Amex, issuer=, acquirer=17, cardExpiry=12/28, nameOnCard=NGUYEN VAN A, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, settlementAmount=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=InstallmentDetail(priceDifference=0, period=, amountMonthly=0.00), promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=************, ipProxy=N, ipCountry=************, binCountry=UNITED STATES, riskAssessment=Review Required), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null, amountAdvance=null), actions=null, histories=null)
2025-08-21 11:08:29.684 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-08-21 11:08:29.684 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getAdvanceFeeInfo ===================
2025-08-21 11:08:29.876 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ==================== Start getMerchantById ====================
2025-08-21 11:08:29.886 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getParentMerchantTransRef ===================
2025-08-21 11:08:29.909 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getPromotionInfo ===================
2025-08-21 11:08:29.914 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call getTransactionHistory-----------
2025-08-21 11:08:29.915 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: originalTransactionId=111803842 ,paymentMethod=QT ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 11:08:29.915 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionHistory MA-----------
2025-08-21 11:08:29.916 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/111803842/history ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 11:08:29.925 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION HISTORY API: response code=200 ,response body={"transactions":[{"transaction_id":"111803842","original_transaction_id":"111803842","response_code":"0","reference_number":"*********","merchant_transaction_ref":"TEST_1755506112099348164310","transaction_type":"Authorize","amount":{"total":12000.0,"currency":"VND"},"status":"0","operator_id":"","merchant_id":"TESTPCI","transaction_time":"2025-08-18T15:35:24Z","advance_status":"Successful","financial_transaction_id":"*********","note":"","parent_id":"111803842","dadId":"","subHistories":""}]}
2025-08-21 11:08:29.925 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionHistory MA------------
2025-08-21 11:08:29.925 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION HISTORY INTERNATIONAL: {"transactions":[{"transaction_id":"111803842","original_transaction_id":"111803842","response_code":"0","reference_number":"*********","merchant_transaction_ref":"TEST_1755506112099348164310","transaction_type":"Authorize","amount":{"total":12000.0,"currency":"VND"},"status":"0","operator_id":"","merchant_id":"TESTPCI","transaction_time":"2025-08-18T15:35:24Z","advance_status":"Successful","financial_transaction_id":"*********","note":"","parent_id":"111803842","dadId":"","subHistories":""}]}
2025-08-21 11:08:29.929 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call getTransactionHistory------------
2025-08-21 11:08:29.929 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-08-21 11:08:29.930 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-08-21 11:08:30.199 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-08-21 11:08:30.200 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - list merchant from permission: [OP_TESTVND, OP_VND01, OP_MPGSVND, OP_MPGSUSD, OP_MPAYVND3D, TESTBIDVV3, ONEPAYHM, D_TEST, OP_CYBSVND, TEST3DSMPGS, OP_MPAYVN1, TESTND, OP_TESTK7011, TESTHD3BEN, TESTSTATICQR, OP_TESTK4121, OP_SACOMTH, TESTAPLVNC, TESTORIFLCBS, TESTVCB_5411, OP_TESTK7399, TESTGGPAY, OP_TESTK5331, TESTKOVENA, TESTSTGINVOICE, TESTMERCHANT16KY, TESTOPVPB, TESTVTBCYBER, HUONG1, TESTOPBIDV, TESTTRAGOP, TESTPCI, TESTONEPAY, OP_TESTUSD, OP_SACOMTRVV, ONEPAY, INVOICETG, TESTDMX, TESTTNS, TESTINVOICE, TESTVCB3D2, TESTBIDV, TESTHC, TEST3DS2_LT, MIGS3DS, TESTAXA1, TESTVF, TESTUPOSTG, TESTAOSVNBANK, OP_TESTKBANK, TESTAPPLE, TESTVCBHM1, TESTTVLOKA, TESTVCB_7399, TESTVAGROUP, TESTVCB_6300, OP_TESTM8211, OP_TESTK7372, OP_TESTK7991, TESTPCI4, TESTOPVCB2, TESTSACOMCYBER, TESTUSD, OP_TESTVP5732, OP_TESTVP6300, ONEPAYMT, TESTATM, OPTEST, TESTTNSUSD, MPAYVND, TESTBIDV1, OP_VPBMPGS3, TESTAWPOSTG, TESTTRAGOP1, TESTSAMSUNG, TESTOP_VTB2, TESTUPOSKBANK, TESTAPLBANK, TESTVCB_8211, TESTMERCHANTID20KYTU, MONPAYOUT, TESTVIETQR, TESTINVOICETG, OP_TESTVP8211, TESTPAYOUT2, TEST, OP_SACOMCBSU, OP_VPBMPGS1, TESTONEPAY20, TESTVJU, OP_TESTCONFIG, OP_TESTSS, OP_TESTBHX, OPTESTHB, TESTAWPOS, TESTACVNC, OP_TESTK4900, TESTONEPAYTHB, ONEPAY_REFUND, TESTAPLTG, OP_TESTK5814, TESTVCB_4722, OP_TESTK5722, TESTPAYOUT1, TESTOPVCB, TESTIDTEST4, TESTONEPAYDVC, OP_SACOMCBSV, OP_SACOMTRVU, ****************, OPMPAY, FALSE, TESTDUONG, OP_VPBMPGS2, TESTSACOMCBU, TESTLZD, TESTSAPO, OP_TESTAUTH, TESTMEGA1, OP_TESTMID, TESTVTASABRE, OP_TESTK4722, TESTOP_VTB3, OP_SACOMVIN, OP_TESTK8220, TESTCNY, TESTVCB_4900, OP_TESTK5698, TESTPCI3, ANHTVTEST, TESTTOKEN, TESTONEPAY2, MONPAYCOLLECT, OP_TESTVP5411, TESTSHOPIFYTG, TESTOP_D, TESTONEPAYDVTT, TESTTRAGOP2, TESTBIDVU3, TESTBIDVV2, TESTTOKENUSD, TESTMEGA, TEST3DS2_MK, TEST3DS2_TN, TESTVFVND, TESTVTB3D2, TESTENETVIET, TESTSHOPIFY, TESTAPPLEND, TESTVCBHM2, TESTTRAGOP3, TESTOKENOP, TESTKONEVA, TESTOP_VTB1, AUTOAPLND, TESTSSPAY, TESTSAMSUNGPL, TESTVCB_5732, TESTAES, OP_TESTK7832, TESTPCI2, TESTJBAUSD, TESTVCBCYBER, TESTMERCHANT15K, OPPREPAID, TESTONEPAYUSD, OP_MPAYVND, TESTSACOMCBV, OP_VCBVND, TESTSAPO2, TESTBIDVCBSU, TESTVCB3D2O, TESTMAFC, OP_TESTOP20, TESTAXA, TESTTGDD3D2, TESTPR, TESTHC1, ONEPAYHB, TESTSSBNPL, OP_TESTK8299, TESTHD2BEN, TESTPCIEM, TESTMCD, OP_TESTK5732, TESTPROMMG, OP_TESTTT, TESTAUTOMSP, OP_TESTUSD2, OP_TESTAPPAY, TESTTHB, TESTSOS, TESTAPPLETG, OP_TESTK5977, EKKO1, TESTONEPAY1, TESTIDTEST6, TESTONEPAYDVDT, TESTBIDVCSU2, ****************, OP_VCBUSD, TESTBIDVCBSV, OP_TEST3DS, OP_TEST3DS2V, OP_CYBSUSD, TESTTHSC, TESTBNPL, TESTAOSVNC, TESTPROM, OP_SACOMECOM, TESTQRTINH, TESTTOKENDV, TESTUPOS, OP_TESTK5969, OP_TESTK5816, TESTONEPAY3, TESTPCI1, TESTKBANKCYBER, TESTVPBCYBER, TEST_OP2B, OP_TESTVP4900, TESTIDTEST3, TESTPAYPAL]
2025-08-21 11:08:30.202 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-08-21 11:08:30.203 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-08-21 11:08:30.204 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start checkPerAndActionByUserId-----------
2025-08-21 11:08:30.247 [reactor-http-epoll-3] INFO  v.o.t.repository.AcquirerRepository - Starting getListAcquirer - calling function ONEDATA.GET_LIST_ACQUIRER
2025-08-21 11:09:07.150 [reactor-http-epoll-3] ERROR v.o.t.util.CheckPermissionUtil - Error parsing date: 2025-08-18T15:35:24
java.time.temporal.UnsupportedTemporalTypeException: Unsupported field: HourOfDay
	at java.base/java.time.LocalDate.get0(LocalDate.java:698)
	at java.base/java.time.LocalDate.getLong(LocalDate.java:678)
	at java.base/java.time.format.DateTimePrintContext.getValue(DateTimePrintContext.java:308)
	at java.base/java.time.format.DateTimeFormatterBuilder$NumberPrinterParser.format(DateTimeFormatterBuilder.java:2914)
	at java.base/java.time.format.DateTimeFormatterBuilder$CompositePrinterParser.format(DateTimeFormatterBuilder.java:2529)
	at java.base/java.time.format.DateTimeFormatter.formatTo(DateTimeFormatter.java:1905)
	at java.base/java.time.format.DateTimeFormatter.format(DateTimeFormatter.java:1879)
	at java.base/java.time.LocalDate.format(LocalDate.java:1797)
	at vn.onepay.transaction.util.CheckPermissionUtil.checkDeadline(CheckPermissionUtil.java:950)
	at vn.onepay.transaction.util.CheckPermissionUtil.checkVoidAuthorizeQT(CheckPermissionUtil.java:540)
	at vn.onepay.transaction.util.CheckPermissionUtil.checkActionVoidAuthorize(CheckPermissionUtil.java:511)
	at vn.onepay.transaction.util.CheckPermissionUtil.checkPerActionVoid(CheckPermissionUtil.java:141)
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.lambda$35(TransactionOldServiceImpl.java:3368)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondComplete(MonoFlatMap.java:245)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onNext(MonoFlatMap.java:305)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
	at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
	at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-21 11:09:07.153 [reactor-http-epoll-3] INFO  v.o.t.util.CheckPermissionUtil - checkActionRefund paymentMethod invalid, transactionId=111803842, transactionType=Authorize
2025-08-21 11:11:10.711 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 2164213 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-08-21 11:11:10.714 [main] INFO  v.o.t.TransactionAppApplication - The following 1 profile is active: "dev"
2025-08-21 11:11:11.454 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-08-21 11:11:11.624 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 163 ms. Found 2 R2DBC repository interfaces.
2025-08-21 11:11:13.190 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Starting...
2025-08-21 11:11:13.482 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleMerchantPortalHikariPool - Added connection oracle.jdbc.driver.T4CConnection@58aa5c94
2025-08-21 11:11:13.484 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Start completed.
2025-08-21 11:11:13.553 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Starting...
2025-08-21 11:11:13.667 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleOneReportHikariPool - Added connection oracle.jdbc.driver.T4CConnection@6ec3d8e4
2025-08-21 11:11:13.667 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Start completed.
2025-08-21 11:11:13.761 [main] INFO  v.o.t.job.PromotionReportJob - Scheduling PromotionReportJob with cron: 0 05 16 * * *
2025-08-21 11:11:13.768 [main] INFO  v.o.t.job.PromotionReportJob - End PromotionReportJob!
2025-08-21 11:11:13.770 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-08-21 11:11:13.771 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-08-21 11:11:13.774 [main] INFO  v.o.transaction.job.UposReportJob - Scheduling UposReportJob with cron: 0 10 16 * * *
2025-08-21 11:11:13.775 [main] INFO  v.o.transaction.job.UposReportJob - End UposReportJob!
2025-08-21 11:11:14.619 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 8396 (http)
2025-08-21 11:11:14.810 [main] INFO  v.o.t.TransactionAppApplication - Started TransactionAppApplication in 4.746 seconds (process running for 6.734)
2025-08-21 11:11:19.062 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: GET http://localhost:8396/ma-service/api/v1/transactions/transaction/detail?transactionId=111803842&transactionType=Authorize&paymentMethod=QT
2025-08-21 11:11:19.063 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-08-21 11:11:19.063 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-08-21 11:11:19.063 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-08-21 11:11:19.063 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-08-21 11:11:19.063 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-08-21 11:11:19.063 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-08-21 11:11:19.063 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/mav2-main/
2025-08-21 11:11:19.064 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-08-21 11:11:19.064 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-08-21 11:11:19.064 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-08-21 11:11:19.064 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-08-21 11:11:19.064 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-08-21 11:11:19.064 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-08-21 11:11:19.064 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-08-21 11:11:19.064 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 417604
2025-08-21 11:11:19.064 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; JSESSIONID=dummy; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ1ODc1NjIsInVzZXIiOnsiZW1haWwiOiJhZG1pbkBvbmVwYXkudm4iLCJmdWxsX25hbWUiOiJBZG1pbiAxIiwiaWQiOiI1MUREN0M5QzdEQTZBMjc4OEY0QUI1M0I1NkJFNjgxMSIsImpvYl90aXRsZSI6IkFkbWluIGFsw7QyMjIiLCJwaG9uZSI6IjA5MDEyMzQ1NjEifX0.hEjfsrAg9gqUPMiXcjcugUJwwz7RVYeT8g3lnbyOc2Y
2025-08-21 11:11:19.064 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 11:11:19.064 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = c87b6466-c456-416d-acc3-34b1d38b1ab0
2025-08-21 11:11:19.064 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8396
2025-08-21 11:11:19.064 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-08-21 11:11:19.068 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-08-21 11:11:19.114 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=111803842 ,paymentMethod=QT ,transactionType=Authorize ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 11:11:19.115 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-08-21 11:11:19.177 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/111803842 ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 11:11:19.281 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"transaction_id":"111803842","original_transaction_id":"111803842","transaction_type":"Authorize","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"17","acquirer_name":"","acquirer_short_name":""},"transaction_reference":"TEST_1755506112099348164310","amount":{"total":12000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"340353****0900","card_type":"Amex","name_on_card":"NGUYEN VAN A","card_date":{"month":"12","year":"28"},"commercical_card":"888","commercial_card_indicator":"3"},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y","authentication_type":"Amex Safekey","authorization_code":"013834"},"transaction_time":"2025-08-18T15:35:24Z","transaction_status":"0","transaction_ref_number":"*********","ip_address":"************","ip_proxy":"N","bin_country":"UNITED STATES","csc_result_code":"","verification_security_level":"","response_code":"0","can_void":true,"risk_assesment":"Review Required","bank_id":"AMERICAN EXPRESS US CONSUMER","wait_for_approval_amount":"0.00","advance_status":"Successful","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"CREDIT","acqResponseCode":"","status3ds":"Y","riskOverAllResult":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"NGUYEN VAN A","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00","installment_statement_date":"","reasonName":""}
2025-08-21 11:11:19.281 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-08-21 11:11:19.298 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-08-21 11:11:19.307 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-08-21 11:11:19.330 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-08-18T15:35:24, orderAmount=12000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=111803842, parentTransactionId=111803842, transactionType=Authorize, paymentMethod=QT, transactionCreatedTime=2025-08-18T15:35:24, transactionCompletedTime=2025-08-18T15:35:24, transactionAmount=12000.0, transactionCurrency=VND, transactionStatus=Successful, responseCode=0, merchantTransRef=TEST_1755506112099348164310, parentMerchantTransRef=null, paymentChannel=null, installmentStatus=, operator=null), paymentMethod=PaymentMethod(cardNumber=340353****0900, cardBrand=null, cardType=Amex, issuer=, acquirer=17, cardExpiry=12/28, nameOnCard=NGUYEN VAN A, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, settlementAmount=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=InstallmentDetail(priceDifference=0, period=, amountMonthly=0.00), promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=************, ipProxy=N, ipCountry=************, binCountry=UNITED STATES, riskAssessment=Review Required), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null, amountAdvance=null), actions=null, histories=null)
2025-08-21 11:11:19.330 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-08-21 11:11:19.330 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getAdvanceFeeInfo ===================
2025-08-21 11:11:19.612 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ==================== Start getMerchantById ====================
2025-08-21 11:11:19.625 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getParentMerchantTransRef ===================
2025-08-21 11:11:19.657 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getPromotionInfo ===================
2025-08-21 11:11:19.663 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call getTransactionHistory-----------
2025-08-21 11:11:19.664 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: originalTransactionId=111803842 ,paymentMethod=QT ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 11:11:19.664 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionHistory MA-----------
2025-08-21 11:11:19.665 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/111803842/history ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 11:11:19.675 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION HISTORY API: response code=200 ,response body={"transactions":[{"transaction_id":"111803842","original_transaction_id":"111803842","response_code":"0","reference_number":"*********","merchant_transaction_ref":"TEST_1755506112099348164310","transaction_type":"Authorize","amount":{"total":12000.0,"currency":"VND"},"status":"0","operator_id":"","merchant_id":"TESTPCI","transaction_time":"2025-08-18T15:35:24Z","advance_status":"Successful","financial_transaction_id":"*********","note":"","parent_id":"111803842","dadId":"","subHistories":""}]}
2025-08-21 11:11:19.675 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionHistory MA------------
2025-08-21 11:11:19.675 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION HISTORY INTERNATIONAL: {"transactions":[{"transaction_id":"111803842","original_transaction_id":"111803842","response_code":"0","reference_number":"*********","merchant_transaction_ref":"TEST_1755506112099348164310","transaction_type":"Authorize","amount":{"total":12000.0,"currency":"VND"},"status":"0","operator_id":"","merchant_id":"TESTPCI","transaction_time":"2025-08-18T15:35:24Z","advance_status":"Successful","financial_transaction_id":"*********","note":"","parent_id":"111803842","dadId":"","subHistories":""}]}
2025-08-21 11:11:19.681 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call getTransactionHistory------------
2025-08-21 11:11:19.681 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-08-21 11:11:19.682 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
