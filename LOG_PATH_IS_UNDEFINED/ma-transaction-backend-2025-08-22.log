2025-08-22 03:24:38.526 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 2335532 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-08-22 03:24:38.531 [main] INFO  v.o.t.TransactionAppApplication - The following 1 profile is active: "dev"
2025-08-22 03:24:39.413 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-08-22 03:24:39.646 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 226 ms. Found 2 R2DBC repository interfaces.
2025-08-22 03:24:41.464 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Starting...
2025-08-22 03:24:41.772 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleMerchantPortalHikariPool - Added connection oracle.jdbc.driver.T4CConnection@65e620b0
2025-08-22 03:24:41.774 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Start completed.
2025-08-22 03:24:41.865 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Starting...
2025-08-22 03:24:41.985 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleOneReportHikariPool - Added connection oracle.jdbc.driver.T4CConnection@1760e594
2025-08-22 03:24:41.986 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Start completed.
2025-08-22 03:24:42.093 [main] INFO  v.o.t.job.PromotionReportJob - Scheduling PromotionReportJob with cron: 0 05 16 * * *
2025-08-22 03:24:42.106 [main] INFO  v.o.t.job.PromotionReportJob - End PromotionReportJob!
2025-08-22 03:24:42.109 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-08-22 03:24:42.110 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-08-22 03:24:42.112 [main] INFO  v.o.transaction.job.UposReportJob - Scheduling UposReportJob with cron: 0 10 16 * * *
2025-08-22 03:24:42.113 [main] INFO  v.o.transaction.job.UposReportJob - End UposReportJob!
2025-08-22 03:24:42.995 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 8397 (http)
2025-08-22 03:24:43.197 [main] INFO  v.o.t.TransactionAppApplication - Started TransactionAppApplication in 5.429 seconds (process running for 7.799)
2025-08-22 03:25:11.010 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: GET http://localhost:8397/ma-service/api/v1/transactions/transaction/detail?transactionId=*********&transactionType=Purchase&paymentMethod=QT
2025-08-22 03:25:11.011 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-08-22 03:25:11.011 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-08-22 03:25:11.011 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-08-22 03:25:11.011 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-08-22 03:25:11.011 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-08-22 03:25:11.012 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-08-22 03:25:11.012 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/mav2-main/
2025-08-22 03:25:11.012 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-08-22 03:25:11.012 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-08-22 03:25:11.012 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-08-22 03:25:11.012 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-08-22 03:25:11.012 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-08-22 03:25:11.012 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-08-22 03:25:11.012 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-08-22 03:25:11.012 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 417604
2025-08-22 03:25:11.012 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; JSESSIONID=dummy; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ1ODc1NjIsInVzZXIiOnsiZW1haWwiOiJhZG1pbkBvbmVwYXkudm4iLCJmdWxsX25hbWUiOiJBZG1pbiAxIiwiaWQiOiI1MUREN0M5QzdEQTZBMjc4OEY0QUI1M0I1NkJFNjgxMSIsImpvYl90aXRsZSI6IkFkbWluIGFsw7QyMjIiLCJwaG9uZSI6IjA5MDEyMzQ1NjEifX0.hEjfsrAg9gqUPMiXcjcugUJwwz7RVYeT8g3lnbyOc2Y
2025-08-22 03:25:11.012 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-22 03:25:11.012 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 394f5d62-dc8d-4ab5-9627-3add1c797af4
2025-08-22 03:25:11.012 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8397
2025-08-22 03:25:11.012 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-08-22 03:25:11.016 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-08-22 03:25:11.063 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=********* ,paymentMethod=QT ,transactionType=Purchase ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-22 03:25:11.063 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-08-22 03:25:11.130 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/********* ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-22 03:25:11.217 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"transaction_id":"*********","original_transaction_id":"*********","transaction_type":"Purchase","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"17","acquirer_name":"","acquirer_short_name":""},"transaction_reference":"TEST_17558312773011958313560","amount":{"total":10000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"512345****0008","card_type":"Mastercard","name_on_card":"NGUYEN VAN THANH","card_date":{"month":"12","year":"25"}},"merchant_id":"TESTPCI","authentication":{"authorization_code":"463487"},"transaction_time":"2025-08-22T09:54:55Z","transaction_status":"0","transaction_ref_number":"*********","ip_address":"************","ip_proxy":"N","bin_country":"ANGUILLA","csc_result_code":"","verification_security_level":"","response_code":"0","can_void":true,"risk_assesment":"Not Assessed","bank_id":"VIETINBANK","wait_for_approval_amount":"0.00","advance_status":"Successful","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"OTHER","acqResponseCode":"","status3ds":"","riskOverAllResult":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"NGUYEN VAN THANH","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00","installment_statement_date":"","reasonName":""}
2025-08-22 03:25:11.218 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-08-22 03:25:11.241 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-08-22 03:25:11.254 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-08-22 03:25:11.284 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-08-22T09:54:55, orderAmount=10000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=*********, parentTransactionId=*********, transactionType=Purchase, paymentMethod=QT, transactionCreatedTime=2025-08-22T09:54:55, transactionCompletedTime=2025-08-22T09:54:55, transactionAmount=10000.0, transactionCurrency=VND, transactionStatus=Successful, responseCode=0, merchantTransRef=TEST_17558312773011958313560, parentMerchantTransRef=null, paymentChannel=null, installmentStatus=, operator=null), paymentMethod=PaymentMethod(cardNumber=512345****0008, cardBrand=null, cardType=Mastercard, issuer=VIETINBANK, acquirer=17, cardExpiry=12/25, nameOnCard=NGUYEN VAN THANH, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, settlementAmount=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=InstallmentDetail(priceDifference=0, period=, amountMonthly=0.00), promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=************, ipProxy=N, ipCountry=************, binCountry=ANGUILLA, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null, amountAdvance=null), actions=null, histories=null)
2025-08-22 03:25:11.285 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-08-22 03:25:11.285 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getAdvanceFeeInfo ===================
2025-08-22 03:25:11.482 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ==================== Start getMerchantById ====================
2025-08-22 03:25:11.492 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getParentMerchantTransRef ===================
2025-08-22 03:25:11.515 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getPromotionInfo ===================
2025-08-22 03:25:11.521 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call getTransactionHistory-----------
2025-08-22 03:25:11.521 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: originalTransactionId=********* ,paymentMethod=QT ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-22 03:25:11.521 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionHistory MA-----------
2025-08-22 03:25:11.522 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/*********/history ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-22 03:25:11.531 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION HISTORY API: response code=200 ,response body={"transactions":[{"transaction_id":"*********","original_transaction_id":"*********","response_code":"0","reference_number":"*********","merchant_transaction_ref":"TEST_17558312773011958313560","transaction_type":"Purchase","amount":{"total":10000.0,"currency":"VND"},"status":"0","operator_id":"","merchant_id":"TESTPCI","transaction_time":"2025-08-22T09:54:55Z","advance_status":"Successful","financial_transaction_id":"*********","note":"","parent_id":"*********","dadId":"","subHistories":""}]}
2025-08-22 03:25:11.531 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionHistory MA------------
2025-08-22 03:25:11.531 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION HISTORY INTERNATIONAL: {"transactions":[{"transaction_id":"*********","original_transaction_id":"*********","response_code":"0","reference_number":"*********","merchant_transaction_ref":"TEST_17558312773011958313560","transaction_type":"Purchase","amount":{"total":10000.0,"currency":"VND"},"status":"0","operator_id":"","merchant_id":"TESTPCI","transaction_time":"2025-08-22T09:54:55Z","advance_status":"Successful","financial_transaction_id":"*********","note":"","parent_id":"*********","dadId":"","subHistories":""}]}
2025-08-22 03:25:11.536 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call getTransactionHistory------------
2025-08-22 03:25:11.536 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-08-22 03:25:11.536 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-08-22 03:25:11.767 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-08-22 03:25:11.767 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - list merchant from permission: [OP_TESTVND, OP_VND01, OP_MPGSVND, OP_MPGSUSD, OP_MPAYVND3D, TESTBIDVV3, ONEPAYHM, D_TEST, OP_CYBSVND, TEST3DSMPGS, OP_MPAYVN1, TESTND, OP_TESTK7011, TESTHD3BEN, TESTSTATICQR, OP_TESTK4121, OP_SACOMTH, TESTAPLVNC, TESTORIFLCBS, TESTVCB_5411, OP_TESTK7399, TESTGGPAY, OP_TESTK5331, TESTKOVENA, TESTSTGINVOICE, TESTMERCHANT16KY, TESTOPVPB, TESTVTBCYBER, HUONG1, TESTOPBIDV, TESTTRAGOP, TESTPCI, TESTONEPAY, OP_TESTUSD, OP_SACOMTRVV, ONEPAY, INVOICETG, TESTDMX, TESTTNS, TESTINVOICE, TESTVCB3D2, TESTBIDV, TESTHC, TEST3DS2_LT, MIGS3DS, TESTAXA1, TESTVF, TESTUPOSTG, TESTAOSVNBANK, OP_TESTKBANK, TESTAPPLE, TESTVCBHM1, TESTTVLOKA, TESTVCB_7399, TESTVAGROUP, TESTVCB_6300, OP_TESTM8211, OP_TESTK7372, OP_TESTK7991, TESTPCI4, TESTOPVCB2, TESTSACOMCYBER, TESTUSD, OP_TESTVP5732, OP_TESTVP6300, ONEPAYMT, TESTATM, OPTEST, TESTTNSUSD, MPAYVND, TESTBIDV1, OP_VPBMPGS3, TESTAWPOSTG, TESTTRAGOP1, TESTSAMSUNG, TESTOP_VTB2, TESTUPOSKBANK, TESTAPLBANK, TESTVCB_8211, TESTMERCHANTID20KYTU, MONPAYOUT, TESTVIETQR, TESTINVOICETG, OP_TESTVP8211, TESTPAYOUT2, TEST, OP_SACOMCBSU, OP_VPBMPGS1, TESTONEPAY20, TESTVJU, OP_TESTCONFIG, OP_TESTSS, OP_TESTBHX, OPTESTHB, TESTAWPOS, TESTACVNC, OP_TESTK4900, TESTONEPAYTHB, ONEPAY_REFUND, TESTAPLTG, OP_TESTK5814, TESTVCB_4722, OP_TESTK5722, TESTPAYOUT1, TESTOPVCB, TESTIDTEST4, TESTONEPAYDVC, OP_SACOMCBSV, OP_SACOMTRVU, ****************, OPMPAY, FALSE, TESTDUONG, OP_VPBMPGS2, TESTSACOMCBU, TESTLZD, TESTSAPO, OP_TESTAUTH, TESTMEGA1, OP_TESTMID, TESTVTASABRE, OP_TESTK4722, TESTOP_VTB3, OP_SACOMVIN, OP_TESTK8220, TESTCNY, TESTVCB_4900, OP_TESTK5698, TESTPCI3, ANHTVTEST, TESTTOKEN, TESTONEPAY2, MONPAYCOLLECT, OP_TESTVP5411, TESTSHOPIFYTG, TESTOP_D, TESTONEPAYDVTT, TESTTRAGOP2, TESTBIDVU3, TESTBIDVV2, TESTTOKENUSD, TESTMEGA, TEST3DS2_MK, TEST3DS2_TN, TESTVFVND, TESTVTB3D2, TESTENETVIET, TESTSHOPIFY, TESTAPPLEND, TESTVCBHM2, TESTTRAGOP3, TESTOKENOP, TESTKONEVA, TESTOP_VTB1, AUTOAPLND, TESTSSPAY, TESTSAMSUNGPL, TESTVCB_5732, TESTAES, OP_TESTK7832, TESTPCI2, TESTJBAUSD, TESTVCBCYBER, TESTMERCHANT15K, OPPREPAID, TESTONEPAYUSD, OP_MPAYVND, TESTSACOMCBV, OP_VCBVND, TESTSAPO2, TESTBIDVCBSU, TESTVCB3D2O, TESTMAFC, OP_TESTOP20, TESTAXA, TESTTGDD3D2, TESTPR, TESTHC1, ONEPAYHB, TESTSSBNPL, OP_TESTK8299, TESTHD2BEN, TESTPCIEM, TESTMCD, OP_TESTK5732, TESTPROMMG, OP_TESTTT, TESTAUTOMSP, OP_TESTUSD2, OP_TESTAPPAY, TESTTHB, TESTSOS, TESTAPPLETG, OP_TESTK5977, EKKO1, TESTONEPAY1, TESTIDTEST6, TESTONEPAYDVDT, TESTBIDVCSU2, ****************, OP_VCBUSD, TESTBIDVCBSV, OP_TEST3DS, OP_TEST3DS2V, OP_CYBSUSD, TESTTHSC, TESTBNPL, TESTAOSVNC, TESTPROM, OP_SACOMECOM, TESTQRTINH, TESTTOKENDV, TESTUPOS, OP_TESTK5969, OP_TESTK5816, TESTONEPAY3, TESTPCI1, TESTKBANKCYBER, TESTVPBCYBER, TEST_OP2B, OP_TESTVP4900, TESTIDTEST3, TESTPAYPAL]
2025-08-22 03:25:11.769 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-08-22 03:25:11.771 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-08-22 03:25:11.771 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start checkPerAndActionByUserId-----------
2025-08-22 03:25:11.817 [reactor-http-epoll-3] INFO  v.o.t.repository.AcquirerRepository - Starting getListAcquirer - calling function ONEDATA.GET_LIST_ACQUIRER
2025-08-22 03:27:36.589 [reactor-http-epoll-3] INFO  v.o.t.util.CheckPermissionUtil - checkCutoffDeadline transactionTime=2025-08-22T09:54:55, cutoffTime=2025-08-22T10:00, now=2025-08-22T03:27:36.*********, hour=10
