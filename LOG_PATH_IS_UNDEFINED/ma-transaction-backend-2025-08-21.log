2025-08-21 10:43:35.462 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 2155493 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-08-21 10:43:35.465 [main] INFO  v.o.t.TransactionAppApplication - The following 1 profile is active: "dev"
2025-08-21 10:43:36.276 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-08-21 10:43:36.465 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 179 ms. Found 2 R2DBC repository interfaces.
2025-08-21 10:43:38.149 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Starting...
2025-08-21 10:43:38.470 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleMerchantPortalHikariPool - Added connection oracle.jdbc.driver.T4CConnection@58aa5c94
2025-08-21 10:43:38.471 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Start completed.
2025-08-21 10:43:38.545 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Starting...
2025-08-21 10:43:38.661 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleOneReportHikariPool - Added connection oracle.jdbc.driver.T4CConnection@6ec3d8e4
2025-08-21 10:43:38.661 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Start completed.
2025-08-21 10:43:38.810 [main] INFO  v.o.t.job.PromotionReportJob - Scheduling PromotionReportJob with cron: 0 05 16 * * *
2025-08-21 10:43:38.821 [main] INFO  v.o.t.job.PromotionReportJob - End PromotionReportJob!
2025-08-21 10:43:38.824 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-08-21 10:43:38.825 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-08-21 10:43:38.827 [main] INFO  v.o.transaction.job.UposReportJob - Scheduling UposReportJob with cron: 0 10 16 * * *
2025-08-21 10:43:38.831 [main] INFO  v.o.transaction.job.UposReportJob - End UposReportJob!
2025-08-21 10:43:39.740 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 8396 (http)
2025-08-21 10:43:39.923 [main] INFO  v.o.t.TransactionAppApplication - Started TransactionAppApplication in 5.142 seconds (process running for 7.106)
2025-08-21 10:44:23.081 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: GET http://localhost:8396/ma-service/api/v1/transactions/transaction/detail?transactionId=868987&transactionType=Request%20Refund&paymentMethod=Apple%20Pay
2025-08-21 10:44:23.082 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-08-21 10:44:23.082 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-08-21 10:44:23.082 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-08-21 10:44:23.082 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-08-21 10:44:23.082 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-08-21 10:44:23.082 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-08-21 10:44:23.082 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/mav2-main/
2025-08-21 10:44:23.082 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-08-21 10:44:23.082 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-08-21 10:44:23.083 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-08-21 10:44:23.083 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-08-21 10:44:23.083 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-08-21 10:44:23.083 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-08-21 10:44:23.083 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-08-21 10:44:23.083 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 417604
2025-08-21 10:44:23.083 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; JSESSIONID=dummy; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ1ODc1NjIsInVzZXIiOnsiZW1haWwiOiJhZG1pbkBvbmVwYXkudm4iLCJmdWxsX25hbWUiOiJBZG1pbiAxIiwiaWQiOiI1MUREN0M5QzdEQTZBMjc4OEY0QUI1M0I1NkJFNjgxMSIsImpvYl90aXRsZSI6IkFkbWluIGFsw7QyMjIiLCJwaG9uZSI6IjA5MDEyMzQ1NjEifX0.hEjfsrAg9gqUPMiXcjcugUJwwz7RVYeT8g3lnbyOc2Y
2025-08-21 10:44:23.083 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 10:44:23.083 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 3aed8870-ff51-403a-91dc-fd01f0c09dd2
2025-08-21 10:44:23.083 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8396
2025-08-21 10:44:23.083 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-08-21 10:44:23.087 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-08-21 10:44:23.139 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=868987 ,paymentMethod=Apple Pay ,transactionType=Request Refund ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 10:44:23.140 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-08-21 10:44:23.205 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/868987 ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 10:44:23.288 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"transaction_id":"868987","original_transaction_id":"*********","transaction_type":"Request Refund","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"17","acquirer_name":"","acquirer_short_name":""},"transaction_reference":"TESTPCI_1755770718806","amount":{"total":107.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"APP Visa 1741","card_type":"Visa","card_date":{"month":"01","year":"30"},"commercical_card":"!01","commercial_card_indicator":"0"},"merchant_id":"TESTPCI","authentication":{"authentication_type":"Verified by Visa"},"transaction_time":"2025-08-21T17:05:19Z","transaction_status":"","transaction_ref_number":"************","ip_address":"***************","ip_proxy":"","bin_country":"","csc_result_code":"","verification_security_level":"5","response_code":"","can_void":false,"risk_assesment":"Not Assessed","bank_id":"","wait_for_approval_amount":"0.00","advance_status":"Merchant Rejected","source":"Apple Pay","networkTransId":"","tokenNumber":"401200***1112","deviceId":"************","tokenExpiry":"30/01/2031","type":"CREDIT","acqResponseCode":"","status3ds":"","riskOverAllResult":"","cardLevelIndicator":"","customer_mobile":"","customer_email":"","fraud_check":"","customer_address":"","customer_name":"","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"","installment_cus_email":"","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00","installment_statement_date":"","reasonName":""}
2025-08-21 10:44:23.289 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-08-21 10:44:23.304 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-08-21 10:44:23.313 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-08-21 10:44:23.333 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-08-21T17:05:19, orderAmount=107.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=868987, parentTransactionId=*********, transactionType=Request Refund, paymentMethod=Apple Pay, transactionCreatedTime=2025-08-21T17:05:19, transactionCompletedTime=2025-08-21T17:05:19, transactionAmount=107.0, transactionCurrency=VND, transactionStatus=Merchant Rejected, responseCode=, merchantTransRef=TESTPCI_1755770718806, parentMerchantTransRef=null, paymentChannel=null, installmentStatus=, operator=null), paymentMethod=PaymentMethod(cardNumber=APP Visa 1741, cardBrand=null, cardType=Visa, issuer=, acquirer=17, cardExpiry=01/30, nameOnCard=null, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, settlementAmount=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=InstallmentDetail(priceDifference=0, period=, amountMonthly=0.00), promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=***************, ipProxy=, ipCountry=***************, binCountry=, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null, amountAdvance=null), actions=null, histories=null)
2025-08-21 10:44:23.333 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-08-21 10:44:23.334 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/refund/868987 ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 10:44:23.344 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"row_num":"0","merchant_id":"TESTPCI","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"17","acquirer_name":"Vietinbank - MPGS","acquirer_short_name":"Vietinbank - MPGS"},"original_transaction_id":"*********","transaction_id":"868987","transaction_time":"2025-08-21T17:05:19Z","transaction_type":"Request Refund","transaction_status":"404","authentication":"","amount":{"currency":"VND","total":107.0,"purchase_total":107.0},"transaction_purchase_time":"2025-08-20T16:53:19Z","card":{"card_number":"APP Visa 1741","card_type":"Visa","card_date":{"month":"01","year":"30"}},"csc_result_code":"","refund_type":"1","reasonName":""}
2025-08-21 10:44:23.345 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-08-21 10:44:23.345 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonRequestRefundIntenational...
2025-08-21 10:44:23.346 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonRequestRefundIntenational...
2025-08-21 10:44:23.346 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE REFUND INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-08-21T17:05:19, orderAmount=107.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=868987, parentTransactionId=*********, transactionType=Request Refund, paymentMethod=Apple Pay, transactionCreatedTime=2025-08-21T17:05:19, transactionCompletedTime=2025-08-21T17:05:19, transactionAmount=107.0, transactionCurrency=VND, transactionStatus=404, responseCode=null, merchantTransRef=null, parentMerchantTransRef=null, paymentChannel=null, installmentStatus=null, operator=null), paymentMethod=PaymentMethod(cardNumber=APP Visa 1741, cardBrand=null, cardType=Visa, issuer=Vietinbank - MPGS, acquirer=17, cardExpiry=01/30, nameOnCard=null, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, settlementAmount=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=null, promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=null), riskManagement=RiskManagement(ipAddress=***************, ipProxy=, ipCountry=***************, binCountry=, riskAssessment=Not Assessed), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null, amountAdvance=null), actions=null, histories=null)
2025-08-21 10:44:23.346 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-08-21 10:44:23.346 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getAdvanceFeeInfo ===================
2025-08-21 10:44:23.535 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ==================== Start getMerchantById ====================
2025-08-21 10:44:23.544 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getTransactionInfo ===================
2025-08-21 10:44:23.549 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getParentMerchantTransRef ===================
2025-08-21 10:44:27.577 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getPromotionInfo ===================
2025-08-21 10:44:27.584 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call getTransactionHistory-----------
2025-08-21 10:44:27.585 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: originalTransactionId=********* ,paymentMethod=Apple Pay ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 10:44:27.585 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionHistory MA-----------
2025-08-21 10:44:27.585 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/*********/history ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 10:44:27.596 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION HISTORY API: response code=200 ,response body={"transactions":[{"transaction_id":"868987","original_transaction_id":"*********","response_code":"","reference_number":"************","merchant_transaction_ref":"TESTPCI_1755770718806","transaction_type":"Request Refund","amount":{"total":107.0,"currency":"VND"},"status":"404","operator_id":"<EMAIL>","merchant_id":"TESTPCI","transaction_time":"2025-08-21T17:05:19Z","advance_status":"Merchant Rejected","financial_transaction_id":"868987","note":"","parent_id":"*********","dadId":"*********","subHistories":""},{"transaction_id":"*********","original_transaction_id":"*********","response_code":"0","reference_number":"************","merchant_transaction_ref":"TEST_1755683507","transaction_type":"Purchase","amount":{"total":107.0,"currency":"VND"},"status":"0","operator_id":"","merchant_id":"TESTPCI","transaction_time":"2025-08-20T16:53:19Z","advance_status":"Successful","financial_transaction_id":"************","note":"","parent_id":"*********","dadId":"","subHistories":""}]}
2025-08-21 10:44:27.597 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionHistory MA------------
2025-08-21 10:44:27.597 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION HISTORY INTERNATIONAL: {"transactions":[{"transaction_id":"868987","original_transaction_id":"*********","response_code":"","reference_number":"************","merchant_transaction_ref":"TESTPCI_1755770718806","transaction_type":"Request Refund","amount":{"total":107.0,"currency":"VND"},"status":"404","operator_id":"<EMAIL>","merchant_id":"TESTPCI","transaction_time":"2025-08-21T17:05:19Z","advance_status":"Merchant Rejected","financial_transaction_id":"868987","note":"","parent_id":"*********","dadId":"*********","subHistories":""},{"transaction_id":"*********","original_transaction_id":"*********","response_code":"0","reference_number":"************","merchant_transaction_ref":"TEST_1755683507","transaction_type":"Purchase","amount":{"total":107.0,"currency":"VND"},"status":"0","operator_id":"","merchant_id":"TESTPCI","transaction_time":"2025-08-20T16:53:19Z","advance_status":"Successful","financial_transaction_id":"************","note":"","parent_id":"*********","dadId":"","subHistories":""}]}
2025-08-21 10:44:27.603 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call getTransactionHistory------------
2025-08-21 10:44:27.603 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-08-21 10:44:27.603 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=Apple Pay
2025-08-21 10:44:27.848 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-08-21 10:44:27.848 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - list merchant from permission: [OP_TESTVND, OP_VND01, OP_MPGSVND, OP_MPGSUSD, OP_MPAYVND3D, TESTBIDVV3, ONEPAYHM, D_TEST, OP_CYBSVND, TEST3DSMPGS, OP_MPAYVN1, TESTND, OP_TESTK7011, TESTHD3BEN, TESTSTATICQR, OP_TESTK4121, OP_SACOMTH, TESTAPLVNC, TESTORIFLCBS, TESTVCB_5411, OP_TESTK7399, TESTGGPAY, OP_TESTK5331, TESTKOVENA, TESTSTGINVOICE, TESTMERCHANT16KY, TESTOPVPB, TESTVTBCYBER, HUONG1, TESTOPBIDV, TESTTRAGOP, TESTPCI, TESTONEPAY, OP_TESTUSD, OP_SACOMTRVV, ONEPAY, INVOICETG, TESTDMX, TESTTNS, TESTINVOICE, TESTVCB3D2, TESTBIDV, TESTHC, TEST3DS2_LT, MIGS3DS, TESTAXA1, TESTVF, TESTUPOSTG, TESTAOSVNBANK, OP_TESTKBANK, TESTAPPLE, TESTVCBHM1, TESTTVLOKA, TESTVCB_7399, TESTVAGROUP, TESTVCB_6300, OP_TESTM8211, OP_TESTK7372, OP_TESTK7991, TESTPCI4, TESTOPVCB2, TESTSACOMCYBER, TESTUSD, OP_TESTVP5732, OP_TESTVP6300, ONEPAYMT, TESTATM, OPTEST, TESTTNSUSD, MPAYVND, TESTBIDV1, OP_VPBMPGS3, TESTAWPOSTG, TESTTRAGOP1, TESTSAMSUNG, TESTOP_VTB2, TESTUPOSKBANK, TESTAPLBANK, TESTVCB_8211, TESTMERCHANTID20KYTU, MONPAYOUT, TESTVIETQR, TESTINVOICETG, OP_TESTVP8211, TESTPAYOUT2, TEST, OP_SACOMCBSU, OP_VPBMPGS1, TESTONEPAY20, TESTVJU, OP_TESTCONFIG, OP_TESTSS, OP_TESTBHX, OPTESTHB, TESTAWPOS, TESTACVNC, OP_TESTK4900, TESTONEPAYTHB, ONEPAY_REFUND, TESTAPLTG, OP_TESTK5814, TESTVCB_4722, OP_TESTK5722, TESTPAYOUT1, TESTOPVCB, TESTIDTEST4, TESTONEPAYDVC, OP_SACOMCBSV, OP_SACOMTRVU, ****************, OPMPAY, FALSE, TESTDUONG, OP_VPBMPGS2, TESTSACOMCBU, TESTLZD, TESTSAPO, OP_TESTAUTH, TESTMEGA1, OP_TESTMID, TESTVTASABRE, OP_TESTK4722, TESTOP_VTB3, OP_SACOMVIN, OP_TESTK8220, TESTCNY, TESTVCB_4900, OP_TESTK5698, TESTPCI3, ANHTVTEST, TESTTOKEN, TESTONEPAY2, MONPAYCOLLECT, OP_TESTVP5411, TESTSHOPIFYTG, TESTOP_D, TESTONEPAYDVTT, TESTTRAGOP2, TESTBIDVU3, TESTBIDVV2, TESTTOKENUSD, TESTMEGA, TEST3DS2_MK, TEST3DS2_TN, TESTVFVND, TESTVTB3D2, TESTENETVIET, TESTSHOPIFY, TESTAPPLEND, TESTVCBHM2, TESTTRAGOP3, TESTOKENOP, TESTKONEVA, TESTOP_VTB1, AUTOAPLND, TESTSSPAY, TESTSAMSUNGPL, TESTVCB_5732, TESTAES, OP_TESTK7832, TESTPCI2, TESTJBAUSD, TESTVCBCYBER, TESTMERCHANT15K, OPPREPAID, TESTONEPAYUSD, OP_MPAYVND, TESTSACOMCBV, OP_VCBVND, TESTSAPO2, TESTBIDVCBSU, TESTVCB3D2O, TESTMAFC, OP_TESTOP20, TESTAXA, TESTTGDD3D2, TESTPR, TESTHC1, ONEPAYHB, TESTSSBNPL, OP_TESTK8299, TESTHD2BEN, TESTPCIEM, TESTMCD, OP_TESTK5732, TESTPROMMG, OP_TESTTT, TESTAUTOMSP, OP_TESTUSD2, OP_TESTAPPAY, TESTTHB, TESTSOS, TESTAPPLETG, OP_TESTK5977, EKKO1, TESTONEPAY1, TESTIDTEST6, TESTONEPAYDVDT, TESTBIDVCSU2, ****************, OP_VCBUSD, TESTBIDVCBSV, OP_TEST3DS, OP_TEST3DS2V, OP_CYBSUSD, TESTTHSC, TESTBNPL, TESTAOSVNC, TESTPROM, OP_SACOMECOM, TESTQRTINH, TESTTOKENDV, TESTUPOS, OP_TESTK5969, OP_TESTK5816, TESTONEPAY3, TESTPCI1, TESTKBANKCYBER, TESTVPBCYBER, TEST_OP2B, OP_TESTVP4900, TESTIDTEST3, TESTPAYPAL]
2025-08-21 10:44:27.851 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-08-21 10:44:27.852 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-08-21 10:44:27.853 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start checkPerAndActionByUserId-----------
2025-08-21 10:44:27.900 [reactor-http-epoll-3] INFO  v.o.t.repository.AcquirerRepository - Starting getListAcquirer - calling function ONEDATA.GET_LIST_ACQUIRER
2025-08-21 10:44:27.963 [reactor-http-epoll-3] INFO  v.o.t.util.CheckPermissionUtil - checkActionVoid paymentMethod invalid, transactionId=868987, transactionType=Request Refund
2025-08-21 10:44:27.964 [reactor-http-epoll-3] INFO  v.o.t.util.CheckPermissionUtil - checkActionRefund paymentMethod invalid, transactionId=868987, transactionType=Request Refund
2025-08-21 10:44:27.964 [reactor-http-epoll-3] INFO  v.o.t.util.CheckPermissionUtil - checkActionCapture transactionType invalid, transactionId=868987, transactionType=Request Refund
2025-08-21 10:44:27.964 [reactor-http-epoll-3] INFO  v.o.t.util.CheckPermissionUtil - checkApproveAndRejectRefund transactionStatus invalid, transactionId=868987, transactionStatus=Merchant Rejected
2025-08-21 10:44:28.025 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Response: GET http://localhost:8396/ma-service/api/v1/transactions/transaction/detail?transactionId=868987&transactionType=Request%20Refund&paymentMethod=Apple%20Pay - Status: 200 OK
2025-08-21 11:05:11.280 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: GET http://localhost:8396/ma-service/api/v1/transactions/transaction/detail?transactionId=*********&transactionType=Authorize&paymentMethod=Q
2025-08-21 11:05:11.280 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-08-21 11:05:11.281 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-08-21 11:05:11.281 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-08-21 11:05:11.281 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-08-21 11:05:11.281 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-08-21 11:05:11.281 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-08-21 11:05:11.281 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/mav2-main/
2025-08-21 11:05:11.281 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-08-21 11:05:11.281 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-08-21 11:05:11.281 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-08-21 11:05:11.281 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-08-21 11:05:11.281 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-08-21 11:05:11.281 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-08-21 11:05:11.281 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-08-21 11:05:11.281 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 417604
2025-08-21 11:05:11.281 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; JSESSIONID=dummy; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ1ODc1NjIsInVzZXIiOnsiZW1haWwiOiJhZG1pbkBvbmVwYXkudm4iLCJmdWxsX25hbWUiOiJBZG1pbiAxIiwiaWQiOiI1MUREN0M5QzdEQTZBMjc4OEY0QUI1M0I1NkJFNjgxMSIsImpvYl90aXRsZSI6IkFkbWluIGFsw7QyMjIiLCJwaG9uZSI6IjA5MDEyMzQ1NjEifX0.hEjfsrAg9gqUPMiXcjcugUJwwz7RVYeT8g3lnbyOc2Y
2025-08-21 11:05:11.282 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 11:05:11.282 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 49b2eb26-5525-4377-b454-a5b1c63342db
2025-08-21 11:05:11.282 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8396
2025-08-21 11:05:11.282 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-08-21 11:05:11.282 [reactor-http-epoll-4] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-08-21 11:05:11.285 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=********* ,paymentMethod=Q ,transactionType=Authorize ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 11:05:11.285 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - Dont have payment method: Q
2025-08-21 11:05:11.285 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-08-21 11:05:11.300 [reactor-http-epoll-4] ERROR v.o.t.e.GlobalExceptionHandler - Unhandled exception: 
org.springframework.web.server.ResponseStatusException: 404 NOT_FOUND "Transaction ID not found"
	at vn.onepay.transaction.controller.TransactionController.getTransactionDetail(TransactionController.java:169)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#getTransactionDetail(ServerWebExchange, String, String, String, String) [DispatcherHandler]
Original Stack Trace:
		at vn.onepay.transaction.controller.TransactionController.getTransactionDetail(TransactionController.java:169)
		at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
		at java.base/java.lang.reflect.Method.invoke(Method.java:580)
		at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
		at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
		at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onComplete(FluxDefaultIfEmpty.java:134)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onComplete(FluxSwitchIfEmpty.java:85)
		at reactor.core.publisher.MonoSupplier$MonoSupplierSubscription.request(MonoSupplier.java:148)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2241)
		at reactor.core.publisher.MonoSupplier.subscribe(MonoSupplier.java:48)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onComplete(FluxSwitchIfEmpty.java:82)
		at reactor.core.publisher.Operators.complete(Operators.java:137)
		at reactor.core.publisher.FluxFlatMap.trySubscribeScalarMap(FluxFlatMap.java:146)
		at reactor.core.publisher.MonoFlatMap.subscribeOrReturn(MonoFlatMap.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:63)
		at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onSubscribe(MonoIgnoreThen.java:135)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
		at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:241)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onComplete(MonoFlatMap.java:189)
		at reactor.core.publisher.Operators.complete(Operators.java:137)
		at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:121)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)
		at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.innerNext(FluxConcatMapNoPrefetch.java:259)
		at reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:865)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
		at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
		at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
		at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.request(MonoPeekTerminal.java:139)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.request(FluxMapFuseable.java:171)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.request(Operators.java:2331)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:339)
		at reactor.core.publisher.MonoNext$NextSubscriber.request(MonoNext.java:108)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2241)
		at reactor.core.publisher.MonoNext$NextSubscriber.onSubscribe(MonoNext.java:70)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onSubscribe(FluxConcatMapNoPrefetch.java:164)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:201)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:83)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
		at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
		at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
		at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-21 11:05:11.308 [reactor-http-epoll-4] WARN  o.s.w.r.r.m.a.RequestMappingHandlerAdapter - [74a38240-2] Failure in @ExceptionHandler vn.onepay.transaction.exception.GlobalExceptionHandler#handleNotFound(ResponseStatusException, ServerWebExchange)
org.springframework.web.server.ResponseStatusException: 404 NOT_FOUND "Not found"
	at vn.onepay.transaction.exception.GlobalExceptionHandler.handleNotFound(GlobalExceptionHandler.java:23)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
	at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.request(MonoPeekTerminal.java:139)
	at reactor.core.publisher.MonoZip$ZipInner.onSubscribe(MonoZip.java:470)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onSubscribe(MonoPeekTerminal.java:152)
	at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onSubscribe(FluxOnErrorResume.java:74)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
	at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onError(FluxOnAssembly.java:544)
	at reactor.core.publisher.Operators.error(Operators.java:198)
	at reactor.core.publisher.FluxFlatMap.trySubscribeScalarMap(FluxFlatMap.java:136)
	at reactor.core.publisher.MonoFlatMap.subscribeOrReturn(MonoFlatMap.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:63)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onNext(FluxPeek.java:200)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.complete(MonoIgnoreThen.java:294)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onNext(MonoIgnoreThen.java:188)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
	at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
	at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onComplete(FluxDefaultIfEmpty.java:134)
	at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onComplete(FluxSwitchIfEmpty.java:85)
	at reactor.core.publisher.MonoSupplier$MonoSupplierSubscription.request(MonoSupplier.java:148)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2241)
	at reactor.core.publisher.MonoSupplier.subscribe(MonoSupplier.java:48)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onComplete(FluxSwitchIfEmpty.java:82)
	at reactor.core.publisher.Operators.complete(Operators.java:137)
	at reactor.core.publisher.FluxFlatMap.trySubscribeScalarMap(FluxFlatMap.java:146)
	at reactor.core.publisher.MonoFlatMap.subscribeOrReturn(MonoFlatMap.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:63)
	at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onSubscribe(MonoIgnoreThen.java:135)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
	at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:241)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onComplete(MonoFlatMap.java:189)
	at reactor.core.publisher.Operators.complete(Operators.java:137)
	at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:121)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)
	at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.innerNext(FluxConcatMapNoPrefetch.java:259)
	at reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:865)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.request(MonoPeekTerminal.java:139)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.request(FluxMapFuseable.java:171)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.request(Operators.java:2331)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:339)
	at reactor.core.publisher.MonoNext$NextSubscriber.request(MonoNext.java:108)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2241)
	at reactor.core.publisher.MonoNext$NextSubscriber.onSubscribe(MonoNext.java:70)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onSubscribe(FluxConcatMapNoPrefetch.java:164)
	at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:201)
	at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:83)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
	at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
	at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
	at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
	at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
	at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-21 11:05:11.310 [reactor-http-epoll-4] ERROR v.o.t.exception.RequestLoggingFilter - Request error: GET http://localhost:8396/ma-service/api/v1/transactions/transaction/detail?transactionId=*********&transactionType=Authorize&paymentMethod=Q - Exception: 404 NOT_FOUND "Transaction ID not found"
org.springframework.web.server.ResponseStatusException: 404 NOT_FOUND "Transaction ID not found"
	at vn.onepay.transaction.controller.TransactionController.getTransactionDetail(TransactionController.java:169)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#getTransactionDetail(ServerWebExchange, String, String, String, String) [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x0000748bd060cba0 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
Original Stack Trace:
		at vn.onepay.transaction.controller.TransactionController.getTransactionDetail(TransactionController.java:169)
		at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
		at java.base/java.lang.reflect.Method.invoke(Method.java:580)
		at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
		at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
		at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.FluxDefaultIfEmpty$DefaultIfEmptySubscriber.onComplete(FluxDefaultIfEmpty.java:134)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onComplete(FluxSwitchIfEmpty.java:85)
		at reactor.core.publisher.MonoSupplier$MonoSupplierSubscription.request(MonoSupplier.java:148)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2241)
		at reactor.core.publisher.MonoSupplier.subscribe(MonoSupplier.java:48)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onComplete(FluxSwitchIfEmpty.java:82)
		at reactor.core.publisher.Operators.complete(Operators.java:137)
		at reactor.core.publisher.FluxFlatMap.trySubscribeScalarMap(FluxFlatMap.java:146)
		at reactor.core.publisher.MonoFlatMap.subscribeOrReturn(MonoFlatMap.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:63)
		at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onSubscribe(MonoIgnoreThen.java:135)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
		at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:241)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.onComplete(MonoIgnoreThen.java:204)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onComplete(MonoFlatMap.java:189)
		at reactor.core.publisher.Operators.complete(Operators.java:137)
		at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:121)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)
		at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.innerNext(FluxConcatMapNoPrefetch.java:259)
		at reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:865)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:129)
		at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
		at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
		at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.request(MonoPeekTerminal.java:139)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.request(FluxMapFuseable.java:171)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.request(Operators.java:2331)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:339)
		at reactor.core.publisher.MonoNext$NextSubscriber.request(MonoNext.java:108)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
		at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2241)
		at reactor.core.publisher.MonoNext$NextSubscriber.onSubscribe(MonoNext.java:70)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onSubscribe(FluxConcatMapNoPrefetch.java:164)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:201)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:83)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:265)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)
		at reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)
		at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1249)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:716)
		at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:486)
		at reactor.netty.http.server.HttpServerOperations.handleDefaultHttpRequest(HttpServerOperations.java:856)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:782)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:272)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-21 11:05:30.471 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: GET http://localhost:8396/ma-service/api/v1/transactions/transaction/detail?transactionId=*********&transactionType=Authorize&paymentMethod=QT
2025-08-21 11:05:30.471 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-08-21 11:05:30.471 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-08-21 11:05:30.472 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-08-21 11:05:30.472 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-08-21 11:05:30.472 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-08-21 11:05:30.472 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-08-21 11:05:30.472 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/mav2-main/
2025-08-21 11:05:30.472 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-08-21 11:05:30.472 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-08-21 11:05:30.472 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-08-21 11:05:30.472 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-08-21 11:05:30.472 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-08-21 11:05:30.472 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-08-21 11:05:30.472 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-08-21 11:05:30.472 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 417604
2025-08-21 11:05:30.472 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; JSESSIONID=dummy; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ1ODc1NjIsInVzZXIiOnsiZW1haWwiOiJhZG1pbkBvbmVwYXkudm4iLCJmdWxsX25hbWUiOiJBZG1pbiAxIiwiaWQiOiI1MUREN0M5QzdEQTZBMjc4OEY0QUI1M0I1NkJFNjgxMSIsImpvYl90aXRsZSI6IkFkbWluIGFsw7QyMjIiLCJwaG9uZSI6IjA5MDEyMzQ1NjEifX0.hEjfsrAg9gqUPMiXcjcugUJwwz7RVYeT8g3lnbyOc2Y
2025-08-21 11:05:30.473 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 11:05:30.473 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 3ff579a9-6774-48cd-89a9-562854ae9449
2025-08-21 11:05:30.473 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8396
2025-08-21 11:05:30.473 [reactor-http-epoll-4] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-08-21 11:05:30.473 [reactor-http-epoll-4] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-08-21 11:05:30.474 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=********* ,paymentMethod=QT ,transactionType=Authorize ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 11:05:30.475 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-08-21 11:05:30.476 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/********* ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 11:05:30.501 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"transaction_id":"*********","original_transaction_id":"*********","transaction_type":"Authorize","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"17","acquirer_name":"","acquirer_short_name":""},"transaction_reference":"TEST_1755506112099348164310","amount":{"total":12000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"340353****0900","card_type":"Amex","name_on_card":"NGUYEN VAN A","card_date":{"month":"12","year":"28"},"commercical_card":"888","commercial_card_indicator":"3"},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y","authentication_type":"Amex Safekey","authorization_code":"013834"},"transaction_time":"2025-08-18T15:35:24Z","transaction_status":"0","transaction_ref_number":"*********","ip_address":"************","ip_proxy":"N","bin_country":"UNITED STATES","csc_result_code":"","verification_security_level":"","response_code":"0","can_void":true,"risk_assesment":"Review Required","bank_id":"AMERICAN EXPRESS US CONSUMER","wait_for_approval_amount":"0.00","advance_status":"Successful","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"CREDIT","acqResponseCode":"","status3ds":"Y","riskOverAllResult":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"NGUYEN VAN A","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00","installment_statement_date":"","reasonName":""}
2025-08-21 11:05:30.501 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-08-21 11:05:30.502 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-08-21 11:05:30.503 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-08-21 11:05:30.504 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-08-18T15:35:24, orderAmount=12000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=*********, parentTransactionId=*********, transactionType=Authorize, paymentMethod=QT, transactionCreatedTime=2025-08-18T15:35:24, transactionCompletedTime=2025-08-18T15:35:24, transactionAmount=12000.0, transactionCurrency=VND, transactionStatus=Successful, responseCode=0, merchantTransRef=TEST_1755506112099348164310, parentMerchantTransRef=null, paymentChannel=null, installmentStatus=, operator=null), paymentMethod=PaymentMethod(cardNumber=340353****0900, cardBrand=null, cardType=Amex, issuer=, acquirer=17, cardExpiry=12/28, nameOnCard=NGUYEN VAN A, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, settlementAmount=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=InstallmentDetail(priceDifference=0, period=, amountMonthly=0.00), promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=************, ipProxy=N, ipCountry=************, binCountry=UNITED STATES, riskAssessment=Review Required), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null, amountAdvance=null), actions=null, histories=null)
2025-08-21 11:05:30.504 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-08-21 11:05:30.504 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getAdvanceFeeInfo ===================
2025-08-21 11:05:30.511 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ==================== Start getMerchantById ====================
2025-08-21 11:05:30.515 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getParentMerchantTransRef ===================
2025-08-21 11:05:30.518 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getPromotionInfo ===================
2025-08-21 11:05:30.521 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call getTransactionHistory-----------
2025-08-21 11:05:30.522 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: originalTransactionId=********* ,paymentMethod=QT ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 11:05:30.522 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionHistory MA-----------
2025-08-21 11:05:30.522 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/*********/history ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 11:05:30.531 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION HISTORY API: response code=200 ,response body={"transactions":[{"transaction_id":"*********","original_transaction_id":"*********","response_code":"0","reference_number":"*********","merchant_transaction_ref":"TEST_1755506112099348164310","transaction_type":"Authorize","amount":{"total":12000.0,"currency":"VND"},"status":"0","operator_id":"","merchant_id":"TESTPCI","transaction_time":"2025-08-18T15:35:24Z","advance_status":"Successful","financial_transaction_id":"*********","note":"","parent_id":"*********","dadId":"","subHistories":""}]}
2025-08-21 11:05:30.531 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionHistory MA------------
2025-08-21 11:05:30.531 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION HISTORY INTERNATIONAL: {"transactions":[{"transaction_id":"*********","original_transaction_id":"*********","response_code":"0","reference_number":"*********","merchant_transaction_ref":"TEST_1755506112099348164310","transaction_type":"Authorize","amount":{"total":12000.0,"currency":"VND"},"status":"0","operator_id":"","merchant_id":"TESTPCI","transaction_time":"2025-08-18T15:35:24Z","advance_status":"Successful","financial_transaction_id":"*********","note":"","parent_id":"*********","dadId":"","subHistories":""}]}
2025-08-21 11:05:30.532 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call getTransactionHistory------------
2025-08-21 11:05:30.532 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-08-21 11:05:30.532 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-08-21 11:05:30.556 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-08-21 11:05:30.556 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - list merchant from permission: [OP_TESTVND, OP_VND01, OP_MPGSVND, OP_MPGSUSD, OP_MPAYVND3D, TESTBIDVV3, ONEPAYHM, D_TEST, OP_CYBSVND, TEST3DSMPGS, OP_MPAYVN1, TESTND, OP_TESTK7011, TESTHD3BEN, TESTSTATICQR, OP_TESTK4121, OP_SACOMTH, TESTAPLVNC, TESTORIFLCBS, TESTVCB_5411, OP_TESTK7399, TESTGGPAY, OP_TESTK5331, TESTKOVENA, TESTSTGINVOICE, TESTMERCHANT16KY, TESTOPVPB, TESTVTBCYBER, HUONG1, TESTOPBIDV, TESTTRAGOP, TESTPCI, TESTONEPAY, OP_TESTUSD, OP_SACOMTRVV, ONEPAY, INVOICETG, TESTDMX, TESTTNS, TESTINVOICE, TESTVCB3D2, TESTBIDV, TESTHC, TEST3DS2_LT, MIGS3DS, TESTAXA1, TESTVF, TESTUPOSTG, TESTAOSVNBANK, OP_TESTKBANK, TESTAPPLE, TESTVCBHM1, TESTTVLOKA, TESTVCB_7399, TESTVAGROUP, TESTVCB_6300, OP_TESTM8211, OP_TESTK7372, OP_TESTK7991, TESTPCI4, TESTOPVCB2, TESTSACOMCYBER, TESTUSD, OP_TESTVP5732, OP_TESTVP6300, ONEPAYMT, TESTATM, OPTEST, TESTTNSUSD, MPAYVND, TESTBIDV1, OP_VPBMPGS3, TESTAWPOSTG, TESTTRAGOP1, TESTSAMSUNG, TESTOP_VTB2, TESTUPOSKBANK, TESTAPLBANK, TESTVCB_8211, TESTMERCHANTID20KYTU, MONPAYOUT, TESTVIETQR, TESTINVOICETG, OP_TESTVP8211, TESTPAYOUT2, TEST, OP_SACOMCBSU, OP_VPBMPGS1, TESTONEPAY20, TESTVJU, OP_TESTCONFIG, OP_TESTSS, OP_TESTBHX, OPTESTHB, TESTAWPOS, TESTACVNC, OP_TESTK4900, TESTONEPAYTHB, ONEPAY_REFUND, TESTAPLTG, OP_TESTK5814, TESTVCB_4722, OP_TESTK5722, TESTPAYOUT1, TESTOPVCB, TESTIDTEST4, TESTONEPAYDVC, OP_SACOMCBSV, OP_SACOMTRVU, ****************, OPMPAY, FALSE, TESTDUONG, OP_VPBMPGS2, TESTSACOMCBU, TESTLZD, TESTSAPO, OP_TESTAUTH, TESTMEGA1, OP_TESTMID, TESTVTASABRE, OP_TESTK4722, TESTOP_VTB3, OP_SACOMVIN, OP_TESTK8220, TESTCNY, TESTVCB_4900, OP_TESTK5698, TESTPCI3, ANHTVTEST, TESTTOKEN, TESTONEPAY2, MONPAYCOLLECT, OP_TESTVP5411, TESTSHOPIFYTG, TESTOP_D, TESTONEPAYDVTT, TESTTRAGOP2, TESTBIDVU3, TESTBIDVV2, TESTTOKENUSD, TESTMEGA, TEST3DS2_MK, TEST3DS2_TN, TESTVFVND, TESTVTB3D2, TESTENETVIET, TESTSHOPIFY, TESTAPPLEND, TESTVCBHM2, TESTTRAGOP3, TESTOKENOP, TESTKONEVA, TESTOP_VTB1, AUTOAPLND, TESTSSPAY, TESTSAMSUNGPL, TESTVCB_5732, TESTAES, OP_TESTK7832, TESTPCI2, TESTJBAUSD, TESTVCBCYBER, TESTMERCHANT15K, OPPREPAID, TESTONEPAYUSD, OP_MPAYVND, TESTSACOMCBV, OP_VCBVND, TESTSAPO2, TESTBIDVCBSU, TESTVCB3D2O, TESTMAFC, OP_TESTOP20, TESTAXA, TESTTGDD3D2, TESTPR, TESTHC1, ONEPAYHB, TESTSSBNPL, OP_TESTK8299, TESTHD2BEN, TESTPCIEM, TESTMCD, OP_TESTK5732, TESTPROMMG, OP_TESTTT, TESTAUTOMSP, OP_TESTUSD2, OP_TESTAPPAY, TESTTHB, TESTSOS, TESTAPPLETG, OP_TESTK5977, EKKO1, TESTONEPAY1, TESTIDTEST6, TESTONEPAYDVDT, TESTBIDVCSU2, ****************, OP_VCBUSD, TESTBIDVCBSV, OP_TEST3DS, OP_TEST3DS2V, OP_CYBSUSD, TESTTHSC, TESTBNPL, TESTAOSVNC, TESTPROM, OP_SACOMECOM, TESTQRTINH, TESTTOKENDV, TESTUPOS, OP_TESTK5969, OP_TESTK5816, TESTONEPAY3, TESTPCI1, TESTKBANKCYBER, TESTVPBCYBER, TEST_OP2B, OP_TESTVP4900, TESTIDTEST3, TESTPAYPAL]
2025-08-21 11:05:30.556 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-08-21 11:05:30.557 [reactor-http-epoll-4] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-08-21 11:05:30.557 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start checkPerAndActionByUserId-----------
2025-08-21 11:05:52.692 [reactor-http-epoll-4] ERROR v.o.t.util.CheckPermissionUtil - Error parsing date: 2025-08-18T15:35:24
java.time.temporal.UnsupportedTemporalTypeException: Unsupported field: HourOfDay
	at java.base/java.time.LocalDate.get0(LocalDate.java:698)
	at java.base/java.time.LocalDate.getLong(LocalDate.java:678)
	at java.base/java.time.format.DateTimePrintContext.getValue(DateTimePrintContext.java:308)
	at java.base/java.time.format.DateTimeFormatterBuilder$NumberPrinterParser.format(DateTimeFormatterBuilder.java:2914)
	at java.base/java.time.format.DateTimeFormatterBuilder$CompositePrinterParser.format(DateTimeFormatterBuilder.java:2529)
	at java.base/java.time.format.DateTimeFormatter.formatTo(DateTimeFormatter.java:1905)
	at java.base/java.time.format.DateTimeFormatter.format(DateTimeFormatter.java:1879)
	at java.base/java.time.LocalDate.format(LocalDate.java:1797)
	at vn.onepay.transaction.util.CheckPermissionUtil.checkDeadline(CheckPermissionUtil.java:950)
	at vn.onepay.transaction.util.CheckPermissionUtil.checkVoidAuthorizeQT(CheckPermissionUtil.java:540)
	at vn.onepay.transaction.util.CheckPermissionUtil.checkActionVoidAuthorize(CheckPermissionUtil.java:511)
	at vn.onepay.transaction.util.CheckPermissionUtil.checkPerActionVoid(CheckPermissionUtil.java:141)
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.lambda$35(TransactionOldServiceImpl.java:3368)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondComplete(MonoFlatMap.java:245)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onNext(MonoFlatMap.java:305)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
	at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
	at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-21 11:05:52.693 [reactor-http-epoll-4] INFO  v.o.t.util.CheckPermissionUtil - checkActionRefund paymentMethod invalid, transactionId=*********, transactionType=Authorize
2025-08-21 11:07:18.617 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 2162757 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-08-21 11:07:18.619 [main] INFO  v.o.t.TransactionAppApplication - The following 1 profile is active: "dev"
2025-08-21 11:07:19.426 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-08-21 11:07:19.609 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 174 ms. Found 2 R2DBC repository interfaces.
2025-08-21 11:07:21.179 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Starting...
2025-08-21 11:07:21.476 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleMerchantPortalHikariPool - Added connection oracle.jdbc.driver.T4CConnection@58aa5c94
2025-08-21 11:07:21.478 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Start completed.
2025-08-21 11:07:21.561 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Starting...
2025-08-21 11:07:21.667 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleOneReportHikariPool - Added connection oracle.jdbc.driver.T4CConnection@6ec3d8e4
2025-08-21 11:07:21.667 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Start completed.
2025-08-21 11:07:21.787 [main] INFO  v.o.t.job.PromotionReportJob - Scheduling PromotionReportJob with cron: 0 05 16 * * *
2025-08-21 11:07:21.800 [main] INFO  v.o.t.job.PromotionReportJob - End PromotionReportJob!
2025-08-21 11:07:21.806 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-08-21 11:07:21.807 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-08-21 11:07:21.812 [main] INFO  v.o.transaction.job.UposReportJob - Scheduling UposReportJob with cron: 0 10 16 * * *
2025-08-21 11:07:21.813 [main] INFO  v.o.transaction.job.UposReportJob - End UposReportJob!
2025-08-21 11:07:22.706 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 8396 (http)
2025-08-21 11:07:22.903 [main] INFO  v.o.t.TransactionAppApplication - Started TransactionAppApplication in 4.988 seconds (process running for 6.7)
2025-08-21 11:08:29.415 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: GET http://localhost:8396/ma-service/api/v1/transactions/transaction/detail?transactionId=*********&transactionType=Authorize&paymentMethod=QT
2025-08-21 11:08:29.415 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-08-21 11:08:29.416 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-08-21 11:08:29.416 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-08-21 11:08:29.416 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-08-21 11:08:29.416 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-08-21 11:08:29.416 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-08-21 11:08:29.416 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/mav2-main/
2025-08-21 11:08:29.416 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-08-21 11:08:29.416 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-08-21 11:08:29.416 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-08-21 11:08:29.416 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-08-21 11:08:29.416 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-08-21 11:08:29.416 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-08-21 11:08:29.417 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-08-21 11:08:29.417 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 417604
2025-08-21 11:08:29.417 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; JSESSIONID=dummy; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ1ODc1NjIsInVzZXIiOnsiZW1haWwiOiJhZG1pbkBvbmVwYXkudm4iLCJmdWxsX25hbWUiOiJBZG1pbiAxIiwiaWQiOiI1MUREN0M5QzdEQTZBMjc4OEY0QUI1M0I1NkJFNjgxMSIsImpvYl90aXRsZSI6IkFkbWluIGFsw7QyMjIiLCJwaG9uZSI6IjA5MDEyMzQ1NjEifX0.hEjfsrAg9gqUPMiXcjcugUJwwz7RVYeT8g3lnbyOc2Y
2025-08-21 11:08:29.417 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 11:08:29.417 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = bcc7ce7b-c6d3-4205-8b08-b2172636bde2
2025-08-21 11:08:29.417 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8396
2025-08-21 11:08:29.417 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-08-21 11:08:29.421 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-08-21 11:08:29.471 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=********* ,paymentMethod=QT ,transactionType=Authorize ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 11:08:29.472 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-08-21 11:08:29.552 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/********* ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 11:08:29.638 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"transaction_id":"*********","original_transaction_id":"*********","transaction_type":"Authorize","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"17","acquirer_name":"","acquirer_short_name":""},"transaction_reference":"TEST_1755506112099348164310","amount":{"total":12000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"340353****0900","card_type":"Amex","name_on_card":"NGUYEN VAN A","card_date":{"month":"12","year":"28"},"commercical_card":"888","commercial_card_indicator":"3"},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y","authentication_type":"Amex Safekey","authorization_code":"013834"},"transaction_time":"2025-08-18T15:35:24Z","transaction_status":"0","transaction_ref_number":"*********","ip_address":"************","ip_proxy":"N","bin_country":"UNITED STATES","csc_result_code":"","verification_security_level":"","response_code":"0","can_void":true,"risk_assesment":"Review Required","bank_id":"AMERICAN EXPRESS US CONSUMER","wait_for_approval_amount":"0.00","advance_status":"Successful","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"CREDIT","acqResponseCode":"","status3ds":"Y","riskOverAllResult":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"NGUYEN VAN A","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00","installment_statement_date":"","reasonName":""}
2025-08-21 11:08:29.638 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-08-21 11:08:29.654 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-08-21 11:08:29.664 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-08-21 11:08:29.684 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-08-18T15:35:24, orderAmount=12000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=*********, parentTransactionId=*********, transactionType=Authorize, paymentMethod=QT, transactionCreatedTime=2025-08-18T15:35:24, transactionCompletedTime=2025-08-18T15:35:24, transactionAmount=12000.0, transactionCurrency=VND, transactionStatus=Successful, responseCode=0, merchantTransRef=TEST_1755506112099348164310, parentMerchantTransRef=null, paymentChannel=null, installmentStatus=, operator=null), paymentMethod=PaymentMethod(cardNumber=340353****0900, cardBrand=null, cardType=Amex, issuer=, acquirer=17, cardExpiry=12/28, nameOnCard=NGUYEN VAN A, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, settlementAmount=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=InstallmentDetail(priceDifference=0, period=, amountMonthly=0.00), promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=************, ipProxy=N, ipCountry=************, binCountry=UNITED STATES, riskAssessment=Review Required), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null, amountAdvance=null), actions=null, histories=null)
2025-08-21 11:08:29.684 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-08-21 11:08:29.684 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getAdvanceFeeInfo ===================
2025-08-21 11:08:29.876 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ==================== Start getMerchantById ====================
2025-08-21 11:08:29.886 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getParentMerchantTransRef ===================
2025-08-21 11:08:29.909 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getPromotionInfo ===================
2025-08-21 11:08:29.914 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call getTransactionHistory-----------
2025-08-21 11:08:29.915 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: originalTransactionId=********* ,paymentMethod=QT ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 11:08:29.915 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionHistory MA-----------
2025-08-21 11:08:29.916 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/*********/history ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 11:08:29.925 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION HISTORY API: response code=200 ,response body={"transactions":[{"transaction_id":"*********","original_transaction_id":"*********","response_code":"0","reference_number":"*********","merchant_transaction_ref":"TEST_1755506112099348164310","transaction_type":"Authorize","amount":{"total":12000.0,"currency":"VND"},"status":"0","operator_id":"","merchant_id":"TESTPCI","transaction_time":"2025-08-18T15:35:24Z","advance_status":"Successful","financial_transaction_id":"*********","note":"","parent_id":"*********","dadId":"","subHistories":""}]}
2025-08-21 11:08:29.925 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionHistory MA------------
2025-08-21 11:08:29.925 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION HISTORY INTERNATIONAL: {"transactions":[{"transaction_id":"*********","original_transaction_id":"*********","response_code":"0","reference_number":"*********","merchant_transaction_ref":"TEST_1755506112099348164310","transaction_type":"Authorize","amount":{"total":12000.0,"currency":"VND"},"status":"0","operator_id":"","merchant_id":"TESTPCI","transaction_time":"2025-08-18T15:35:24Z","advance_status":"Successful","financial_transaction_id":"*********","note":"","parent_id":"*********","dadId":"","subHistories":""}]}
2025-08-21 11:08:29.929 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call getTransactionHistory------------
2025-08-21 11:08:29.929 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-08-21 11:08:29.930 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-08-21 11:08:30.199 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-08-21 11:08:30.200 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - list merchant from permission: [OP_TESTVND, OP_VND01, OP_MPGSVND, OP_MPGSUSD, OP_MPAYVND3D, TESTBIDVV3, ONEPAYHM, D_TEST, OP_CYBSVND, TEST3DSMPGS, OP_MPAYVN1, TESTND, OP_TESTK7011, TESTHD3BEN, TESTSTATICQR, OP_TESTK4121, OP_SACOMTH, TESTAPLVNC, TESTORIFLCBS, TESTVCB_5411, OP_TESTK7399, TESTGGPAY, OP_TESTK5331, TESTKOVENA, TESTSTGINVOICE, TESTMERCHANT16KY, TESTOPVPB, TESTVTBCYBER, HUONG1, TESTOPBIDV, TESTTRAGOP, TESTPCI, TESTONEPAY, OP_TESTUSD, OP_SACOMTRVV, ONEPAY, INVOICETG, TESTDMX, TESTTNS, TESTINVOICE, TESTVCB3D2, TESTBIDV, TESTHC, TEST3DS2_LT, MIGS3DS, TESTAXA1, TESTVF, TESTUPOSTG, TESTAOSVNBANK, OP_TESTKBANK, TESTAPPLE, TESTVCBHM1, TESTTVLOKA, TESTVCB_7399, TESTVAGROUP, TESTVCB_6300, OP_TESTM8211, OP_TESTK7372, OP_TESTK7991, TESTPCI4, TESTOPVCB2, TESTSACOMCYBER, TESTUSD, OP_TESTVP5732, OP_TESTVP6300, ONEPAYMT, TESTATM, OPTEST, TESTTNSUSD, MPAYVND, TESTBIDV1, OP_VPBMPGS3, TESTAWPOSTG, TESTTRAGOP1, TESTSAMSUNG, TESTOP_VTB2, TESTUPOSKBANK, TESTAPLBANK, TESTVCB_8211, TESTMERCHANTID20KYTU, MONPAYOUT, TESTVIETQR, TESTINVOICETG, OP_TESTVP8211, TESTPAYOUT2, TEST, OP_SACOMCBSU, OP_VPBMPGS1, TESTONEPAY20, TESTVJU, OP_TESTCONFIG, OP_TESTSS, OP_TESTBHX, OPTESTHB, TESTAWPOS, TESTACVNC, OP_TESTK4900, TESTONEPAYTHB, ONEPAY_REFUND, TESTAPLTG, OP_TESTK5814, TESTVCB_4722, OP_TESTK5722, TESTPAYOUT1, TESTOPVCB, TESTIDTEST4, TESTONEPAYDVC, OP_SACOMCBSV, OP_SACOMTRVU, ****************, OPMPAY, FALSE, TESTDUONG, OP_VPBMPGS2, TESTSACOMCBU, TESTLZD, TESTSAPO, OP_TESTAUTH, TESTMEGA1, OP_TESTMID, TESTVTASABRE, OP_TESTK4722, TESTOP_VTB3, OP_SACOMVIN, OP_TESTK8220, TESTCNY, TESTVCB_4900, OP_TESTK5698, TESTPCI3, ANHTVTEST, TESTTOKEN, TESTONEPAY2, MONPAYCOLLECT, OP_TESTVP5411, TESTSHOPIFYTG, TESTOP_D, TESTONEPAYDVTT, TESTTRAGOP2, TESTBIDVU3, TESTBIDVV2, TESTTOKENUSD, TESTMEGA, TEST3DS2_MK, TEST3DS2_TN, TESTVFVND, TESTVTB3D2, TESTENETVIET, TESTSHOPIFY, TESTAPPLEND, TESTVCBHM2, TESTTRAGOP3, TESTOKENOP, TESTKONEVA, TESTOP_VTB1, AUTOAPLND, TESTSSPAY, TESTSAMSUNGPL, TESTVCB_5732, TESTAES, OP_TESTK7832, TESTPCI2, TESTJBAUSD, TESTVCBCYBER, TESTMERCHANT15K, OPPREPAID, TESTONEPAYUSD, OP_MPAYVND, TESTSACOMCBV, OP_VCBVND, TESTSAPO2, TESTBIDVCBSU, TESTVCB3D2O, TESTMAFC, OP_TESTOP20, TESTAXA, TESTTGDD3D2, TESTPR, TESTHC1, ONEPAYHB, TESTSSBNPL, OP_TESTK8299, TESTHD2BEN, TESTPCIEM, TESTMCD, OP_TESTK5732, TESTPROMMG, OP_TESTTT, TESTAUTOMSP, OP_TESTUSD2, OP_TESTAPPAY, TESTTHB, TESTSOS, TESTAPPLETG, OP_TESTK5977, EKKO1, TESTONEPAY1, TESTIDTEST6, TESTONEPAYDVDT, TESTBIDVCSU2, ****************, OP_VCBUSD, TESTBIDVCBSV, OP_TEST3DS, OP_TEST3DS2V, OP_CYBSUSD, TESTTHSC, TESTBNPL, TESTAOSVNC, TESTPROM, OP_SACOMECOM, TESTQRTINH, TESTTOKENDV, TESTUPOS, OP_TESTK5969, OP_TESTK5816, TESTONEPAY3, TESTPCI1, TESTKBANKCYBER, TESTVPBCYBER, TEST_OP2B, OP_TESTVP4900, TESTIDTEST3, TESTPAYPAL]
2025-08-21 11:08:30.202 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-08-21 11:08:30.203 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-08-21 11:08:30.204 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start checkPerAndActionByUserId-----------
2025-08-21 11:08:30.247 [reactor-http-epoll-3] INFO  v.o.t.repository.AcquirerRepository - Starting getListAcquirer - calling function ONEDATA.GET_LIST_ACQUIRER
2025-08-21 11:09:07.150 [reactor-http-epoll-3] ERROR v.o.t.util.CheckPermissionUtil - Error parsing date: 2025-08-18T15:35:24
java.time.temporal.UnsupportedTemporalTypeException: Unsupported field: HourOfDay
	at java.base/java.time.LocalDate.get0(LocalDate.java:698)
	at java.base/java.time.LocalDate.getLong(LocalDate.java:678)
	at java.base/java.time.format.DateTimePrintContext.getValue(DateTimePrintContext.java:308)
	at java.base/java.time.format.DateTimeFormatterBuilder$NumberPrinterParser.format(DateTimeFormatterBuilder.java:2914)
	at java.base/java.time.format.DateTimeFormatterBuilder$CompositePrinterParser.format(DateTimeFormatterBuilder.java:2529)
	at java.base/java.time.format.DateTimeFormatter.formatTo(DateTimeFormatter.java:1905)
	at java.base/java.time.format.DateTimeFormatter.format(DateTimeFormatter.java:1879)
	at java.base/java.time.LocalDate.format(LocalDate.java:1797)
	at vn.onepay.transaction.util.CheckPermissionUtil.checkDeadline(CheckPermissionUtil.java:950)
	at vn.onepay.transaction.util.CheckPermissionUtil.checkVoidAuthorizeQT(CheckPermissionUtil.java:540)
	at vn.onepay.transaction.util.CheckPermissionUtil.checkActionVoidAuthorize(CheckPermissionUtil.java:511)
	at vn.onepay.transaction.util.CheckPermissionUtil.checkPerActionVoid(CheckPermissionUtil.java:141)
	at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.lambda$35(TransactionOldServiceImpl.java:3368)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondComplete(MonoFlatMap.java:245)
	at reactor.core.publisher.MonoFlatMap$FlatMapInner.onNext(MonoFlatMap.java:305)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:539)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
	at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
	at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-21 11:09:07.153 [reactor-http-epoll-3] INFO  v.o.t.util.CheckPermissionUtil - checkActionRefund paymentMethod invalid, transactionId=*********, transactionType=Authorize
2025-08-21 11:11:10.711 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 2164213 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-08-21 11:11:10.714 [main] INFO  v.o.t.TransactionAppApplication - The following 1 profile is active: "dev"
2025-08-21 11:11:11.454 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-08-21 11:11:11.624 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 163 ms. Found 2 R2DBC repository interfaces.
2025-08-21 11:11:13.190 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Starting...
2025-08-21 11:11:13.482 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleMerchantPortalHikariPool - Added connection oracle.jdbc.driver.T4CConnection@58aa5c94
2025-08-21 11:11:13.484 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Start completed.
2025-08-21 11:11:13.553 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Starting...
2025-08-21 11:11:13.667 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleOneReportHikariPool - Added connection oracle.jdbc.driver.T4CConnection@6ec3d8e4
2025-08-21 11:11:13.667 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Start completed.
2025-08-21 11:11:13.761 [main] INFO  v.o.t.job.PromotionReportJob - Scheduling PromotionReportJob with cron: 0 05 16 * * *
2025-08-21 11:11:13.768 [main] INFO  v.o.t.job.PromotionReportJob - End PromotionReportJob!
2025-08-21 11:11:13.770 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-08-21 11:11:13.771 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-08-21 11:11:13.774 [main] INFO  v.o.transaction.job.UposReportJob - Scheduling UposReportJob with cron: 0 10 16 * * *
2025-08-21 11:11:13.775 [main] INFO  v.o.transaction.job.UposReportJob - End UposReportJob!
2025-08-21 11:11:14.619 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 8396 (http)
2025-08-21 11:11:14.810 [main] INFO  v.o.t.TransactionAppApplication - Started TransactionAppApplication in 4.746 seconds (process running for 6.734)
2025-08-21 11:11:19.062 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: GET http://localhost:8396/ma-service/api/v1/transactions/transaction/detail?transactionId=*********&transactionType=Authorize&paymentMethod=QT
2025-08-21 11:11:19.063 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-08-21 11:11:19.063 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-08-21 11:11:19.063 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-08-21 11:11:19.063 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-08-21 11:11:19.063 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-08-21 11:11:19.063 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-08-21 11:11:19.063 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/mav2-main/
2025-08-21 11:11:19.064 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-08-21 11:11:19.064 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-08-21 11:11:19.064 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-08-21 11:11:19.064 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-08-21 11:11:19.064 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-08-21 11:11:19.064 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-08-21 11:11:19.064 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-08-21 11:11:19.064 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 417604
2025-08-21 11:11:19.064 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; JSESSIONID=dummy; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ1ODc1NjIsInVzZXIiOnsiZW1haWwiOiJhZG1pbkBvbmVwYXkudm4iLCJmdWxsX25hbWUiOiJBZG1pbiAxIiwiaWQiOiI1MUREN0M5QzdEQTZBMjc4OEY0QUI1M0I1NkJFNjgxMSIsImpvYl90aXRsZSI6IkFkbWluIGFsw7QyMjIiLCJwaG9uZSI6IjA5MDEyMzQ1NjEifX0.hEjfsrAg9gqUPMiXcjcugUJwwz7RVYeT8g3lnbyOc2Y
2025-08-21 11:11:19.064 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 11:11:19.064 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = c87b6466-c456-416d-acc3-34b1d38b1ab0
2025-08-21 11:11:19.064 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8396
2025-08-21 11:11:19.064 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-08-21 11:11:19.068 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-08-21 11:11:19.114 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=********* ,paymentMethod=QT ,transactionType=Authorize ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 11:11:19.115 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-08-21 11:11:19.177 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/********* ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 11:11:19.281 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"transaction_id":"*********","original_transaction_id":"*********","transaction_type":"Authorize","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"17","acquirer_name":"","acquirer_short_name":""},"transaction_reference":"TEST_1755506112099348164310","amount":{"total":12000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"340353****0900","card_type":"Amex","name_on_card":"NGUYEN VAN A","card_date":{"month":"12","year":"28"},"commercical_card":"888","commercial_card_indicator":"3"},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y","authentication_type":"Amex Safekey","authorization_code":"013834"},"transaction_time":"2025-08-18T15:35:24Z","transaction_status":"0","transaction_ref_number":"*********","ip_address":"************","ip_proxy":"N","bin_country":"UNITED STATES","csc_result_code":"","verification_security_level":"","response_code":"0","can_void":true,"risk_assesment":"Review Required","bank_id":"AMERICAN EXPRESS US CONSUMER","wait_for_approval_amount":"0.00","advance_status":"Successful","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"CREDIT","acqResponseCode":"","status3ds":"Y","riskOverAllResult":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"NGUYEN VAN A","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00","installment_statement_date":"","reasonName":""}
2025-08-21 11:11:19.281 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-08-21 11:11:19.298 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-08-21 11:11:19.307 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-08-21 11:11:19.330 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-08-18T15:35:24, orderAmount=12000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=*********, parentTransactionId=*********, transactionType=Authorize, paymentMethod=QT, transactionCreatedTime=2025-08-18T15:35:24, transactionCompletedTime=2025-08-18T15:35:24, transactionAmount=12000.0, transactionCurrency=VND, transactionStatus=Successful, responseCode=0, merchantTransRef=TEST_1755506112099348164310, parentMerchantTransRef=null, paymentChannel=null, installmentStatus=, operator=null), paymentMethod=PaymentMethod(cardNumber=340353****0900, cardBrand=null, cardType=Amex, issuer=, acquirer=17, cardExpiry=12/28, nameOnCard=NGUYEN VAN A, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, settlementAmount=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=InstallmentDetail(priceDifference=0, period=, amountMonthly=0.00), promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=************, ipProxy=N, ipCountry=************, binCountry=UNITED STATES, riskAssessment=Review Required), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null, amountAdvance=null), actions=null, histories=null)
2025-08-21 11:11:19.330 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-08-21 11:11:19.330 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getAdvanceFeeInfo ===================
2025-08-21 11:11:19.612 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ==================== Start getMerchantById ====================
2025-08-21 11:11:19.625 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getParentMerchantTransRef ===================
2025-08-21 11:11:19.657 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getPromotionInfo ===================
2025-08-21 11:11:19.663 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call getTransactionHistory-----------
2025-08-21 11:11:19.664 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: originalTransactionId=********* ,paymentMethod=QT ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 11:11:19.664 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionHistory MA-----------
2025-08-21 11:11:19.665 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/*********/history ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 11:11:19.675 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION HISTORY API: response code=200 ,response body={"transactions":[{"transaction_id":"*********","original_transaction_id":"*********","response_code":"0","reference_number":"*********","merchant_transaction_ref":"TEST_1755506112099348164310","transaction_type":"Authorize","amount":{"total":12000.0,"currency":"VND"},"status":"0","operator_id":"","merchant_id":"TESTPCI","transaction_time":"2025-08-18T15:35:24Z","advance_status":"Successful","financial_transaction_id":"*********","note":"","parent_id":"*********","dadId":"","subHistories":""}]}
2025-08-21 11:11:19.675 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionHistory MA------------
2025-08-21 11:11:19.675 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION HISTORY INTERNATIONAL: {"transactions":[{"transaction_id":"*********","original_transaction_id":"*********","response_code":"0","reference_number":"*********","merchant_transaction_ref":"TEST_1755506112099348164310","transaction_type":"Authorize","amount":{"total":12000.0,"currency":"VND"},"status":"0","operator_id":"","merchant_id":"TESTPCI","transaction_time":"2025-08-18T15:35:24Z","advance_status":"Successful","financial_transaction_id":"*********","note":"","parent_id":"*********","dadId":"","subHistories":""}]}
2025-08-21 11:11:19.681 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call getTransactionHistory------------
2025-08-21 11:11:19.681 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-08-21 11:11:19.682 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-08-21 11:11:19.939 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-08-21 11:11:19.939 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - list merchant from permission: [OP_TESTVND, OP_VND01, OP_MPGSVND, OP_MPGSUSD, OP_MPAYVND3D, TESTBIDVV3, ONEPAYHM, D_TEST, OP_CYBSVND, TEST3DSMPGS, OP_MPAYVN1, TESTND, OP_TESTK7011, TESTHD3BEN, TESTSTATICQR, OP_TESTK4121, OP_SACOMTH, TESTAPLVNC, TESTORIFLCBS, TESTVCB_5411, OP_TESTK7399, TESTGGPAY, OP_TESTK5331, TESTKOVENA, TESTSTGINVOICE, TESTMERCHANT16KY, TESTOPVPB, TESTVTBCYBER, HUONG1, TESTOPBIDV, TESTTRAGOP, TESTPCI, TESTONEPAY, OP_TESTUSD, OP_SACOMTRVV, ONEPAY, INVOICETG, TESTDMX, TESTTNS, TESTINVOICE, TESTVCB3D2, TESTBIDV, TESTHC, TEST3DS2_LT, MIGS3DS, TESTAXA1, TESTVF, TESTUPOSTG, TESTAOSVNBANK, OP_TESTKBANK, TESTAPPLE, TESTVCBHM1, TESTTVLOKA, TESTVCB_7399, TESTVAGROUP, TESTVCB_6300, OP_TESTM8211, OP_TESTK7372, OP_TESTK7991, TESTPCI4, TESTOPVCB2, TESTSACOMCYBER, TESTUSD, OP_TESTVP5732, OP_TESTVP6300, ONEPAYMT, TESTATM, OPTEST, TESTTNSUSD, MPAYVND, TESTBIDV1, OP_VPBMPGS3, TESTAWPOSTG, TESTTRAGOP1, TESTSAMSUNG, TESTOP_VTB2, TESTUPOSKBANK, TESTAPLBANK, TESTVCB_8211, TESTMERCHANTID20KYTU, MONPAYOUT, TESTVIETQR, TESTINVOICETG, OP_TESTVP8211, TESTPAYOUT2, TEST, OP_SACOMCBSU, OP_VPBMPGS1, TESTONEPAY20, TESTVJU, OP_TESTCONFIG, OP_TESTSS, OP_TESTBHX, OPTESTHB, TESTAWPOS, TESTACVNC, OP_TESTK4900, TESTONEPAYTHB, ONEPAY_REFUND, TESTAPLTG, OP_TESTK5814, TESTVCB_4722, OP_TESTK5722, TESTPAYOUT1, TESTOPVCB, TESTIDTEST4, TESTONEPAYDVC, OP_SACOMCBSV, OP_SACOMTRVU, ****************, OPMPAY, FALSE, TESTDUONG, OP_VPBMPGS2, TESTSACOMCBU, TESTLZD, TESTSAPO, OP_TESTAUTH, TESTMEGA1, OP_TESTMID, TESTVTASABRE, OP_TESTK4722, TESTOP_VTB3, OP_SACOMVIN, OP_TESTK8220, TESTCNY, TESTVCB_4900, OP_TESTK5698, TESTPCI3, ANHTVTEST, TESTTOKEN, TESTONEPAY2, MONPAYCOLLECT, OP_TESTVP5411, TESTSHOPIFYTG, TESTOP_D, TESTONEPAYDVTT, TESTTRAGOP2, TESTBIDVU3, TESTBIDVV2, TESTTOKENUSD, TESTMEGA, TEST3DS2_MK, TEST3DS2_TN, TESTVFVND, TESTVTB3D2, TESTENETVIET, TESTSHOPIFY, TESTAPPLEND, TESTVCBHM2, TESTTRAGOP3, TESTOKENOP, TESTKONEVA, TESTOP_VTB1, AUTOAPLND, TESTSSPAY, TESTSAMSUNGPL, TESTVCB_5732, TESTAES, OP_TESTK7832, TESTPCI2, TESTJBAUSD, TESTVCBCYBER, TESTMERCHANT15K, OPPREPAID, TESTONEPAYUSD, OP_MPAYVND, TESTSACOMCBV, OP_VCBVND, TESTSAPO2, TESTBIDVCBSU, TESTVCB3D2O, TESTMAFC, OP_TESTOP20, TESTAXA, TESTTGDD3D2, TESTPR, TESTHC1, ONEPAYHB, TESTSSBNPL, OP_TESTK8299, TESTHD2BEN, TESTPCIEM, TESTMCD, OP_TESTK5732, TESTPROMMG, OP_TESTTT, TESTAUTOMSP, OP_TESTUSD2, OP_TESTAPPAY, TESTTHB, TESTSOS, TESTAPPLETG, OP_TESTK5977, EKKO1, TESTONEPAY1, TESTIDTEST6, TESTONEPAYDVDT, TESTBIDVCSU2, ****************, OP_VCBUSD, TESTBIDVCBSV, OP_TEST3DS, OP_TEST3DS2V, OP_CYBSUSD, TESTTHSC, TESTBNPL, TESTAOSVNC, TESTPROM, OP_SACOMECOM, TESTQRTINH, TESTTOKENDV, TESTUPOS, OP_TESTK5969, OP_TESTK5816, TESTONEPAY3, TESTPCI1, TESTKBANKCYBER, TESTVPBCYBER, TEST_OP2B, OP_TESTVP4900, TESTIDTEST3, TESTPAYPAL]
2025-08-21 11:11:19.942 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-08-21 11:11:19.943 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-08-21 11:11:19.943 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start checkPerAndActionByUserId-----------
2025-08-21 11:11:19.987 [reactor-http-epoll-3] INFO  v.o.t.repository.AcquirerRepository - Starting getListAcquirer - calling function ONEDATA.GET_LIST_ACQUIRER
2025-08-21 11:11:20.035 [reactor-http-epoll-3] ERROR v.o.t.exception.RequestLoggingFilter - Request error: GET http://localhost:8396/ma-service/api/v1/transactions/transaction/detail?transactionId=*********&transactionType=Authorize&paymentMethod=QT - Exception: Unresolved compilation problem: 
	Cannot make a static reference to the non-static field internationalAuthorizeDeadline

java.lang.Error: Unresolved compilation problem: 
	Cannot make a static reference to the non-static field internationalAuthorizeDeadline

	at vn.onepay.transaction.util.CheckPermissionUtil.checkVoidAuthorizeQT(CheckPermissionUtil.java:540)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#getTransactionDetail(ServerWebExchange, String, String, String, String) [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x00007febb86068e8 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
Original Stack Trace:
		at vn.onepay.transaction.util.CheckPermissionUtil.checkVoidAuthorizeQT(CheckPermissionUtil.java:540)
		at vn.onepay.transaction.util.CheckPermissionUtil.checkActionVoidAuthorize(CheckPermissionUtil.java:511)
		at vn.onepay.transaction.util.CheckPermissionUtil.checkPerActionVoid(CheckPermissionUtil.java:141)
		at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.lambda$35(TransactionOldServiceImpl.java:3368)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondComplete(MonoFlatMap.java:245)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onNext(MonoFlatMap.java:305)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
		at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
		at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
		at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
		at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-21 11:11:20.052 [reactor-http-epoll-3] ERROR o.s.b.a.w.r.e.AbstractErrorWebExceptionHandler - [77ebc004-1]  500 Server Error for HTTP GET "/transaction/detail?transactionId=*********&transactionType=Authorize&paymentMethod=QT"
java.lang.Error: Unresolved compilation problem: 
	Cannot make a static reference to the non-static field internationalAuthorizeDeadline

	at vn.onepay.transaction.util.CheckPermissionUtil.checkVoidAuthorizeQT(CheckPermissionUtil.java:540)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#getTransactionDetail(ServerWebExchange, String, String, String, String) [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x00007febb86068e8 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.RequestLoggingFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ HTTP GET "/ma-service/api/v1/transactions/transaction/detail?transactionId=*********&transactionType=Authorize&paymentMethod=QT" [ExceptionHandlingWebHandler]
Original Stack Trace:
		at vn.onepay.transaction.util.CheckPermissionUtil.checkVoidAuthorizeQT(CheckPermissionUtil.java:540)
		at vn.onepay.transaction.util.CheckPermissionUtil.checkActionVoidAuthorize(CheckPermissionUtil.java:511)
		at vn.onepay.transaction.util.CheckPermissionUtil.checkPerActionVoid(CheckPermissionUtil.java:141)
		at vn.onepay.transaction.service.impl.TransactionOldServiceImpl.lambda$35(TransactionOldServiceImpl.java:3368)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.secondComplete(MonoFlatMap.java:245)
		at reactor.core.publisher.MonoFlatMap$FlatMapInner.onNext(MonoFlatMap.java:305)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)
		at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)
		at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)
		at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)
		at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)
		at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)
		at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)
		at reactor.netty.channel.ChannelOperations.terminate(ChannelOperations.java:509)
		at reactor.netty.http.client.HttpClientOperations.onInboundNext(HttpClientOperations.java:824)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:107)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)
		at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)
		at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-21 12:03:40.675 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:03:40.675 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:03:40.675 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:03:40.814 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:03:40.814 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:03:40.814 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:03:41.588 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:03:41.588 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:03:41.589 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:03:41.803 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:03:41.803 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:03:41.803 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:03:42.491 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:03:42.492 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:03:42.492 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:03:42.839 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:03:42.839 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:03:42.839 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:03:43.395 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:03:43.395 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:03:43.395 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:03:43.840 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:03:43.840 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:03:43.840 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:03:44.444 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:03:44.444 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:03:44.445 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:03:44.840 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:03:44.841 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:03:44.841 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:03:45.371 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:03:45.371 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:03:45.371 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:03:45.818 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:03:45.819 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:03:45.819 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:03:46.374 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:03:46.374 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:03:46.374 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:03:46.843 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:03:46.844 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:03:46.844 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:03:47.327 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:03:47.327 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:03:47.327 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:03:47.843 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:03:47.844 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:03:47.844 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:03:48.377 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:03:48.377 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:03:48.377 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:03:48.844 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:03:48.844 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:03:48.844 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:03:49.378 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:03:49.378 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:03:49.378 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:03:49.848 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:03:49.848 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:03:49.848 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:03:50.388 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:03:50.389 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:03:50.389 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:03:50.848 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:03:50.849 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:03:50.849 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:03:51.389 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:03:51.389 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:03:51.389 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:03:51.848 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:03:51.849 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:03:51.849 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:03:52.392 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:03:52.392 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:03:52.392 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:03:52.849 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:03:52.849 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:03:52.850 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:03:53.393 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:03:53.393 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:03:53.393 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:03:53.803 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:03:53.803 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:03:53.803 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:03:54.394 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:03:54.394 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:03:54.394 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:03:54.756 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:03:54.756 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:03:54.757 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:03:55.356 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:03:55.356 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:03:55.356 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:03:55.756 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:03:55.757 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:03:55.757 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:03:56.398 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:03:56.398 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:03:56.398 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:03:56.660 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:03:56.660 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:03:56.660 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:03:57.444 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:03:57.445 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:03:57.445 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:03:57.703 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:03:57.703 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:03:57.703 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:03:58.448 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:03:58.448 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:03:58.448 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:03:58.657 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:03:58.657 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:03:58.657 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:03:59.351 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:03:59.352 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:03:59.352 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:03:59.656 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:03:59.656 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:03:59.656 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:00.371 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:00.371 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:00.371 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:00.662 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:00.663 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:00.663 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:01.371 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:01.371 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:01.372 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:01.656 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:01.656 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:01.656 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:02.341 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:02.341 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:02.341 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:02.610 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:02.610 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:02.610 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:03.374 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:03.374 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:03.374 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:03.613 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:03.613 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:03.613 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:04.374 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:04.374 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:04.375 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:04.516 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:04.516 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:04.516 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:05.183 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:05.183 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:05.184 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:05.564 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:05.564 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:05.564 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:06.186 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:06.187 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:06.187 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:06.565 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:06.566 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:06.566 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:07.089 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:07.090 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:07.090 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:07.566 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:07.566 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:07.566 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:08.089 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:08.090 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:08.090 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:08.566 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:08.567 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:08.567 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:09.042 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:09.042 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:09.042 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:09.419 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:09.420 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:09.420 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:10.073 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:10.074 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:10.074 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:10.464 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:10.465 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:10.465 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:11.073 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:11.074 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:11.074 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:11.466 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:11.467 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:11.467 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:12.085 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:12.085 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:12.085 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:12.467 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:12.467 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:12.467 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:13.037 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:13.038 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:13.038 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:13.477 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:13.477 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:13.477 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:13.890 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:13.890 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:13.890 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:14.478 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:14.478 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:14.478 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:14.789 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:14.790 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:14.790 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:15.320 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:15.320 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:15.320 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:15.831 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:15.832 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:15.832 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:16.173 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:16.173 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:16.173 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:16.814 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:16.815 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:16.815 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:17.077 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:17.077 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:17.077 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:17.834 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:17.834 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:17.834 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:18.078 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:18.078 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:18.078 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:18.686 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:18.687 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:18.687 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:19.091 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:19.091 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:19.092 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:19.539 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:19.539 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:19.539 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:20.092 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:20.092 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:20.092 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:20.540 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:20.540 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:20.540 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:20.995 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:20.995 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:20.995 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:21.541 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:21.541 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:21.541 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:21.998 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:21.999 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:21.999 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:22.541 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:22.541 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:22.541 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:22.852 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:22.852 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:22.852 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:23.394 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:23.394 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:23.394 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:23.902 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:23.902 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:23.902 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:24.370 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:24.370 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:24.370 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:24.805 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:24.805 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:24.805 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:25.371 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:25.371 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:25.371 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:25.783 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:25.783 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:25.783 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:26.377 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:26.378 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:26.378 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:26.784 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:26.784 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:26.784 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:27.398 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:27.399 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:27.399 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:27.784 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:27.785 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:27.785 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:28.401 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:28.401 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:28.401 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:28.765 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:28.765 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:28.765 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:29.437 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:29.437 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:29.437 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:29.766 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:29.766 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:29.766 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:30.281 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:30.282 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:30.282 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:30.767 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:30.767 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:30.767 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:31.289 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:31.289 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:31.290 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:31.720 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:31.721 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:31.721 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:32.292 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:32.292 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:32.292 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:32.721 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:32.721 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:32.721 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:33.211 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:33.211 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:33.211 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:33.725 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:33.725 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:33.725 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:34.212 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:34.212 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:34.212 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:34.628 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:34.628 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:34.628 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:35.262 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:35.262 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:35.262 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:35.588 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:35.588 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:35.588 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:36.164 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:36.164 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:36.164 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:36.591 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:36.591 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:36.591 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:37.117 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:37.117 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:37.117 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:37.393 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:37.394 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:37.394 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:38.118 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:38.118 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:38.118 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:38.393 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:38.394 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:38.394 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:39.075 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:39.075 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:39.075 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:39.246 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:39.246 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:39.246 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:40.060 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:40.060 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:40.060 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:40.259 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:40.259 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:40.259 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:41.047 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:41.048 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:41.048 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:41.260 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:41.260 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:41.260 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:42.047 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:42.047 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:42.047 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:42.261 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:42.261 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:42.261 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:42.950 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:42.950 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:42.950 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:43.262 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:43.262 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:43.262 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:43.852 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:43.853 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:43.853 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:44.262 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:44.263 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:44.263 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:44.884 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:44.884 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:44.884 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:45.262 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:45.263 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:45.263 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:45.915 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:45.916 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:45.916 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:46.281 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:46.281 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:46.281 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:46.915 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:46.916 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:46.916 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:47.184 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:04:47.184 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:47.184 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Bootstrap broker localhost:9092 (id: -1 rack: null) disconnected
2025-08-21 12:04:47.818 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Node -1 disconnected.
2025-08-21 12:14:44.692 [main] INFO  vn.onepay.transaction.MainTest - Starting MainTest using Java 21.0.6 with PID 2187763 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-08-21 12:14:44.695 [main] INFO  vn.onepay.transaction.MainTest - No active profile set, falling back to 1 default profile: "default"
2025-08-21 12:14:44.789 [main] WARN  o.s.b.w.r.c.AnnotationConfigReactiveWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Unable to start reactive web server
2025-08-21 12:14:44.843 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web application could not be started as there was no org.springframework.boot.web.reactive.server.ReactiveWebServerFactory bean defined in the context.

Action:

Check your application's dependencies for a supported reactive web server.
Check the configured web application type.

2025-08-21 12:29:43.809 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 2195540 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-08-21 12:29:43.812 [main] INFO  v.o.t.TransactionAppApplication - The following 1 profile is active: "dev"
2025-08-21 12:29:44.566 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-08-21 12:29:44.739 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 165 ms. Found 2 R2DBC repository interfaces.
2025-08-21 12:29:46.284 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Starting...
2025-08-21 12:29:46.563 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleMerchantPortalHikariPool - Added connection oracle.jdbc.driver.T4CConnection@46702c26
2025-08-21 12:29:46.565 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Start completed.
2025-08-21 12:29:46.643 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Starting...
2025-08-21 12:29:46.768 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleOneReportHikariPool - Added connection oracle.jdbc.driver.T4CConnection@146f3d22
2025-08-21 12:29:46.768 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Start completed.
2025-08-21 12:29:46.875 [main] INFO  v.o.t.job.PromotionReportJob - Scheduling PromotionReportJob with cron: 0 05 16 * * *
2025-08-21 12:29:46.884 [main] INFO  v.o.t.job.PromotionReportJob - End PromotionReportJob!
2025-08-21 12:29:46.886 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-08-21 12:29:46.887 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-08-21 12:29:46.889 [main] INFO  v.o.transaction.job.UposReportJob - Scheduling UposReportJob with cron: 0 10 16 * * *
2025-08-21 12:29:46.890 [main] INFO  v.o.transaction.job.UposReportJob - End UposReportJob!
2025-08-21 12:29:49.839 [main] WARN  o.s.b.w.r.c.AnnotationConfigReactiveWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-08-21 12:29:49.842 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Shutdown initiated...
2025-08-21 12:29:49.851 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Shutdown completed.
2025-08-21 12:29:49.851 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Shutdown initiated...
2025-08-21 12:29:49.938 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Shutdown completed.
2025-08-21 12:29:49.948 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-21 12:29:49.963 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8396 was already in use.

Action:

Identify and stop the process that's listening on port 8396 or configure this application to listen on another port.

2025-08-21 12:30:13.691 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 2196030 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-08-21 12:30:13.693 [main] INFO  v.o.t.TransactionAppApplication - The following 1 profile is active: "dev"
2025-08-21 12:30:14.484 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-08-21 12:30:14.654 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 163 ms. Found 2 R2DBC repository interfaces.
2025-08-21 12:30:16.186 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Starting...
2025-08-21 12:30:16.521 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleMerchantPortalHikariPool - Added connection oracle.jdbc.driver.T4CConnection@21f50d2c
2025-08-21 12:30:16.523 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Start completed.
2025-08-21 12:30:16.596 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Starting...
2025-08-21 12:30:16.707 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleOneReportHikariPool - Added connection oracle.jdbc.driver.T4CConnection@5a466dd
2025-08-21 12:30:16.708 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Start completed.
2025-08-21 12:30:16.805 [main] INFO  v.o.t.job.PromotionReportJob - Scheduling PromotionReportJob with cron: 0 05 16 * * *
2025-08-21 12:30:16.813 [main] INFO  v.o.t.job.PromotionReportJob - End PromotionReportJob!
2025-08-21 12:30:16.815 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-08-21 12:30:16.816 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-08-21 12:30:16.818 [main] INFO  v.o.transaction.job.UposReportJob - Scheduling UposReportJob with cron: 0 10 16 * * *
2025-08-21 12:30:16.819 [main] INFO  v.o.transaction.job.UposReportJob - End UposReportJob!
2025-08-21 12:30:17.647 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 8397 (http)
2025-08-21 12:30:17.834 [main] INFO  v.o.t.TransactionAppApplication - Started TransactionAppApplication in 4.795 seconds (process running for 6.526)
2025-08-21 12:30:31.388 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: GET http://localhost:8397/ma-service/api/v1/transactions/transaction/detail?transactionId=*********&transactionType=Authorize&paymentMethod=QT
2025-08-21 12:30:31.388 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-08-21 12:30:31.388 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-08-21 12:30:31.389 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-08-21 12:30:31.389 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-08-21 12:30:31.389 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-08-21 12:30:31.389 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-08-21 12:30:31.389 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/mav2-main/
2025-08-21 12:30:31.389 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-08-21 12:30:31.389 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-08-21 12:30:31.389 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-08-21 12:30:31.389 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-08-21 12:30:31.389 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-08-21 12:30:31.389 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-08-21 12:30:31.389 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-08-21 12:30:31.389 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 417604
2025-08-21 12:30:31.389 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; JSESSIONID=dummy; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ1ODc1NjIsInVzZXIiOnsiZW1haWwiOiJhZG1pbkBvbmVwYXkudm4iLCJmdWxsX25hbWUiOiJBZG1pbiAxIiwiaWQiOiI1MUREN0M5QzdEQTZBMjc4OEY0QUI1M0I1NkJFNjgxMSIsImpvYl90aXRsZSI6IkFkbWluIGFsw7QyMjIiLCJwaG9uZSI6IjA5MDEyMzQ1NjEifX0.hEjfsrAg9gqUPMiXcjcugUJwwz7RVYeT8g3lnbyOc2Y
2025-08-21 12:30:31.390 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 12:30:31.390 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = eacaf613-4bb1-47bb-94c9-b1600a2dcfae
2025-08-21 12:30:31.390 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8397
2025-08-21 12:30:31.390 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-08-21 12:30:31.393 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-08-21 12:30:31.441 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=********* ,paymentMethod=QT ,transactionType=Authorize ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 12:30:31.441 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-08-21 12:30:31.504 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/********* ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 12:30:31.655 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"transaction_id":"*********","original_transaction_id":"*********","transaction_type":"Authorize","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"17","acquirer_name":"","acquirer_short_name":""},"transaction_reference":"TEST_1755506112099348164310","amount":{"total":12000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"340353****0900","card_type":"Amex","name_on_card":"NGUYEN VAN A","card_date":{"month":"12","year":"28"},"commercical_card":"888","commercial_card_indicator":"3"},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y","authentication_type":"Amex Safekey","authorization_code":"013834"},"transaction_time":"2025-08-18T15:35:24Z","transaction_status":"0","transaction_ref_number":"*********","ip_address":"************","ip_proxy":"N","bin_country":"UNITED STATES","csc_result_code":"","verification_security_level":"","response_code":"0","can_void":true,"risk_assesment":"Review Required","bank_id":"AMERICAN EXPRESS US CONSUMER","wait_for_approval_amount":"0.00","advance_status":"Successful","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"CREDIT","acqResponseCode":"","status3ds":"Y","riskOverAllResult":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"NGUYEN VAN A","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00","installment_statement_date":"","reasonName":""}
2025-08-21 12:30:31.656 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-08-21 12:30:31.671 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-08-21 12:30:31.681 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-08-21 12:30:31.701 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-08-18T15:35:24, orderAmount=12000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=*********, parentTransactionId=*********, transactionType=Authorize, paymentMethod=QT, transactionCreatedTime=2025-08-18T15:35:24, transactionCompletedTime=2025-08-18T15:35:24, transactionAmount=12000.0, transactionCurrency=VND, transactionStatus=Successful, responseCode=0, merchantTransRef=TEST_1755506112099348164310, parentMerchantTransRef=null, paymentChannel=null, installmentStatus=, operator=null), paymentMethod=PaymentMethod(cardNumber=340353****0900, cardBrand=null, cardType=Amex, issuer=, acquirer=17, cardExpiry=12/28, nameOnCard=NGUYEN VAN A, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, settlementAmount=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=InstallmentDetail(priceDifference=0, period=, amountMonthly=0.00), promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=************, ipProxy=N, ipCountry=************, binCountry=UNITED STATES, riskAssessment=Review Required), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null, amountAdvance=null), actions=null, histories=null)
2025-08-21 12:30:31.702 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-08-21 12:30:31.702 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getAdvanceFeeInfo ===================
2025-08-21 12:30:31.892 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ==================== Start getMerchantById ====================
2025-08-21 12:30:31.902 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getParentMerchantTransRef ===================
2025-08-21 12:30:31.926 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getPromotionInfo ===================
2025-08-21 12:30:31.932 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call getTransactionHistory-----------
2025-08-21 12:30:31.932 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: originalTransactionId=********* ,paymentMethod=QT ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 12:30:31.932 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionHistory MA-----------
2025-08-21 12:30:31.933 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/*********/history ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 12:30:31.944 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION HISTORY API: response code=200 ,response body={"transactions":[{"transaction_id":"111803899","original_transaction_id":"*********","response_code":"","reference_number":"","merchant_transaction_ref":"VOID_A_TESTPCI_311b2241-8bdb-48","transaction_type":"Void Authorize","amount":{"total":12000.0,"currency":"VND"},"status":"","operator_id":"PSP-ONECREDIT","merchant_id":"TESTPCI","transaction_time":"2025-08-21T18:20:07Z","advance_status":"Pending","financial_transaction_id":"","note":"","parent_id":"*********","dadId":"*********","subHistories":""},{"transaction_id":"111803898","original_transaction_id":"*********","response_code":"","reference_number":"","merchant_transaction_ref":"VOID_A_TESTPCI_0a17da55-ea16-46","transaction_type":"Void Authorize","amount":{"total":12000.0,"currency":"VND"},"status":"","operator_id":"PSP-ONECREDIT","merchant_id":"TESTPCI","transaction_time":"2025-08-21T18:19:02Z","advance_status":"Pending","financial_transaction_id":"","note":"","parent_id":"*********","dadId":"*********","subHistories":""},{"transaction_id":"*********","original_transaction_id":"*********","response_code":"0","reference_number":"*********","merchant_transaction_ref":"TEST_1755506112099348164310","transaction_type":"Authorize","amount":{"total":12000.0,"currency":"VND"},"status":"0","operator_id":"","merchant_id":"TESTPCI","transaction_time":"2025-08-18T15:35:24Z","advance_status":"Successful","financial_transaction_id":"*********","note":"","parent_id":"*********","dadId":"","subHistories":""}]}
2025-08-21 12:30:31.944 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionHistory MA------------
2025-08-21 12:30:31.944 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION HISTORY INTERNATIONAL: {"transactions":[{"transaction_id":"111803899","original_transaction_id":"*********","response_code":"","reference_number":"","merchant_transaction_ref":"VOID_A_TESTPCI_311b2241-8bdb-48","transaction_type":"Void Authorize","amount":{"total":12000.0,"currency":"VND"},"status":"","operator_id":"PSP-ONECREDIT","merchant_id":"TESTPCI","transaction_time":"2025-08-21T18:20:07Z","advance_status":"Pending","financial_transaction_id":"","note":"","parent_id":"*********","dadId":"*********","subHistories":""},{"transaction_id":"111803898","original_transaction_id":"*********","response_code":"","reference_number":"","merchant_transaction_ref":"VOID_A_TESTPCI_0a17da55-ea16-46","transaction_type":"Void Authorize","amount":{"total":12000.0,"currency":"VND"},"status":"","operator_id":"PSP-ONECREDIT","merchant_id":"TESTPCI","transaction_time":"2025-08-21T18:19:02Z","advance_status":"Pending","financial_transaction_id":"","note":"","parent_id":"*********","dadId":"*********","subHistories":""},{"transaction_id":"*********","original_transaction_id":"*********","response_code":"0","reference_number":"*********","merchant_transaction_ref":"TEST_1755506112099348164310","transaction_type":"Authorize","amount":{"total":12000.0,"currency":"VND"},"status":"0","operator_id":"","merchant_id":"TESTPCI","transaction_time":"2025-08-18T15:35:24Z","advance_status":"Successful","financial_transaction_id":"*********","note":"","parent_id":"*********","dadId":"","subHistories":""}]}
2025-08-21 12:30:31.950 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call getTransactionHistory------------
2025-08-21 12:30:31.950 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-08-21 12:30:31.950 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-08-21 12:30:32.217 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-08-21 12:30:32.217 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - list merchant from permission: [OP_TESTVND, OP_VND01, OP_MPGSVND, OP_MPGSUSD, OP_MPAYVND3D, TESTBIDVV3, ONEPAYHM, D_TEST, OP_CYBSVND, TEST3DSMPGS, OP_MPAYVN1, TESTND, OP_TESTK7011, TESTHD3BEN, TESTSTATICQR, OP_TESTK4121, OP_SACOMTH, TESTAPLVNC, TESTORIFLCBS, TESTVCB_5411, OP_TESTK7399, TESTGGPAY, OP_TESTK5331, TESTKOVENA, TESTSTGINVOICE, TESTMERCHANT16KY, TESTOPVPB, TESTVTBCYBER, HUONG1, TESTOPBIDV, TESTTRAGOP, TESTPCI, TESTONEPAY, OP_TESTUSD, OP_SACOMTRVV, ONEPAY, INVOICETG, TESTDMX, TESTTNS, TESTINVOICE, TESTVCB3D2, TESTBIDV, TESTHC, TEST3DS2_LT, MIGS3DS, TESTAXA1, TESTVF, TESTUPOSTG, TESTAOSVNBANK, OP_TESTKBANK, TESTAPPLE, TESTVCBHM1, TESTTVLOKA, TESTVCB_7399, TESTVAGROUP, TESTVCB_6300, OP_TESTM8211, OP_TESTK7372, OP_TESTK7991, TESTPCI4, TESTOPVCB2, TESTSACOMCYBER, TESTUSD, OP_TESTVP5732, OP_TESTVP6300, ONEPAYMT, TESTATM, OPTEST, TESTTNSUSD, MPAYVND, TESTBIDV1, OP_VPBMPGS3, TESTAWPOSTG, TESTTRAGOP1, TESTSAMSUNG, TESTOP_VTB2, TESTUPOSKBANK, TESTAPLBANK, TESTVCB_8211, TESTMERCHANTID20KYTU, MONPAYOUT, TESTVIETQR, TESTINVOICETG, OP_TESTVP8211, TESTPAYOUT2, TEST, OP_SACOMCBSU, OP_VPBMPGS1, TESTONEPAY20, TESTVJU, OP_TESTCONFIG, OP_TESTSS, OP_TESTBHX, OPTESTHB, TESTAWPOS, TESTACVNC, OP_TESTK4900, TESTONEPAYTHB, ONEPAY_REFUND, TESTAPLTG, OP_TESTK5814, TESTVCB_4722, OP_TESTK5722, TESTPAYOUT1, TESTOPVCB, TESTIDTEST4, TESTONEPAYDVC, OP_SACOMCBSV, OP_SACOMTRVU, ****************, OPMPAY, FALSE, TESTDUONG, OP_VPBMPGS2, TESTSACOMCBU, TESTLZD, TESTSAPO, OP_TESTAUTH, TESTMEGA1, OP_TESTMID, TESTVTASABRE, OP_TESTK4722, TESTOP_VTB3, OP_SACOMVIN, OP_TESTK8220, TESTCNY, TESTVCB_4900, OP_TESTK5698, TESTPCI3, ANHTVTEST, TESTTOKEN, TESTONEPAY2, MONPAYCOLLECT, OP_TESTVP5411, TESTSHOPIFYTG, TESTOP_D, TESTONEPAYDVTT, TESTTRAGOP2, TESTBIDVU3, TESTBIDVV2, TESTTOKENUSD, TESTMEGA, TEST3DS2_MK, TEST3DS2_TN, TESTVFVND, TESTVTB3D2, TESTENETVIET, TESTSHOPIFY, TESTAPPLEND, TESTVCBHM2, TESTTRAGOP3, TESTOKENOP, TESTKONEVA, TESTOP_VTB1, AUTOAPLND, TESTSSPAY, TESTSAMSUNGPL, TESTVCB_5732, TESTAES, OP_TESTK7832, TESTPCI2, TESTJBAUSD, TESTVCBCYBER, TESTMERCHANT15K, OPPREPAID, TESTONEPAYUSD, OP_MPAYVND, TESTSACOMCBV, OP_VCBVND, TESTSAPO2, TESTBIDVCBSU, TESTVCB3D2O, TESTMAFC, OP_TESTOP20, TESTAXA, TESTTGDD3D2, TESTPR, TESTHC1, ONEPAYHB, TESTSSBNPL, OP_TESTK8299, TESTHD2BEN, TESTPCIEM, TESTMCD, OP_TESTK5732, TESTPROMMG, OP_TESTTT, TESTAUTOMSP, OP_TESTUSD2, OP_TESTAPPAY, TESTTHB, TESTSOS, TESTAPPLETG, OP_TESTK5977, EKKO1, TESTONEPAY1, TESTIDTEST6, TESTONEPAYDVDT, TESTBIDVCSU2, ****************, OP_VCBUSD, TESTBIDVCBSV, OP_TEST3DS, OP_TEST3DS2V, OP_CYBSUSD, TESTTHSC, TESTBNPL, TESTAOSVNC, TESTPROM, OP_SACOMECOM, TESTQRTINH, TESTTOKENDV, TESTUPOS, OP_TESTK5969, OP_TESTK5816, TESTONEPAY3, TESTPCI1, TESTKBANKCYBER, TESTVPBCYBER, TEST_OP2B, OP_TESTVP4900, TESTIDTEST3, TESTPAYPAL]
2025-08-21 12:30:32.220 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-08-21 12:30:32.221 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-08-21 12:30:32.221 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start checkPerAndActionByUserId-----------
2025-08-21 12:30:32.278 [reactor-http-epoll-3] INFO  v.o.t.repository.AcquirerRepository - Starting getListAcquirer - calling function ONEDATA.GET_LIST_ACQUIRER
2025-08-21 12:31:23.985 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 2196951 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-08-21 12:31:23.988 [main] INFO  v.o.t.TransactionAppApplication - The following 1 profile is active: "dev"
2025-08-21 12:31:24.728 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-08-21 12:31:24.900 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 164 ms. Found 2 R2DBC repository interfaces.
2025-08-21 12:31:26.396 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Starting...
2025-08-21 12:31:26.675 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleMerchantPortalHikariPool - Added connection oracle.jdbc.driver.T4CConnection@2e1add6f
2025-08-21 12:31:26.677 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Start completed.
2025-08-21 12:31:26.744 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Starting...
2025-08-21 12:31:26.846 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleOneReportHikariPool - Added connection oracle.jdbc.driver.T4CConnection@6fbe4800
2025-08-21 12:31:26.846 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Start completed.
2025-08-21 12:31:26.946 [main] INFO  v.o.t.job.PromotionReportJob - Scheduling PromotionReportJob with cron: 0 05 16 * * *
2025-08-21 12:31:26.954 [main] INFO  v.o.t.job.PromotionReportJob - End PromotionReportJob!
2025-08-21 12:31:26.956 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-08-21 12:31:26.957 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-08-21 12:31:26.959 [main] INFO  v.o.transaction.job.UposReportJob - Scheduling UposReportJob with cron: 0 10 16 * * *
2025-08-21 12:31:26.960 [main] INFO  v.o.transaction.job.UposReportJob - End UposReportJob!
2025-08-21 12:31:27.014 [main] WARN  o.s.b.w.r.c.AnnotationConfigReactiveWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'checkPermissionUtil' defined in file [/root/onepay/ma-transaction-backend/target/classes/vn/onepay/transaction/util/CheckPermissionUtil.class]: Failed to instantiate [vn.onepay.transaction.util.CheckPermissionUtil]: Constructor threw exception
2025-08-21 12:31:27.015 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Shutdown initiated...
2025-08-21 12:31:27.024 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Shutdown completed.
2025-08-21 12:31:27.025 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Shutdown initiated...
2025-08-21 12:31:27.117 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Shutdown completed.
2025-08-21 12:31:27.129 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-21 12:31:27.146 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'checkPermissionUtil' defined in file [/root/onepay/ma-transaction-backend/target/classes/vn/onepay/transaction/util/CheckPermissionUtil.class]: Failed to instantiate [vn.onepay.transaction.util.CheckPermissionUtil]: Constructor threw exception
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:321)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:309)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1381)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1218)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:563)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:523)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:347)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1155)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1121)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1056)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.reactive.context.ReactiveWebServerApplicationContext.refresh(ReactiveWebServerApplicationContext.java:66)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at vn.onepay.transaction.TransactionAppApplication.main(TransactionAppApplication.java:13)
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [vn.onepay.transaction.util.CheckPermissionUtil]: Constructor threw exception
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:222)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:145)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:318)
	... 21 common frames omitted
Caused by: java.lang.Error: Unresolved compilation problems: 
	The blank final field deadlineConfig may not have been initialized
	The final field CheckPermissionUtil.deadlineConfig cannot be assigned

	at vn.onepay.transaction.util.CheckPermissionUtil.<init>(CheckPermissionUtil.java:63)
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:209)
	... 23 common frames omitted
2025-08-21 12:32:05.185 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 2197466 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-08-21 12:32:05.187 [main] INFO  v.o.t.TransactionAppApplication - The following 1 profile is active: "dev"
2025-08-21 12:32:05.967 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-08-21 12:32:06.145 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 171 ms. Found 2 R2DBC repository interfaces.
2025-08-21 12:32:07.924 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Starting...
2025-08-21 12:32:08.228 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleMerchantPortalHikariPool - Added connection oracle.jdbc.driver.T4CConnection@46702c26
2025-08-21 12:32:08.230 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Start completed.
2025-08-21 12:32:08.301 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Starting...
2025-08-21 12:32:08.420 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleOneReportHikariPool - Added connection oracle.jdbc.driver.T4CConnection@146f3d22
2025-08-21 12:32:08.421 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Start completed.
2025-08-21 12:32:08.525 [main] INFO  v.o.t.job.PromotionReportJob - Scheduling PromotionReportJob with cron: 0 05 16 * * *
2025-08-21 12:32:08.534 [main] INFO  v.o.t.job.PromotionReportJob - End PromotionReportJob!
2025-08-21 12:32:08.536 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-08-21 12:32:08.537 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-08-21 12:32:08.539 [main] INFO  v.o.transaction.job.UposReportJob - Scheduling UposReportJob with cron: 0 10 16 * * *
2025-08-21 12:32:08.540 [main] INFO  v.o.transaction.job.UposReportJob - End UposReportJob!
2025-08-21 12:32:09.482 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 8397 (http)
2025-08-21 12:32:09.834 [main] INFO  v.o.t.TransactionAppApplication - Started TransactionAppApplication in 5.297 seconds (process running for 6.828)
2025-08-21 12:32:13.251 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: GET http://localhost:8397/ma-service/api/v1/transactions/transaction/detail?transactionId=*********&transactionType=Authorize&paymentMethod=QT
2025-08-21 12:32:13.251 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-08-21 12:32:13.252 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-08-21 12:32:13.252 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-08-21 12:32:13.252 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-08-21 12:32:13.252 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-08-21 12:32:13.252 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-08-21 12:32:13.252 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/mav2-main/
2025-08-21 12:32:13.252 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-08-21 12:32:13.252 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-08-21 12:32:13.252 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-08-21 12:32:13.252 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-08-21 12:32:13.252 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-08-21 12:32:13.252 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-08-21 12:32:13.253 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-08-21 12:32:13.253 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 417604
2025-08-21 12:32:13.253 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; JSESSIONID=dummy; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ1ODc1NjIsInVzZXIiOnsiZW1haWwiOiJhZG1pbkBvbmVwYXkudm4iLCJmdWxsX25hbWUiOiJBZG1pbiAxIiwiaWQiOiI1MUREN0M5QzdEQTZBMjc4OEY0QUI1M0I1NkJFNjgxMSIsImpvYl90aXRsZSI6IkFkbWluIGFsw7QyMjIiLCJwaG9uZSI6IjA5MDEyMzQ1NjEifX0.hEjfsrAg9gqUPMiXcjcugUJwwz7RVYeT8g3lnbyOc2Y
2025-08-21 12:32:13.253 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 12:32:13.253 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 93abc1c7-85c2-4bf4-bf42-b8fb58cb168e
2025-08-21 12:32:13.253 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8397
2025-08-21 12:32:13.253 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-08-21 12:32:13.256 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-08-21 12:32:13.313 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=********* ,paymentMethod=QT ,transactionType=Authorize ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 12:32:13.313 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-08-21 12:32:13.386 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/********* ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 12:32:13.473 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"transaction_id":"*********","original_transaction_id":"*********","transaction_type":"Authorize","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"17","acquirer_name":"","acquirer_short_name":""},"transaction_reference":"TEST_1755506112099348164310","amount":{"total":12000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"340353****0900","card_type":"Amex","name_on_card":"NGUYEN VAN A","card_date":{"month":"12","year":"28"},"commercical_card":"888","commercial_card_indicator":"3"},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y","authentication_type":"Amex Safekey","authorization_code":"013834"},"transaction_time":"2025-08-18T15:35:24Z","transaction_status":"0","transaction_ref_number":"*********","ip_address":"************","ip_proxy":"N","bin_country":"UNITED STATES","csc_result_code":"","verification_security_level":"","response_code":"0","can_void":true,"risk_assesment":"Review Required","bank_id":"AMERICAN EXPRESS US CONSUMER","wait_for_approval_amount":"0.00","advance_status":"Successful","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"CREDIT","acqResponseCode":"","status3ds":"Y","riskOverAllResult":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"NGUYEN VAN A","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00","installment_statement_date":"","reasonName":""}
2025-08-21 12:32:13.473 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-08-21 12:32:13.489 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-08-21 12:32:13.498 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-08-21 12:32:13.518 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-08-18T15:35:24, orderAmount=12000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=*********, parentTransactionId=*********, transactionType=Authorize, paymentMethod=QT, transactionCreatedTime=2025-08-18T15:35:24, transactionCompletedTime=2025-08-18T15:35:24, transactionAmount=12000.0, transactionCurrency=VND, transactionStatus=Successful, responseCode=0, merchantTransRef=TEST_1755506112099348164310, parentMerchantTransRef=null, paymentChannel=null, installmentStatus=, operator=null), paymentMethod=PaymentMethod(cardNumber=340353****0900, cardBrand=null, cardType=Amex, issuer=, acquirer=17, cardExpiry=12/28, nameOnCard=NGUYEN VAN A, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, settlementAmount=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=InstallmentDetail(priceDifference=0, period=, amountMonthly=0.00), promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=************, ipProxy=N, ipCountry=************, binCountry=UNITED STATES, riskAssessment=Review Required), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null, amountAdvance=null), actions=null, histories=null)
2025-08-21 12:32:13.518 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-08-21 12:32:13.518 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getAdvanceFeeInfo ===================
2025-08-21 12:32:13.698 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ==================== Start getMerchantById ====================
2025-08-21 12:32:13.707 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getParentMerchantTransRef ===================
2025-08-21 12:32:13.728 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getPromotionInfo ===================
2025-08-21 12:32:13.733 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call getTransactionHistory-----------
2025-08-21 12:32:13.733 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: originalTransactionId=********* ,paymentMethod=QT ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 12:32:13.733 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionHistory MA-----------
2025-08-21 12:32:13.734 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/*********/history ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 12:32:13.743 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION HISTORY API: response code=200 ,response body={"transactions":[{"transaction_id":"111803899","original_transaction_id":"*********","response_code":"","reference_number":"","merchant_transaction_ref":"VOID_A_TESTPCI_311b2241-8bdb-48","transaction_type":"Void Authorize","amount":{"total":12000.0,"currency":"VND"},"status":"","operator_id":"PSP-ONECREDIT","merchant_id":"TESTPCI","transaction_time":"2025-08-21T18:20:07Z","advance_status":"Pending","financial_transaction_id":"","note":"","parent_id":"*********","dadId":"*********","subHistories":""},{"transaction_id":"111803898","original_transaction_id":"*********","response_code":"","reference_number":"","merchant_transaction_ref":"VOID_A_TESTPCI_0a17da55-ea16-46","transaction_type":"Void Authorize","amount":{"total":12000.0,"currency":"VND"},"status":"","operator_id":"PSP-ONECREDIT","merchant_id":"TESTPCI","transaction_time":"2025-08-21T18:19:02Z","advance_status":"Pending","financial_transaction_id":"","note":"","parent_id":"*********","dadId":"*********","subHistories":""},{"transaction_id":"*********","original_transaction_id":"*********","response_code":"0","reference_number":"*********","merchant_transaction_ref":"TEST_1755506112099348164310","transaction_type":"Authorize","amount":{"total":12000.0,"currency":"VND"},"status":"0","operator_id":"","merchant_id":"TESTPCI","transaction_time":"2025-08-18T15:35:24Z","advance_status":"Successful","financial_transaction_id":"*********","note":"","parent_id":"*********","dadId":"","subHistories":""}]}
2025-08-21 12:32:13.743 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionHistory MA------------
2025-08-21 12:32:13.743 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION HISTORY INTERNATIONAL: {"transactions":[{"transaction_id":"111803899","original_transaction_id":"*********","response_code":"","reference_number":"","merchant_transaction_ref":"VOID_A_TESTPCI_311b2241-8bdb-48","transaction_type":"Void Authorize","amount":{"total":12000.0,"currency":"VND"},"status":"","operator_id":"PSP-ONECREDIT","merchant_id":"TESTPCI","transaction_time":"2025-08-21T18:20:07Z","advance_status":"Pending","financial_transaction_id":"","note":"","parent_id":"*********","dadId":"*********","subHistories":""},{"transaction_id":"111803898","original_transaction_id":"*********","response_code":"","reference_number":"","merchant_transaction_ref":"VOID_A_TESTPCI_0a17da55-ea16-46","transaction_type":"Void Authorize","amount":{"total":12000.0,"currency":"VND"},"status":"","operator_id":"PSP-ONECREDIT","merchant_id":"TESTPCI","transaction_time":"2025-08-21T18:19:02Z","advance_status":"Pending","financial_transaction_id":"","note":"","parent_id":"*********","dadId":"*********","subHistories":""},{"transaction_id":"*********","original_transaction_id":"*********","response_code":"0","reference_number":"*********","merchant_transaction_ref":"TEST_1755506112099348164310","transaction_type":"Authorize","amount":{"total":12000.0,"currency":"VND"},"status":"0","operator_id":"","merchant_id":"TESTPCI","transaction_time":"2025-08-18T15:35:24Z","advance_status":"Successful","financial_transaction_id":"*********","note":"","parent_id":"*********","dadId":"","subHistories":""}]}
2025-08-21 12:32:13.748 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call getTransactionHistory------------
2025-08-21 12:32:13.748 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-08-21 12:32:13.748 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-08-21 12:32:13.951 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-08-21 12:32:13.952 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - list merchant from permission: [OP_TESTVND, OP_VND01, OP_MPGSVND, OP_MPGSUSD, OP_MPAYVND3D, TESTBIDVV3, ONEPAYHM, D_TEST, OP_CYBSVND, TEST3DSMPGS, OP_MPAYVN1, TESTND, OP_TESTK7011, TESTHD3BEN, TESTSTATICQR, OP_TESTK4121, OP_SACOMTH, TESTAPLVNC, TESTORIFLCBS, TESTVCB_5411, OP_TESTK7399, TESTGGPAY, OP_TESTK5331, TESTKOVENA, TESTSTGINVOICE, TESTMERCHANT16KY, TESTOPVPB, TESTVTBCYBER, HUONG1, TESTOPBIDV, TESTTRAGOP, TESTPCI, TESTONEPAY, OP_TESTUSD, OP_SACOMTRVV, ONEPAY, INVOICETG, TESTDMX, TESTTNS, TESTINVOICE, TESTVCB3D2, TESTBIDV, TESTHC, TEST3DS2_LT, MIGS3DS, TESTAXA1, TESTVF, TESTUPOSTG, TESTAOSVNBANK, OP_TESTKBANK, TESTAPPLE, TESTVCBHM1, TESTTVLOKA, TESTVCB_7399, TESTVAGROUP, TESTVCB_6300, OP_TESTM8211, OP_TESTK7372, OP_TESTK7991, TESTPCI4, TESTOPVCB2, TESTSACOMCYBER, TESTUSD, OP_TESTVP5732, OP_TESTVP6300, ONEPAYMT, TESTATM, OPTEST, TESTTNSUSD, MPAYVND, TESTBIDV1, OP_VPBMPGS3, TESTAWPOSTG, TESTTRAGOP1, TESTSAMSUNG, TESTOP_VTB2, TESTUPOSKBANK, TESTAPLBANK, TESTVCB_8211, TESTMERCHANTID20KYTU, MONPAYOUT, TESTVIETQR, TESTINVOICETG, OP_TESTVP8211, TESTPAYOUT2, TEST, OP_SACOMCBSU, OP_VPBMPGS1, TESTONEPAY20, TESTVJU, OP_TESTCONFIG, OP_TESTSS, OP_TESTBHX, OPTESTHB, TESTAWPOS, TESTACVNC, OP_TESTK4900, TESTONEPAYTHB, ONEPAY_REFUND, TESTAPLTG, OP_TESTK5814, TESTVCB_4722, OP_TESTK5722, TESTPAYOUT1, TESTOPVCB, TESTIDTEST4, TESTONEPAYDVC, OP_SACOMCBSV, OP_SACOMTRVU, ****************, OPMPAY, FALSE, TESTDUONG, OP_VPBMPGS2, TESTSACOMCBU, TESTLZD, TESTSAPO, OP_TESTAUTH, TESTMEGA1, OP_TESTMID, TESTVTASABRE, OP_TESTK4722, TESTOP_VTB3, OP_SACOMVIN, OP_TESTK8220, TESTCNY, TESTVCB_4900, OP_TESTK5698, TESTPCI3, ANHTVTEST, TESTTOKEN, TESTONEPAY2, MONPAYCOLLECT, OP_TESTVP5411, TESTSHOPIFYTG, TESTOP_D, TESTONEPAYDVTT, TESTTRAGOP2, TESTBIDVU3, TESTBIDVV2, TESTTOKENUSD, TESTMEGA, TEST3DS2_MK, TEST3DS2_TN, TESTVFVND, TESTVTB3D2, TESTENETVIET, TESTSHOPIFY, TESTAPPLEND, TESTVCBHM2, TESTTRAGOP3, TESTOKENOP, TESTKONEVA, TESTOP_VTB1, AUTOAPLND, TESTSSPAY, TESTSAMSUNGPL, TESTVCB_5732, TESTAES, OP_TESTK7832, TESTPCI2, TESTJBAUSD, TESTVCBCYBER, TESTMERCHANT15K, OPPREPAID, TESTONEPAYUSD, OP_MPAYVND, TESTSACOMCBV, OP_VCBVND, TESTSAPO2, TESTBIDVCBSU, TESTVCB3D2O, TESTMAFC, OP_TESTOP20, TESTAXA, TESTTGDD3D2, TESTPR, TESTHC1, ONEPAYHB, TESTSSBNPL, OP_TESTK8299, TESTHD2BEN, TESTPCIEM, TESTMCD, OP_TESTK5732, TESTPROMMG, OP_TESTTT, TESTAUTOMSP, OP_TESTUSD2, OP_TESTAPPAY, TESTTHB, TESTSOS, TESTAPPLETG, OP_TESTK5977, EKKO1, TESTONEPAY1, TESTIDTEST6, TESTONEPAYDVDT, TESTBIDVCSU2, ****************, OP_VCBUSD, TESTBIDVCBSV, OP_TEST3DS, OP_TEST3DS2V, OP_CYBSUSD, TESTTHSC, TESTBNPL, TESTAOSVNC, TESTPROM, OP_SACOMECOM, TESTQRTINH, TESTTOKENDV, TESTUPOS, OP_TESTK5969, OP_TESTK5816, TESTONEPAY3, TESTPCI1, TESTKBANKCYBER, TESTVPBCYBER, TEST_OP2B, OP_TESTVP4900, TESTIDTEST3, TESTPAYPAL]
2025-08-21 12:32:13.954 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-08-21 12:32:13.956 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-08-21 12:32:13.956 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start checkPerAndActionByUserId-----------
2025-08-21 12:32:13.998 [reactor-http-epoll-3] INFO  v.o.t.repository.AcquirerRepository - Starting getListAcquirer - calling function ONEDATA.GET_LIST_ACQUIRER
2025-08-21 12:32:14.005 [reactor-http-epoll-3] INFO  v.o.t.util.CheckPermissionUtil - 30
2025-08-21 12:32:42.369 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 2198045 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-08-21 12:32:42.371 [main] INFO  v.o.t.TransactionAppApplication - The following 1 profile is active: "dev"
2025-08-21 12:32:43.120 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-08-21 12:32:43.292 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 166 ms. Found 2 R2DBC repository interfaces.
2025-08-21 12:32:44.795 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Starting...
2025-08-21 12:32:45.111 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleMerchantPortalHikariPool - Added connection oracle.jdbc.driver.T4CConnection@46702c26
2025-08-21 12:32:45.113 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Start completed.
2025-08-21 12:32:45.181 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Starting...
2025-08-21 12:32:45.497 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleOneReportHikariPool - Added connection oracle.jdbc.driver.T4CConnection@146f3d22
2025-08-21 12:32:45.498 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Start completed.
2025-08-21 12:32:45.593 [main] INFO  v.o.t.job.PromotionReportJob - Scheduling PromotionReportJob with cron: 0 05 16 * * *
2025-08-21 12:32:45.601 [main] INFO  v.o.t.job.PromotionReportJob - End PromotionReportJob!
2025-08-21 12:32:45.603 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-08-21 12:32:45.604 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-08-21 12:32:45.605 [main] INFO  v.o.transaction.job.UposReportJob - Scheduling UposReportJob with cron: 0 10 16 * * *
2025-08-21 12:32:45.606 [main] INFO  v.o.transaction.job.UposReportJob - End UposReportJob!
2025-08-21 12:32:46.493 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 8397 (http)
2025-08-21 12:32:46.691 [main] INFO  v.o.t.TransactionAppApplication - Started TransactionAppApplication in 4.962 seconds (process running for 6.543)
2025-08-21 12:32:51.755 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: GET http://localhost:8397/ma-service/api/v1/transactions/transaction/detail?transactionId=*********&transactionType=Authorize&paymentMethod=QT
2025-08-21 12:32:51.756 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-08-21 12:32:51.756 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-08-21 12:32:51.756 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-08-21 12:32:51.756 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-08-21 12:32:51.756 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-08-21 12:32:51.756 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-08-21 12:32:51.756 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/mav2-main/
2025-08-21 12:32:51.756 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-08-21 12:32:51.756 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-08-21 12:32:51.756 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-08-21 12:32:51.756 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-08-21 12:32:51.756 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-08-21 12:32:51.757 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-08-21 12:32:51.757 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-08-21 12:32:51.757 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 417604
2025-08-21 12:32:51.757 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; JSESSIONID=dummy; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ1ODc1NjIsInVzZXIiOnsiZW1haWwiOiJhZG1pbkBvbmVwYXkudm4iLCJmdWxsX25hbWUiOiJBZG1pbiAxIiwiaWQiOiI1MUREN0M5QzdEQTZBMjc4OEY0QUI1M0I1NkJFNjgxMSIsImpvYl90aXRsZSI6IkFkbWluIGFsw7QyMjIiLCJwaG9uZSI6IjA5MDEyMzQ1NjEifX0.hEjfsrAg9gqUPMiXcjcugUJwwz7RVYeT8g3lnbyOc2Y
2025-08-21 12:32:51.757 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 12:32:51.757 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = 96da9bca-9fa0-4bc3-a3db-82eb69003cf8
2025-08-21 12:32:51.757 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8397
2025-08-21 12:32:51.757 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-08-21 12:32:51.761 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-08-21 12:32:51.806 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=********* ,paymentMethod=QT ,transactionType=Authorize ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 12:32:51.807 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-08-21 12:32:51.864 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/********* ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 12:32:51.943 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"transaction_id":"*********","original_transaction_id":"*********","transaction_type":"Authorize","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"17","acquirer_name":"","acquirer_short_name":""},"transaction_reference":"TEST_1755506112099348164310","amount":{"total":12000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"340353****0900","card_type":"Amex","name_on_card":"NGUYEN VAN A","card_date":{"month":"12","year":"28"},"commercical_card":"888","commercial_card_indicator":"3"},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y","authentication_type":"Amex Safekey","authorization_code":"013834"},"transaction_time":"2025-08-18T15:35:24Z","transaction_status":"0","transaction_ref_number":"*********","ip_address":"************","ip_proxy":"N","bin_country":"UNITED STATES","csc_result_code":"","verification_security_level":"","response_code":"0","can_void":true,"risk_assesment":"Review Required","bank_id":"AMERICAN EXPRESS US CONSUMER","wait_for_approval_amount":"0.00","advance_status":"Successful","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"CREDIT","acqResponseCode":"","status3ds":"Y","riskOverAllResult":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"NGUYEN VAN A","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00","installment_statement_date":"","reasonName":""}
2025-08-21 12:32:51.944 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-08-21 12:32:51.958 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-08-21 12:32:51.967 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-08-21 12:32:51.987 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-08-18T15:35:24, orderAmount=12000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=*********, parentTransactionId=*********, transactionType=Authorize, paymentMethod=QT, transactionCreatedTime=2025-08-18T15:35:24, transactionCompletedTime=2025-08-18T15:35:24, transactionAmount=12000.0, transactionCurrency=VND, transactionStatus=Successful, responseCode=0, merchantTransRef=TEST_1755506112099348164310, parentMerchantTransRef=null, paymentChannel=null, installmentStatus=, operator=null), paymentMethod=PaymentMethod(cardNumber=340353****0900, cardBrand=null, cardType=Amex, issuer=, acquirer=17, cardExpiry=12/28, nameOnCard=NGUYEN VAN A, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, settlementAmount=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=InstallmentDetail(priceDifference=0, period=, amountMonthly=0.00), promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=************, ipProxy=N, ipCountry=************, binCountry=UNITED STATES, riskAssessment=Review Required), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null, amountAdvance=null), actions=null, histories=null)
2025-08-21 12:32:51.987 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-08-21 12:32:51.987 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getAdvanceFeeInfo ===================
2025-08-21 12:32:52.168 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ==================== Start getMerchantById ====================
2025-08-21 12:32:52.177 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getParentMerchantTransRef ===================
2025-08-21 12:32:52.199 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getPromotionInfo ===================
2025-08-21 12:32:52.204 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call getTransactionHistory-----------
2025-08-21 12:32:52.204 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: originalTransactionId=********* ,paymentMethod=QT ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 12:32:52.204 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionHistory MA-----------
2025-08-21 12:32:52.205 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/*********/history ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 12:32:52.215 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION HISTORY API: response code=200 ,response body={"transactions":[{"transaction_id":"111803899","original_transaction_id":"*********","response_code":"","reference_number":"","merchant_transaction_ref":"VOID_A_TESTPCI_311b2241-8bdb-48","transaction_type":"Void Authorize","amount":{"total":12000.0,"currency":"VND"},"status":"","operator_id":"PSP-ONECREDIT","merchant_id":"TESTPCI","transaction_time":"2025-08-21T18:20:07Z","advance_status":"Pending","financial_transaction_id":"","note":"","parent_id":"*********","dadId":"*********","subHistories":""},{"transaction_id":"111803898","original_transaction_id":"*********","response_code":"","reference_number":"","merchant_transaction_ref":"VOID_A_TESTPCI_0a17da55-ea16-46","transaction_type":"Void Authorize","amount":{"total":12000.0,"currency":"VND"},"status":"","operator_id":"PSP-ONECREDIT","merchant_id":"TESTPCI","transaction_time":"2025-08-21T18:19:02Z","advance_status":"Pending","financial_transaction_id":"","note":"","parent_id":"*********","dadId":"*********","subHistories":""},{"transaction_id":"*********","original_transaction_id":"*********","response_code":"0","reference_number":"*********","merchant_transaction_ref":"TEST_1755506112099348164310","transaction_type":"Authorize","amount":{"total":12000.0,"currency":"VND"},"status":"0","operator_id":"","merchant_id":"TESTPCI","transaction_time":"2025-08-18T15:35:24Z","advance_status":"Successful","financial_transaction_id":"*********","note":"","parent_id":"*********","dadId":"","subHistories":""}]}
2025-08-21 12:32:52.215 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionHistory MA------------
2025-08-21 12:32:52.215 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION HISTORY INTERNATIONAL: {"transactions":[{"transaction_id":"111803899","original_transaction_id":"*********","response_code":"","reference_number":"","merchant_transaction_ref":"VOID_A_TESTPCI_311b2241-8bdb-48","transaction_type":"Void Authorize","amount":{"total":12000.0,"currency":"VND"},"status":"","operator_id":"PSP-ONECREDIT","merchant_id":"TESTPCI","transaction_time":"2025-08-21T18:20:07Z","advance_status":"Pending","financial_transaction_id":"","note":"","parent_id":"*********","dadId":"*********","subHistories":""},{"transaction_id":"111803898","original_transaction_id":"*********","response_code":"","reference_number":"","merchant_transaction_ref":"VOID_A_TESTPCI_0a17da55-ea16-46","transaction_type":"Void Authorize","amount":{"total":12000.0,"currency":"VND"},"status":"","operator_id":"PSP-ONECREDIT","merchant_id":"TESTPCI","transaction_time":"2025-08-21T18:19:02Z","advance_status":"Pending","financial_transaction_id":"","note":"","parent_id":"*********","dadId":"*********","subHistories":""},{"transaction_id":"*********","original_transaction_id":"*********","response_code":"0","reference_number":"*********","merchant_transaction_ref":"TEST_1755506112099348164310","transaction_type":"Authorize","amount":{"total":12000.0,"currency":"VND"},"status":"0","operator_id":"","merchant_id":"TESTPCI","transaction_time":"2025-08-18T15:35:24Z","advance_status":"Successful","financial_transaction_id":"*********","note":"","parent_id":"*********","dadId":"","subHistories":""}]}
2025-08-21 12:32:52.220 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call getTransactionHistory------------
2025-08-21 12:32:52.220 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-08-21 12:32:52.220 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-08-21 12:32:52.433 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-08-21 12:32:52.434 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - list merchant from permission: [OP_TESTVND, OP_VND01, OP_MPGSVND, OP_MPGSUSD, OP_MPAYVND3D, TESTBIDVV3, ONEPAYHM, D_TEST, OP_CYBSVND, TEST3DSMPGS, OP_MPAYVN1, TESTND, OP_TESTK7011, TESTHD3BEN, TESTSTATICQR, OP_TESTK4121, OP_SACOMTH, TESTAPLVNC, TESTORIFLCBS, TESTVCB_5411, OP_TESTK7399, TESTGGPAY, OP_TESTK5331, TESTKOVENA, TESTSTGINVOICE, TESTMERCHANT16KY, TESTOPVPB, TESTVTBCYBER, HUONG1, TESTOPBIDV, TESTTRAGOP, TESTPCI, TESTONEPAY, OP_TESTUSD, OP_SACOMTRVV, ONEPAY, INVOICETG, TESTDMX, TESTTNS, TESTINVOICE, TESTVCB3D2, TESTBIDV, TESTHC, TEST3DS2_LT, MIGS3DS, TESTAXA1, TESTVF, TESTUPOSTG, TESTAOSVNBANK, OP_TESTKBANK, TESTAPPLE, TESTVCBHM1, TESTTVLOKA, TESTVCB_7399, TESTVAGROUP, TESTVCB_6300, OP_TESTM8211, OP_TESTK7372, OP_TESTK7991, TESTPCI4, TESTOPVCB2, TESTSACOMCYBER, TESTUSD, OP_TESTVP5732, OP_TESTVP6300, ONEPAYMT, TESTATM, OPTEST, TESTTNSUSD, MPAYVND, TESTBIDV1, OP_VPBMPGS3, TESTAWPOSTG, TESTTRAGOP1, TESTSAMSUNG, TESTOP_VTB2, TESTUPOSKBANK, TESTAPLBANK, TESTVCB_8211, TESTMERCHANTID20KYTU, MONPAYOUT, TESTVIETQR, TESTINVOICETG, OP_TESTVP8211, TESTPAYOUT2, TEST, OP_SACOMCBSU, OP_VPBMPGS1, TESTONEPAY20, TESTVJU, OP_TESTCONFIG, OP_TESTSS, OP_TESTBHX, OPTESTHB, TESTAWPOS, TESTACVNC, OP_TESTK4900, TESTONEPAYTHB, ONEPAY_REFUND, TESTAPLTG, OP_TESTK5814, TESTVCB_4722, OP_TESTK5722, TESTPAYOUT1, TESTOPVCB, TESTIDTEST4, TESTONEPAYDVC, OP_SACOMCBSV, OP_SACOMTRVU, ****************, OPMPAY, FALSE, TESTDUONG, OP_VPBMPGS2, TESTSACOMCBU, TESTLZD, TESTSAPO, OP_TESTAUTH, TESTMEGA1, OP_TESTMID, TESTVTASABRE, OP_TESTK4722, TESTOP_VTB3, OP_SACOMVIN, OP_TESTK8220, TESTCNY, TESTVCB_4900, OP_TESTK5698, TESTPCI3, ANHTVTEST, TESTTOKEN, TESTONEPAY2, MONPAYCOLLECT, OP_TESTVP5411, TESTSHOPIFYTG, TESTOP_D, TESTONEPAYDVTT, TESTTRAGOP2, TESTBIDVU3, TESTBIDVV2, TESTTOKENUSD, TESTMEGA, TEST3DS2_MK, TEST3DS2_TN, TESTVFVND, TESTVTB3D2, TESTENETVIET, TESTSHOPIFY, TESTAPPLEND, TESTVCBHM2, TESTTRAGOP3, TESTOKENOP, TESTKONEVA, TESTOP_VTB1, AUTOAPLND, TESTSSPAY, TESTSAMSUNGPL, TESTVCB_5732, TESTAES, OP_TESTK7832, TESTPCI2, TESTJBAUSD, TESTVCBCYBER, TESTMERCHANT15K, OPPREPAID, TESTONEPAYUSD, OP_MPAYVND, TESTSACOMCBV, OP_VCBVND, TESTSAPO2, TESTBIDVCBSU, TESTVCB3D2O, TESTMAFC, OP_TESTOP20, TESTAXA, TESTTGDD3D2, TESTPR, TESTHC1, ONEPAYHB, TESTSSBNPL, OP_TESTK8299, TESTHD2BEN, TESTPCIEM, TESTMCD, OP_TESTK5732, TESTPROMMG, OP_TESTTT, TESTAUTOMSP, OP_TESTUSD2, OP_TESTAPPAY, TESTTHB, TESTSOS, TESTAPPLETG, OP_TESTK5977, EKKO1, TESTONEPAY1, TESTIDTEST6, TESTONEPAYDVDT, TESTBIDVCSU2, ****************, OP_VCBUSD, TESTBIDVCBSV, OP_TEST3DS, OP_TEST3DS2V, OP_CYBSUSD, TESTTHSC, TESTBNPL, TESTAOSVNC, TESTPROM, OP_SACOMECOM, TESTQRTINH, TESTTOKENDV, TESTUPOS, OP_TESTK5969, OP_TESTK5816, TESTONEPAY3, TESTPCI1, TESTKBANKCYBER, TESTVPBCYBER, TEST_OP2B, OP_TESTVP4900, TESTIDTEST3, TESTPAYPAL]
2025-08-21 12:32:52.436 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-08-21 12:32:52.438 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-08-21 12:32:52.438 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start checkPerAndActionByUserId-----------
2025-08-21 12:32:52.479 [reactor-http-epoll-3] INFO  v.o.t.repository.AcquirerRepository - Starting getListAcquirer - calling function ONEDATA.GET_LIST_ACQUIRER
2025-08-21 12:32:52.487 [reactor-http-epoll-3] INFO  v.o.t.util.CheckPermissionUtil - ========================30
2025-08-21 12:36:51.374 [main] INFO  v.o.t.TransactionAppApplication - Starting TransactionAppApplication using Java 21.0.6 with PID 2200737 (/root/onepay/ma-transaction-backend/target/classes started by root in /root/onepay/ma-transaction-backend)
2025-08-21 12:36:51.376 [main] INFO  v.o.t.TransactionAppApplication - The following 1 profile is active: "dev"
2025-08-21 12:36:52.218 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data R2DBC repositories in DEFAULT mode.
2025-08-21 12:36:52.439 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 211 ms. Found 2 R2DBC repository interfaces.
2025-08-21 12:36:53.981 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Starting...
2025-08-21 12:36:54.270 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleMerchantPortalHikariPool - Added connection oracle.jdbc.driver.T4CConnection@21f50d2c
2025-08-21 12:36:54.271 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Start completed.
2025-08-21 12:36:54.334 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Starting...
2025-08-21 12:36:54.450 [main] INFO  com.zaxxer.hikari.pool.HikariPool - OracleOneReportHikariPool - Added connection oracle.jdbc.driver.T4CConnection@5a466dd
2025-08-21 12:36:54.450 [main] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Start completed.
2025-08-21 12:36:54.552 [main] INFO  v.o.t.job.PromotionReportJob - Scheduling PromotionReportJob with cron: 0 05 16 * * *
2025-08-21 12:36:54.560 [main] INFO  v.o.t.job.PromotionReportJob - End PromotionReportJob!
2025-08-21 12:36:54.562 [main] INFO  v.o.t.job.TransactionReportJob - Scheduling TransactionReportJob with cron: 0 00 16 * * *
2025-08-21 12:36:54.563 [main] INFO  v.o.t.job.TransactionReportJob - End TransactionReportJob!
2025-08-21 12:36:54.565 [main] INFO  v.o.transaction.job.UposReportJob - Scheduling UposReportJob with cron: 0 10 16 * * *
2025-08-21 12:36:54.566 [main] INFO  v.o.transaction.job.UposReportJob - End UposReportJob!
2025-08-21 12:36:55.513 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port 8397 (http)
2025-08-21 12:36:55.705 [main] INFO  v.o.t.TransactionAppApplication - Started TransactionAppApplication in 4.966 seconds (process running for 6.566)
2025-08-21 12:37:15.135 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Incoming request: GET http://localhost:8397/ma-service/api/v1/transactions/transaction/detail?transactionId=*********&transactionType=Authorize&paymentMethod=QT
2025-08-21 12:37:15.136 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept = application/json
2025-08-21 12:37:15.136 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Language = vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5
2025-08-21 12:37:15.136 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cache-Control = no-cache
2025-08-21 12:37:15.136 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Connection = keep-alive
2025-08-21 12:37:15.136 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Content-Type = application/json
2025-08-21 12:37:15.136 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Pragma = no-cache
2025-08-21 12:37:15.136 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Referer = https://dev5-ma.onepay.vn/mav2-main/
2025-08-21 12:37:15.136 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Dest = empty
2025-08-21 12:37:15.137 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Mode = cors
2025-08-21 12:37:15.137 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Sec-Fetch-Site = same-origin
2025-08-21 12:37:15.137 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: User-Agent = Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
2025-08-21 12:37:15.137 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua = "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-08-21 12:37:15.137 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-mobile = ?0
2025-08-21 12:37:15.137 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: sec-ch-ua-platform = "Linux"
2025-08-21 12:37:15.137 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-partner-id = 417604
2025-08-21 12:37:15.137 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Cookie = cf_clearance=vyQq5lsw5Jgug.4_5ApCH35c2EL_7F1BaViQ.jViwjI-1748404977-1.2.1.1-bN.uVwHybAdQ4x0rRGbjFMojklsfUWER0Ff93OhQNOs0hN7dqARHiVESeM40bcaDDVgMDPAsQsVrXAN7UMkVJETbey_pgvCwdYI0loGfjnMO8j5x40pBLZ84HnN.DY2NZP23nqKC0Y6716aqiG7qslN34PsdQmKLkhaEuXCT8OUAJhWM6Xc4o.7GfqDnTDQ1Pvv40KMaisaQXRAVE0jANMw3Uf1zqx27kXKz5OdfYI4gQn_EZ6Yv8c3IXoPvAvJcr_UDctq_BS.lKmolIAoS1NyH03K3zzA1yZTZy1y8F5Wo8VWqBwqfwcIEd376H9eg3.4F2QOmKU5hRYAByoyACkU8506l9c4UIh9WTGm2P7_S0o4mRYqKNaU3jEAVH6tr; _ga=GA1.2.249175694.1750644874; _ga_D5QNXXJGL1=GS2.2.s1750644874$o1$g0$t1750644874$j60$l0$h0; JSESSIONID=dummy; auth=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ1ODc1NjIsInVzZXIiOnsiZW1haWwiOiJhZG1pbkBvbmVwYXkudm4iLCJmdWxsX25hbWUiOiJBZG1pbiAxIiwiaWQiOiI1MUREN0M5QzdEQTZBMjc4OEY0QUI1M0I1NkJFNjgxMSIsImpvYl90aXRsZSI6IkFkbWluIGFsw7QyMjIiLCJwaG9uZSI6IjA5MDEyMzQ1NjEifX0.hEjfsrAg9gqUPMiXcjcugUJwwz7RVYeT8g3lnbyOc2Y
2025-08-21 12:37:15.137 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: x-user-id = 51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 12:37:15.137 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Postman-Token = e1ed4dd2-6eb4-47e1-b1f8-955d0b0c5f92
2025-08-21 12:37:15.137 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Host = localhost:8397
2025-08-21 12:37:15.137 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Header: Accept-Encoding = gzip, deflate, br
2025-08-21 12:37:15.140 [reactor-http-epoll-3] INFO  v.o.t.exception.PartnerIdFilter - Checking headers: x-partner-id and x-user-id...
2025-08-21 12:37:15.184 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: transactionId=********* ,paymentMethod=QT ,transactionType=Authorize ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 12:37:15.184 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionDetail MA-----------
2025-08-21 12:37:15.241 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/********* ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 12:37:15.319 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"transaction_id":"*********","original_transaction_id":"*********","transaction_type":"Authorize","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"17","acquirer_name":"","acquirer_short_name":""},"transaction_reference":"TEST_1755506112099348164310","amount":{"total":12000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"340353****0900","card_type":"Amex","name_on_card":"NGUYEN VAN A","card_date":{"month":"12","year":"28"},"commercical_card":"888","commercial_card_indicator":"3"},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y","authentication_type":"Amex Safekey","authorization_code":"013834"},"transaction_time":"2025-08-18T15:35:24Z","transaction_status":"0","transaction_ref_number":"*********","ip_address":"************","ip_proxy":"N","bin_country":"UNITED STATES","csc_result_code":"","verification_security_level":"","response_code":"0","can_void":true,"risk_assesment":"Review Required","bank_id":"AMERICAN EXPRESS US CONSUMER","wait_for_approval_amount":"0.00","advance_status":"Successful","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"CREDIT","acqResponseCode":"","status3ds":"Y","riskOverAllResult":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"NGUYEN VAN A","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00","installment_statement_date":"","reasonName":""}
2025-08-21 12:37:15.319 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-08-21 12:37:15.334 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-08-21 12:37:15.342 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-08-21 12:37:15.360 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-08-18T15:35:24, orderAmount=12000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=*********, parentTransactionId=*********, transactionType=Authorize, paymentMethod=QT, transactionCreatedTime=2025-08-18T15:35:24, transactionCompletedTime=2025-08-18T15:35:24, transactionAmount=12000.0, transactionCurrency=VND, transactionStatus=Successful, responseCode=0, merchantTransRef=TEST_1755506112099348164310, parentMerchantTransRef=null, paymentChannel=null, installmentStatus=, operator=null), paymentMethod=PaymentMethod(cardNumber=340353****0900, cardBrand=null, cardType=Amex, issuer=, acquirer=17, cardExpiry=12/28, nameOnCard=NGUYEN VAN A, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, settlementAmount=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=InstallmentDetail(priceDifference=0, period=, amountMonthly=0.00), promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=************, ipProxy=N, ipCountry=************, binCountry=UNITED STATES, riskAssessment=Review Required), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null, amountAdvance=null), actions=null, histories=null)
2025-08-21 12:37:15.361 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-08-21 12:37:15.361 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getAdvanceFeeInfo ===================
2025-08-21 12:37:15.538 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ==================== Start getMerchantById ====================
2025-08-21 12:37:15.548 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getParentMerchantTransRef ===================
2025-08-21 12:37:15.570 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getPromotionInfo ===================
2025-08-21 12:37:15.575 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call getTransactionHistory-----------
2025-08-21 12:37:15.575 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: originalTransactionId=********* ,paymentMethod=QT ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 12:37:15.575 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionHistory MA-----------
2025-08-21 12:37:15.576 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/*********/history ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 12:37:15.585 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION HISTORY API: response code=200 ,response body={"transactions":[{"transaction_id":"111803899","original_transaction_id":"*********","response_code":"","reference_number":"","merchant_transaction_ref":"VOID_A_TESTPCI_311b2241-8bdb-48","transaction_type":"Void Authorize","amount":{"total":12000.0,"currency":"VND"},"status":"","operator_id":"PSP-ONECREDIT","merchant_id":"TESTPCI","transaction_time":"2025-08-21T18:20:07Z","advance_status":"Pending","financial_transaction_id":"","note":"","parent_id":"*********","dadId":"*********","subHistories":""},{"transaction_id":"111803898","original_transaction_id":"*********","response_code":"","reference_number":"","merchant_transaction_ref":"VOID_A_TESTPCI_0a17da55-ea16-46","transaction_type":"Void Authorize","amount":{"total":12000.0,"currency":"VND"},"status":"","operator_id":"PSP-ONECREDIT","merchant_id":"TESTPCI","transaction_time":"2025-08-21T18:19:02Z","advance_status":"Pending","financial_transaction_id":"","note":"","parent_id":"*********","dadId":"*********","subHistories":""},{"transaction_id":"*********","original_transaction_id":"*********","response_code":"0","reference_number":"*********","merchant_transaction_ref":"TEST_1755506112099348164310","transaction_type":"Authorize","amount":{"total":12000.0,"currency":"VND"},"status":"0","operator_id":"","merchant_id":"TESTPCI","transaction_time":"2025-08-18T15:35:24Z","advance_status":"Successful","financial_transaction_id":"*********","note":"","parent_id":"*********","dadId":"","subHistories":""}]}
2025-08-21 12:37:15.585 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionHistory MA------------
2025-08-21 12:37:15.585 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION HISTORY INTERNATIONAL: {"transactions":[{"transaction_id":"111803899","original_transaction_id":"*********","response_code":"","reference_number":"","merchant_transaction_ref":"VOID_A_TESTPCI_311b2241-8bdb-48","transaction_type":"Void Authorize","amount":{"total":12000.0,"currency":"VND"},"status":"","operator_id":"PSP-ONECREDIT","merchant_id":"TESTPCI","transaction_time":"2025-08-21T18:20:07Z","advance_status":"Pending","financial_transaction_id":"","note":"","parent_id":"*********","dadId":"*********","subHistories":""},{"transaction_id":"111803898","original_transaction_id":"*********","response_code":"","reference_number":"","merchant_transaction_ref":"VOID_A_TESTPCI_0a17da55-ea16-46","transaction_type":"Void Authorize","amount":{"total":12000.0,"currency":"VND"},"status":"","operator_id":"PSP-ONECREDIT","merchant_id":"TESTPCI","transaction_time":"2025-08-21T18:19:02Z","advance_status":"Pending","financial_transaction_id":"","note":"","parent_id":"*********","dadId":"*********","subHistories":""},{"transaction_id":"*********","original_transaction_id":"*********","response_code":"0","reference_number":"*********","merchant_transaction_ref":"TEST_1755506112099348164310","transaction_type":"Authorize","amount":{"total":12000.0,"currency":"VND"},"status":"0","operator_id":"","merchant_id":"TESTPCI","transaction_time":"2025-08-18T15:35:24Z","advance_status":"Successful","financial_transaction_id":"*********","note":"","parent_id":"*********","dadId":"","subHistories":""}]}
2025-08-21 12:37:15.590 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call getTransactionHistory------------
2025-08-21 12:37:15.590 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-08-21 12:37:15.590 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-08-21 12:37:15.787 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Input merchantId param: TESTPCI
2025-08-21 12:37:15.787 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - list merchant from permission: [OP_TESTVND, OP_VND01, OP_MPGSVND, OP_MPGSUSD, OP_MPAYVND3D, TESTBIDVV3, ONEPAYHM, D_TEST, OP_CYBSVND, TEST3DSMPGS, OP_MPAYVN1, TESTND, OP_TESTK7011, TESTHD3BEN, TESTSTATICQR, OP_TESTK4121, OP_SACOMTH, TESTAPLVNC, TESTORIFLCBS, TESTVCB_5411, OP_TESTK7399, TESTGGPAY, OP_TESTK5331, TESTKOVENA, TESTSTGINVOICE, TESTMERCHANT16KY, TESTOPVPB, TESTVTBCYBER, HUONG1, TESTOPBIDV, TESTTRAGOP, TESTPCI, TESTONEPAY, OP_TESTUSD, OP_SACOMTRVV, ONEPAY, INVOICETG, TESTDMX, TESTTNS, TESTINVOICE, TESTVCB3D2, TESTBIDV, TESTHC, TEST3DS2_LT, MIGS3DS, TESTAXA1, TESTVF, TESTUPOSTG, TESTAOSVNBANK, OP_TESTKBANK, TESTAPPLE, TESTVCBHM1, TESTTVLOKA, TESTVCB_7399, TESTVAGROUP, TESTVCB_6300, OP_TESTM8211, OP_TESTK7372, OP_TESTK7991, TESTPCI4, TESTOPVCB2, TESTSACOMCYBER, TESTUSD, OP_TESTVP5732, OP_TESTVP6300, ONEPAYMT, TESTATM, OPTEST, TESTTNSUSD, MPAYVND, TESTBIDV1, OP_VPBMPGS3, TESTAWPOSTG, TESTTRAGOP1, TESTSAMSUNG, TESTOP_VTB2, TESTUPOSKBANK, TESTAPLBANK, TESTVCB_8211, TESTMERCHANTID20KYTU, MONPAYOUT, TESTVIETQR, TESTINVOICETG, OP_TESTVP8211, TESTPAYOUT2, TEST, OP_SACOMCBSU, OP_VPBMPGS1, TESTONEPAY20, TESTVJU, OP_TESTCONFIG, OP_TESTSS, OP_TESTBHX, OPTESTHB, TESTAWPOS, TESTACVNC, OP_TESTK4900, TESTONEPAYTHB, ONEPAY_REFUND, TESTAPLTG, OP_TESTK5814, TESTVCB_4722, OP_TESTK5722, TESTPAYOUT1, TESTOPVCB, TESTIDTEST4, TESTONEPAYDVC, OP_SACOMCBSV, OP_SACOMTRVU, ****************, OPMPAY, FALSE, TESTDUONG, OP_VPBMPGS2, TESTSACOMCBU, TESTLZD, TESTSAPO, OP_TESTAUTH, TESTMEGA1, OP_TESTMID, TESTVTASABRE, OP_TESTK4722, TESTOP_VTB3, OP_SACOMVIN, OP_TESTK8220, TESTCNY, TESTVCB_4900, OP_TESTK5698, TESTPCI3, ANHTVTEST, TESTTOKEN, TESTONEPAY2, MONPAYCOLLECT, OP_TESTVP5411, TESTSHOPIFYTG, TESTOP_D, TESTONEPAYDVTT, TESTTRAGOP2, TESTBIDVU3, TESTBIDVV2, TESTTOKENUSD, TESTMEGA, TEST3DS2_MK, TEST3DS2_TN, TESTVFVND, TESTVTB3D2, TESTENETVIET, TESTSHOPIFY, TESTAPPLEND, TESTVCBHM2, TESTTRAGOP3, TESTOKENOP, TESTKONEVA, TESTOP_VTB1, AUTOAPLND, TESTSSPAY, TESTSAMSUNGPL, TESTVCB_5732, TESTAES, OP_TESTK7832, TESTPCI2, TESTJBAUSD, TESTVCBCYBER, TESTMERCHANT15K, OPPREPAID, TESTONEPAYUSD, OP_MPAYVND, TESTSACOMCBV, OP_VCBVND, TESTSAPO2, TESTBIDVCBSU, TESTVCB3D2O, TESTMAFC, OP_TESTOP20, TESTAXA, TESTTGDD3D2, TESTPR, TESTHC1, ONEPAYHB, TESTSSBNPL, OP_TESTK8299, TESTHD2BEN, TESTPCIEM, TESTMCD, OP_TESTK5732, TESTPROMMG, OP_TESTTT, TESTAUTOMSP, OP_TESTUSD2, OP_TESTAPPAY, TESTTHB, TESTSOS, TESTAPPLETG, OP_TESTK5977, EKKO1, TESTONEPAY1, TESTIDTEST6, TESTONEPAYDVDT, TESTBIDVCSU2, ****************, OP_VCBUSD, TESTBIDVCBSV, OP_TEST3DS, OP_TEST3DS2V, OP_CYBSUSD, TESTTHSC, TESTBNPL, TESTAOSVNC, TESTPROM, OP_SACOMECOM, TESTQRTINH, TESTTOKENDV, TESTUPOS, OP_TESTK5969, OP_TESTK5816, TESTONEPAY3, TESTPCI1, TESTKBANKCYBER, TESTVPBCYBER, TEST_OP2B, OP_TESTVP4900, TESTIDTEST3, TESTPAYPAL]
2025-08-21 12:37:15.789 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Parsed merchantId input list: [TESTPCI]
2025-08-21 12:37:15.791 [reactor-http-epoll-3] INFO  v.o.t.service.MerchantService - Filtered merchantId list after matching: [TESTPCI]
2025-08-21 12:37:15.791 [reactor-http-epoll-3] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start checkPerAndActionByUserId-----------
2025-08-21 12:37:15.832 [reactor-http-epoll-3] INFO  v.o.t.repository.AcquirerRepository - Starting getListAcquirer - calling function ONEDATA.GET_LIST_ACQUIRER
2025-08-21 12:37:18.905 [reactor-http-epoll-3] INFO  v.o.t.util.CheckPermissionUtil - checkActionRefund paymentMethod invalid, transactionId=*********, transactionType=Authorize
2025-08-21 12:37:25.426 [reactor-http-epoll-3] INFO  v.o.t.util.CheckPermissionUtil - checkApproveAndRejectRefund transactionStatus invalid, transactionId=*********, transactionStatus=Successful
2025-08-21 12:37:25.504 [reactor-http-epoll-3] INFO  v.o.t.exception.RequestLoggingFilter - Response: GET http://localhost:8397/ma-service/api/v1/transactions/transaction/detail?transactionId=*********&transactionType=Authorize&paymentMethod=QT - Status: 200 OK
2025-08-21 16:03:36.070 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.k.l.KafkaMessageListenerContainer - download-task-group: Consumer stopped
2025-08-21 16:03:36.070 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.k.l.KafkaMessageListenerContainer - download-task-group: Consumer stopped
2025-08-21 16:03:36.072 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.netty.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-21 16:03:36.076 [netty-shutdown] INFO  o.s.b.w.e.netty.GracefulShutdown - Graceful shutdown complete
2025-08-21 16:03:38.100 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Shutdown initiated...
2025-08-21 16:03:38.115 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Shutdown completed.
2025-08-21 16:03:38.115 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Shutdown initiated...
2025-08-21 16:03:38.199 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Shutdown completed.
2025-08-21 12:04:47.818 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] WARN  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Connection to node -1 (localhost/127.0.0.1:9092) could not be established. Node may not be available.
2025-08-21 12:04:48.086 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.apache.kafka.clients.NetworkClient - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Node -1 disconnected.
2025-08-21 16:03:38.671 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-08-21 16:03:38.672 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Request joining group due to: consumer pro-actively leaving the group
2025-08-21 16:03:38.672 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.LegacyKafkaConsumer - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Unsubscribed all topics or patterns and assigned partitions
2025-08-21 16:03:38.674 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-08-21 16:03:38.674 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [Consumer clientId=consumer-download-task-group-1, groupId=download-task-group] Request joining group due to: consumer pro-actively leaving the group
2025-08-21 16:03:38.676 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-08-21 16:03:38.676 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Request joining group due to: consumer pro-actively leaving the group
2025-08-21 16:03:38.676 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.c.i.LegacyKafkaConsumer - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Unsubscribed all topics or patterns and assigned partitions
2025-08-21 16:03:38.676 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Resetting generation and member id due to: consumer pro-actively leaving the group
2025-08-21 16:03:38.676 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.k.c.c.i.ConsumerCoordinator - [Consumer clientId=consumer-download-task-group-2, groupId=download-task-group] Request joining group due to: consumer pro-actively leaving the group
2025-08-21 16:03:38.679 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.kafka.common.metrics.Metrics - Metrics scheduler closed
2025-08-21 16:03:38.679 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.kafka.common.metrics.Metrics - Metrics scheduler closed
2025-08-21 16:03:38.679 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.kafka.common.metrics.Metrics - Closing reporter org.apache.kafka.common.metrics.JmxReporter
2025-08-21 16:03:38.679 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.kafka.common.metrics.Metrics - Closing reporter org.apache.kafka.common.metrics.JmxReporter
2025-08-21 16:03:38.679 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.kafka.common.metrics.Metrics - Closing reporter org.apache.kafka.common.telemetry.internals.ClientTelemetryReporter
2025-08-21 16:03:38.679 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.kafka.common.metrics.Metrics - Closing reporter org.apache.kafka.common.telemetry.internals.ClientTelemetryReporter
2025-08-21 16:03:38.679 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.kafka.common.metrics.Metrics - Metrics reporters closed
2025-08-21 16:03:38.680 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.kafka.common.metrics.Metrics - Metrics reporters closed
2025-08-21 16:03:38.685 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.a.kafka.common.utils.AppInfoParser - App info kafka.consumer for consumer-download-task-group-1 unregistered
2025-08-21 16:03:38.685 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.a.kafka.common.utils.AppInfoParser - App info kafka.consumer for consumer-download-task-group-2 unregistered
2025-08-21 16:03:38.685 [org.springframework.kafka.KafkaListenerEndpointContainer#0-0-C-1] INFO  o.s.k.l.KafkaMessageListenerContainer - download-task-group: Consumer stopped
2025-08-21 16:03:38.686 [org.springframework.kafka.KafkaListenerEndpointContainer#0-1-C-1] INFO  o.s.k.l.KafkaMessageListenerContainer - download-task-group: Consumer stopped
2025-08-21 16:03:38.688 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.netty.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-21 16:03:38.702 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION DETAIL API: response code=200 ,response body={"transaction_id":"*********","original_transaction_id":"*********","transaction_type":"Authorize","order_info":"Ma Don Hang","acquirer":{"acquirer_id":"17","acquirer_name":"","acquirer_short_name":""},"transaction_reference":"TEST_1755506112099348164310","amount":{"total":12000.0,"currency":"VND","refund_total":0.0,"refund_capture_total":0.0},"card":{"card_number":"340353****0900","card_type":"Amex","name_on_card":"NGUYEN VAN A","card_date":{"month":"12","year":"28"},"commercical_card":"888","commercial_card_indicator":"3"},"merchant_id":"TESTPCI","authentication":{"authentication_state":"Y","authentication_type":"Amex Safekey","authorization_code":"013834"},"transaction_time":"2025-08-18T15:35:24Z","transaction_status":"0","transaction_ref_number":"*********","ip_address":"************","ip_proxy":"N","bin_country":"UNITED STATES","csc_result_code":"","verification_security_level":"","response_code":"0","can_void":true,"risk_assesment":"Review Required","bank_id":"AMERICAN EXPRESS US CONSUMER","wait_for_approval_amount":"0.00","advance_status":"Successful","source":"Direct","networkTransId":"","tokenNumber":"","deviceId":"","tokenExpiry":"","type":"CREDIT","acqResponseCode":"","status3ds":"Y","riskOverAllResult":"","cardLevelIndicator":"","customer_mobile":"*****4321","customer_email":"t*****<EMAIL>","fraud_check":"","customer_address":"","customer_name":"Test User","installment_bank":"","installment_status":"","installment_time":"","installment_cus_phone":"**********","installment_cus_email":"<EMAIL>","installment_cancel_days":"0","installment_monthly_amount":"0.00","installment_fee":"0.00","card_holder_name":"NGUYEN VAN A","qlNote":"","qlCurrency":"","qlAmount":"0.00","qlExchangeRate":"0.00","installment_statement_date":"","reasonName":""}
2025-08-21 16:03:38.703 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionDetail MA------------
2025-08-21 16:03:38.704 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - Start mapJsonPurchaseIntenational...
2025-08-21 16:03:38.705 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - End mapJsonPurchaseIntenational...
2025-08-21 16:03:38.705 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE PURCHASE INTERNATIONAL: TransactionDetailResponse(orderInformation=OrderInformation(orderReference=Ma Don Hang, orderCreatedTime=2025-08-18T15:35:24, orderAmount=12000.0, orderCurrency=VND, orderSource=null), merchantInformation=MerchantInformation(merchantId=TESTPCI, merchantName=null), transactionInformation=TransactionInformation(transactionId=*********, parentTransactionId=*********, transactionType=Authorize, paymentMethod=QT, transactionCreatedTime=2025-08-18T15:35:24, transactionCompletedTime=2025-08-18T15:35:24, transactionAmount=12000.0, transactionCurrency=VND, transactionStatus=Successful, responseCode=0, merchantTransRef=TEST_1755506112099348164310, parentMerchantTransRef=null, paymentChannel=null, installmentStatus=, operator=null), paymentMethod=PaymentMethod(cardNumber=340353****0900, cardBrand=null, cardType=Amex, issuer=, acquirer=17, cardExpiry=12/28, nameOnCard=NGUYEN VAN A, commercialCard=null, commercialCardIndicator=null, cscResultCode=null, dialectCscResultCode=null, authorizationCode=null, appName=null, cardName=null, qrId=null, bankTranRef=null, provider=null, settlementAmount=null, model=null, period=null, firstPaymentAmount=null, payLaterAmount=null), installmentDetail=InstallmentDetail(priceDifference=0, period=, amountMonthly=0.00), promotionInformation=PromotionInformation(promotionCode=null, promotionName=null, discountAmount=null, discountCurrency=), riskManagement=RiskManagement(ipAddress=************, ipProxy=N, ipCountry=************, binCountry=UNITED STATES, riskAssessment=Review Required), feeInformation=FeeInformation(transactionProcessingFee=null, cardPaymentFee=null, itaFee=null), advanceInformation=AdvanceInformation(pvNo=null, contractNo=null, transferDate=null, status=null, amountAdvance=null), actions=null, histories=null)
2025-08-21 16:03:38.706 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------end getTransactionDetail-----------
2025-08-21 16:03:38.706 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getAdvanceFeeInfo ===================
2025-08-21 16:03:38.711 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ==================== Start getMerchantById ====================
2025-08-21 16:03:38.715 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getParentMerchantTransRef ===================
2025-08-21 16:03:38.718 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - =============== Start getPromotionInfo ===================
2025-08-21 16:03:38.722 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call getTransactionHistory-----------
2025-08-21 16:03:38.722 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: originalTransactionId=********* ,paymentMethod=QT ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 16:03:38.722 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ---------------start call api getTransactionHistory MA-----------
2025-08-21 16:03:38.723 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: url=http://localhost:8238/ma-international/api/v1/international/transaction/*********/history ,xUserId=51DD7C9C7DA6A2788F4AB53B56BE6811
2025-08-21 16:03:38.733 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION HISTORY API: response code=200 ,response body={"transactions":[{"transaction_id":"111803899","original_transaction_id":"*********","response_code":"","reference_number":"","merchant_transaction_ref":"VOID_A_TESTPCI_311b2241-8bdb-48","transaction_type":"Void Authorize","amount":{"total":12000.0,"currency":"VND"},"status":"","operator_id":"PSP-ONECREDIT","merchant_id":"TESTPCI","transaction_time":"2025-08-21T18:20:07Z","advance_status":"Pending","financial_transaction_id":"","note":"","parent_id":"*********","dadId":"*********","subHistories":""},{"transaction_id":"111803898","original_transaction_id":"*********","response_code":"","reference_number":"","merchant_transaction_ref":"VOID_A_TESTPCI_0a17da55-ea16-46","transaction_type":"Void Authorize","amount":{"total":12000.0,"currency":"VND"},"status":"","operator_id":"PSP-ONECREDIT","merchant_id":"TESTPCI","transaction_time":"2025-08-21T18:19:02Z","advance_status":"Pending","financial_transaction_id":"","note":"","parent_id":"*********","dadId":"*********","subHistories":""},{"transaction_id":"*********","original_transaction_id":"*********","response_code":"0","reference_number":"*********","merchant_transaction_ref":"TEST_1755506112099348164310","transaction_type":"Authorize","amount":{"total":12000.0,"currency":"VND"},"status":"0","operator_id":"","merchant_id":"TESTPCI","transaction_time":"2025-08-18T15:35:24Z","advance_status":"Successful","financial_transaction_id":"*********","note":"","parent_id":"*********","dadId":"","subHistories":""}]}
2025-08-21 16:03:38.733 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call api getTransactionHistory MA------------
2025-08-21 16:03:38.734 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - RESPONSE TRANSACTION HISTORY INTERNATIONAL: {"transactions":[{"transaction_id":"111803899","original_transaction_id":"*********","response_code":"","reference_number":"","merchant_transaction_ref":"VOID_A_TESTPCI_311b2241-8bdb-48","transaction_type":"Void Authorize","amount":{"total":12000.0,"currency":"VND"},"status":"","operator_id":"PSP-ONECREDIT","merchant_id":"TESTPCI","transaction_time":"2025-08-21T18:20:07Z","advance_status":"Pending","financial_transaction_id":"","note":"","parent_id":"*********","dadId":"*********","subHistories":""},{"transaction_id":"111803898","original_transaction_id":"*********","response_code":"","reference_number":"","merchant_transaction_ref":"VOID_A_TESTPCI_0a17da55-ea16-46","transaction_type":"Void Authorize","amount":{"total":12000.0,"currency":"VND"},"status":"","operator_id":"PSP-ONECREDIT","merchant_id":"TESTPCI","transaction_time":"2025-08-21T18:19:02Z","advance_status":"Pending","financial_transaction_id":"","note":"","parent_id":"*********","dadId":"*********","subHistories":""},{"transaction_id":"*********","original_transaction_id":"*********","response_code":"0","reference_number":"*********","merchant_transaction_ref":"TEST_1755506112099348164310","transaction_type":"Authorize","amount":{"total":12000.0,"currency":"VND"},"status":"0","operator_id":"","merchant_id":"TESTPCI","transaction_time":"2025-08-18T15:35:24Z","advance_status":"Successful","financial_transaction_id":"*********","note":"","parent_id":"*********","dadId":"","subHistories":""}]}
2025-08-21 16:03:38.735 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------end call getTransactionHistory------------
2025-08-21 16:03:38.735 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - ------------strart checkMerchantIdByUserId------------
2025-08-21 16:03:38.735 [reactor-http-epoll-4] INFO  v.o.t.s.i.TransactionOldServiceImpl - REQUEST: userid=51DD7C9C7DA6A2788F4AB53B56BE6811 ,merchantTransactionDetail=TESTPCI ,paymentMethod=QT
2025-08-21 16:03:38.740 [netty-shutdown] INFO  o.s.b.w.e.netty.GracefulShutdown - Graceful shutdown complete
2025-08-21 16:03:38.753 [reactor-http-epoll-4] WARN  r.n.http.client.HttpClientConnect - [bdf53b2b-1, L:/127.0.0.1:50390 ! R:localhost/127.0.0.1:8482] The connection observed an error
reactor.netty.http.client.PrematureCloseException: Connection prematurely closed BEFORE response
2025-08-21 16:03:38.764 [reactor-http-epoll-4] WARN  o.s.w.r.r.m.a.RequestMappingHandlerAdapter - [ae644102-2] Failure in @ExceptionHandler vn.onepay.transaction.exception.GlobalExceptionHandler#handleRuntimeException(RuntimeException)
org.springframework.web.server.ResponseStatusException: 500 INTERNAL_SERVER_ERROR "Internal Server Error"
	at vn.onepay.transaction.exception.GlobalExceptionHandler.handleRuntimeException(GlobalExceptionHandler.java:29)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.reactive.result.method.InvocableHandlerMethod.lambda$invoke$0(InvocableHandlerMethod.java:208)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)
	at reactor.core.publisher.MonoZip$ZipCoordinator.signal(MonoZip.java:297)
	at reactor.core.publisher.MonoZip$ZipInner.onNext(MonoZip.java:478)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)
	at reactor.core.publisher.MonoZip$ZipInner.onSubscribe(MonoZip.java:470)
	at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)
	at reactor.core.publisher.MonoZip$ZipCoordinator.request(MonoZip.java:220)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onSubscribe(FluxOnErrorResume.java:74)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onSubscribe(MonoFlatMap.java:117)
	at reactor.core.publisher.MonoZip.subscribe(MonoZip.java:129)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onError(FluxOnAssembly.java:544)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onError(MonoFlatMap.java:180)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onError(FluxContextWrite.java:121)
	at reactor.core.publisher.FluxDoFinally$DoFinallySubscriber.onError(FluxDoFinally.java:119)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onError(MonoPeekTerminal.java:258)
	at reactor.core.publisher.FluxPeekFuseable$PeekConditionalSubscriber.onError(FluxPeekFuseable.java:903)
	at reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onError(Operators.java:2236)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onError(FluxOnAssembly.java:544)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
	at reactor.core.publisher.FluxMap$MapSubscriber.onError(FluxMap.java:134)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:106)
	at reactor.core.publisher.Operators.error(Operators.java:198)
	at reactor.core.publisher.MonoErrorSupplied.subscribe(MonoErrorSupplied.java:56)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
	at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
	at reactor.core.publisher.MonoNext$NextSubscriber.onError(MonoNext.java:93)
	at reactor.core.publisher.MonoFlatMapMany$FlatMapManyMain.onError(MonoFlatMapMany.java:205)
	at reactor.core.publisher.SerializedSubscriber.onError(SerializedSubscriber.java:124)
	at reactor.core.publisher.FluxRetryWhen$RetryWhenMainSubscriber.whenError(FluxRetryWhen.java:229)
	at reactor.core.publisher.FluxRetryWhen$RetryWhenOtherSubscriber.onError(FluxRetryWhen.java:279)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onError(FluxContextWrite.java:121)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.maybeOnError(FluxConcatMapNoPrefetch.java:327)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onNext(FluxConcatMapNoPrefetch.java:212)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
	at reactor.core.publisher.SinkManyEmitterProcessor.drain(SinkManyEmitterProcessor.java:476)
	at reactor.core.publisher.SinkManyEmitterProcessor$EmitterInner.drainParent(SinkManyEmitterProcessor.java:620)
	at reactor.core.publisher.FluxPublish$PubSubInner.request(FluxPublish.java:874)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.request(FluxContextWrite.java:136)
	at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:337)
	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.request(FluxContextWrite.java:136)
	at reactor.core.publisher.Operators$DeferredSubscription.request(Operators.java:1743)
	at reactor.core.publisher.FluxRetryWhen$RetryWhenMainSubscriber.onError(FluxRetryWhen.java:196)
	at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
	at reactor.netty.http.client.HttpClientConnect$HttpObserver.onUncaughtException(HttpClientConnect.java:415)
	at reactor.netty.ReactorNetty$CompositeConnectionObserver.onUncaughtException(ReactorNetty.java:709)
	at reactor.netty.resources.DefaultPooledConnectionProvider$DisposableAcquire.onUncaughtException(DefaultPooledConnectionProvider.java:225)
	at reactor.netty.resources.DefaultPooledConnectionProvider$PooledConnection.onUncaughtException(DefaultPooledConnectionProvider.java:478)
	at reactor.netty.http.client.HttpClientOperations.onInboundClose(HttpClientOperations.java:346)
	at reactor.netty.channel.ChannelOperationsHandler.channelInactive(ChannelOperationsHandler.java:73)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:303)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:281)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:274)
	at io.netty.channel.ChannelInboundHandlerAdapter.channelInactive(ChannelInboundHandlerAdapter.java:81)
	at io.netty.handler.codec.http.HttpContentDecoder.channelInactive(HttpContentDecoder.java:235)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:303)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:281)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:274)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelInactive(CombinedChannelDuplexHandler.java:418)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:412)
	at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:377)
	at io.netty.handler.codec.http.HttpClientCodec$Decoder.channelInactive(HttpClientCodec.java:410)
	at io.netty.channel.CombinedChannelDuplexHandler.channelInactive(CombinedChannelDuplexHandler.java:221)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:303)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:281)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:274)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelInactive(DefaultChannelPipeline.java:1352)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:301)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:281)
	at io.netty.channel.DefaultChannelPipeline.fireChannelInactive(DefaultChannelPipeline.java:850)
	at io.netty.channel.AbstractChannel$AbstractUnsafe$7.run(AbstractChannel.java:811)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasksFrom(SingleThreadEventExecutor.java:428)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:377)
	at io.netty.util.concurrent.SingleThreadEventExecutor.confirmShutdown(SingleThreadEventExecutor.java:763)
	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:423)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-21 16:03:38.765 [reactor-http-epoll-4] ERROR v.o.t.exception.RequestLoggingFilter - Request error: GET http://localhost:8396/ma-service/api/v1/transactions/transaction/detail?transactionId=*********&transactionType=Authorize&paymentMethod=QT - Exception: Connection prematurely closed BEFORE response
org.springframework.web.reactive.function.client.WebClientRequestException: Connection prematurely closed BEFORE response
	at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Request to GET http://localhost:8482/permission/api/v1/partner/417604/user/51DD7C9C7DA6A2788F4AB53B56BE6811/merchants [DefaultWebClient]
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#getTransactionDetail(ServerWebExchange, String, String, String, String) [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x00007febb86068e8 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
Original Stack Trace:
		at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
		at reactor.core.publisher.MonoErrorSupplied.subscribe(MonoErrorSupplied.java:55)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.MonoNext$NextSubscriber.onError(MonoNext.java:93)
		at reactor.core.publisher.MonoFlatMapMany$FlatMapManyMain.onError(MonoFlatMapMany.java:205)
		at reactor.core.publisher.SerializedSubscriber.onError(SerializedSubscriber.java:124)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenMainSubscriber.whenError(FluxRetryWhen.java:229)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenOtherSubscriber.onError(FluxRetryWhen.java:279)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onError(FluxContextWrite.java:121)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.maybeOnError(FluxConcatMapNoPrefetch.java:327)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onNext(FluxConcatMapNoPrefetch.java:212)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.SinkManyEmitterProcessor.drain(SinkManyEmitterProcessor.java:476)
		at reactor.core.publisher.SinkManyEmitterProcessor$EmitterInner.drainParent(SinkManyEmitterProcessor.java:620)
		at reactor.core.publisher.FluxPublish$PubSubInner.request(FluxPublish.java:874)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.request(FluxContextWrite.java:136)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:337)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.request(FluxContextWrite.java:136)
		at reactor.core.publisher.Operators$DeferredSubscription.request(Operators.java:1743)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenMainSubscriber.onError(FluxRetryWhen.java:196)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.http.client.HttpClientConnect$HttpObserver.onUncaughtException(HttpClientConnect.java:415)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onUncaughtException(ReactorNetty.java:709)
		at reactor.netty.resources.DefaultPooledConnectionProvider$DisposableAcquire.onUncaughtException(DefaultPooledConnectionProvider.java:225)
		at reactor.netty.resources.DefaultPooledConnectionProvider$PooledConnection.onUncaughtException(DefaultPooledConnectionProvider.java:478)
		at reactor.netty.http.client.HttpClientOperations.onInboundClose(HttpClientOperations.java:346)
		at reactor.netty.channel.ChannelOperationsHandler.channelInactive(ChannelOperationsHandler.java:73)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:303)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:281)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:274)
		at io.netty.channel.ChannelInboundHandlerAdapter.channelInactive(ChannelInboundHandlerAdapter.java:81)
		at io.netty.handler.codec.http.HttpContentDecoder.channelInactive(HttpContentDecoder.java:235)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:303)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:281)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:274)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelInactive(CombinedChannelDuplexHandler.java:418)
		at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:412)
		at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:377)
		at io.netty.handler.codec.http.HttpClientCodec$Decoder.channelInactive(HttpClientCodec.java:410)
		at io.netty.channel.CombinedChannelDuplexHandler.channelInactive(CombinedChannelDuplexHandler.java:221)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:303)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:281)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:274)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelInactive(DefaultChannelPipeline.java:1352)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:301)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:281)
		at io.netty.channel.DefaultChannelPipeline.fireChannelInactive(DefaultChannelPipeline.java:850)
		at io.netty.channel.AbstractChannel$AbstractUnsafe$7.run(AbstractChannel.java:811)
		at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:166)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasksFrom(SingleThreadEventExecutor.java:428)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:377)
		at io.netty.util.concurrent.SingleThreadEventExecutor.confirmShutdown(SingleThreadEventExecutor.java:763)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:423)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: reactor.netty.http.client.PrematureCloseException: Connection prematurely closed BEFORE response
2025-08-21 16:03:38.767 [reactor-http-epoll-4] ERROR o.s.b.a.w.r.e.AbstractErrorWebExceptionHandler - [ae644102-2]  500 Server Error for HTTP GET "/transaction/detail?transactionId=*********&transactionType=Authorize&paymentMethod=QT"
org.springframework.web.reactive.function.client.WebClientRequestException: Connection prematurely closed BEFORE response
	at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Request to GET http://localhost:8482/permission/api/v1/partner/417604/user/51DD7C9C7DA6A2788F4AB53B56BE6811/merchants [DefaultWebClient]
	*__checkpoint ⇢ Handler vn.onepay.transaction.controller.TransactionController#getTransactionDetail(ServerWebExchange, String, String, String, String) [DispatcherHandler]
	*__checkpoint ⇢ vn.onepay.transaction.config.WebCorsConfig$$Lambda/0x00007febb86068e8 [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.PartnerIdFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ vn.onepay.transaction.exception.RequestLoggingFilter [DefaultWebFilterChain]
	*__checkpoint ⇢ HTTP GET "/ma-service/api/v1/transactions/transaction/detail?transactionId=*********&transactionType=Authorize&paymentMethod=QT" [ExceptionHandlingWebHandler]
Original Stack Trace:
		at org.springframework.web.reactive.function.client.ExchangeFunctions$DefaultExchangeFunction.lambda$wrapException$9(ExchangeFunctions.java:137)
		at reactor.core.publisher.MonoErrorSupplied.subscribe(MonoErrorSupplied.java:55)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4576)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onError(FluxOnErrorResume.java:103)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.FluxPeek$PeekSubscriber.onError(FluxPeek.java:222)
		at reactor.core.publisher.MonoNext$NextSubscriber.onError(MonoNext.java:93)
		at reactor.core.publisher.MonoFlatMapMany$FlatMapManyMain.onError(MonoFlatMapMany.java:205)
		at reactor.core.publisher.SerializedSubscriber.onError(SerializedSubscriber.java:124)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenMainSubscriber.whenError(FluxRetryWhen.java:229)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenOtherSubscriber.onError(FluxRetryWhen.java:279)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onError(FluxContextWrite.java:121)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.maybeOnError(FluxConcatMapNoPrefetch.java:327)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onNext(FluxConcatMapNoPrefetch.java:212)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)
		at reactor.core.publisher.SinkManyEmitterProcessor.drain(SinkManyEmitterProcessor.java:476)
		at reactor.core.publisher.SinkManyEmitterProcessor$EmitterInner.drainParent(SinkManyEmitterProcessor.java:620)
		at reactor.core.publisher.FluxPublish$PubSubInner.request(FluxPublish.java:874)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.request(FluxContextWrite.java:136)
		at reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:337)
		at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.request(FluxContextWrite.java:136)
		at reactor.core.publisher.Operators$DeferredSubscription.request(Operators.java:1743)
		at reactor.core.publisher.FluxRetryWhen$RetryWhenMainSubscriber.onError(FluxRetryWhen.java:196)
		at reactor.core.publisher.MonoCreate$DefaultMonoSink.error(MonoCreate.java:205)
		at reactor.netty.http.client.HttpClientConnect$HttpObserver.onUncaughtException(HttpClientConnect.java:415)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onUncaughtException(ReactorNetty.java:709)
		at reactor.netty.resources.DefaultPooledConnectionProvider$DisposableAcquire.onUncaughtException(DefaultPooledConnectionProvider.java:225)
		at reactor.netty.resources.DefaultPooledConnectionProvider$PooledConnection.onUncaughtException(DefaultPooledConnectionProvider.java:478)
		at reactor.netty.http.client.HttpClientOperations.onInboundClose(HttpClientOperations.java:346)
		at reactor.netty.channel.ChannelOperationsHandler.channelInactive(ChannelOperationsHandler.java:73)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:303)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:281)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:274)
		at io.netty.channel.ChannelInboundHandlerAdapter.channelInactive(ChannelInboundHandlerAdapter.java:81)
		at io.netty.handler.codec.http.HttpContentDecoder.channelInactive(HttpContentDecoder.java:235)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:303)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:281)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:274)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelInactive(CombinedChannelDuplexHandler.java:418)
		at io.netty.handler.codec.ByteToMessageDecoder.channelInputClosed(ByteToMessageDecoder.java:412)
		at io.netty.handler.codec.ByteToMessageDecoder.channelInactive(ByteToMessageDecoder.java:377)
		at io.netty.handler.codec.http.HttpClientCodec$Decoder.channelInactive(HttpClientCodec.java:410)
		at io.netty.channel.CombinedChannelDuplexHandler.channelInactive(CombinedChannelDuplexHandler.java:221)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:303)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:281)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelInactive(AbstractChannelHandlerContext.java:274)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelInactive(DefaultChannelPipeline.java:1352)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:301)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelInactive(AbstractChannelHandlerContext.java:281)
		at io.netty.channel.DefaultChannelPipeline.fireChannelInactive(DefaultChannelPipeline.java:850)
		at io.netty.channel.AbstractChannel$AbstractUnsafe$7.run(AbstractChannel.java:811)
		at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
		at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:166)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasksFrom(SingleThreadEventExecutor.java:428)
		at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:377)
		at io.netty.util.concurrent.SingleThreadEventExecutor.confirmShutdown(SingleThreadEventExecutor.java:763)
		at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:423)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: reactor.netty.http.client.PrematureCloseException: Connection prematurely closed BEFORE response
2025-08-21 16:03:40.778 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Shutdown initiated...
2025-08-21 16:03:40.790 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - OracleOneReportHikariPool - Shutdown completed.
2025-08-21 16:03:40.790 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Shutdown initiated...
2025-08-21 16:03:40.871 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - OracleMerchantPortalHikariPool - Shutdown completed.
